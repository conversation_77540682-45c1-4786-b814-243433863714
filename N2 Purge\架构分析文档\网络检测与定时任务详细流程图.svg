<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1800">
  <defs>
    <style>
      .node { fill: #f9f9f9; stroke: #333; stroke-width: 2; }
      .start { fill: #e1f5fe; }
      .end { fill: #c8e6c9; }
      .exit { fill: #ffcdd2; }
      .standby { fill: #fff3e0; }
      .main { fill: #e8f5e8; }
      .decision { fill: #fff9c4; }
      .process { fill: #e3f2fd; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .title { font-size: 14px; font-weight: bold; }
      .subtitle { font-size: 12px; font-weight: bold; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .group-border { fill: none; stroke: #666; stroke-width: 1; stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" class="text title">N2 Purge 网络检测与定时任务详细流程</text>
  
  <!-- Network Detection Module -->
  <rect x="50" y="60" width="400" height="500" class="group-border"/>
  <text x="250" y="80" class="text subtitle">网络检测模块</text>
  
  <!-- Start UDP Listener -->
  <rect x="70" y="100" width="140" height="30" rx="5" class="node process"/>
  <text x="140" y="120" class="text">启动UDP监听器</text>
  
  <!-- Bind Port -->
  <rect x="70" y="150" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="170" class="text">绑定端口50001</text>
  
  <!-- Get Local IPs -->
  <rect x="70" y="200" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="220" class="text">获取所有本地IP地址</text>
  
  <!-- Start Listener Thread -->
  <rect x="70" y="250" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="270" class="text">启动监听线程</text>
  
  <!-- Set Ready Flag -->
  <rect x="70" y="300" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="320" class="text">设置_udpListenerReady=true</text>
  
  <!-- Multi-NIC Broadcast -->
  <rect x="270" y="100" width="160" height="30" rx="5" class="node process"/>
  <text x="350" y="120" class="text">多网卡广播检测</text>
  
  <!-- Create UDP Clients -->
  <rect x="270" y="150" width="160" height="30" rx="5" class="node"/>
  <text x="350" y="170" class="text">为每个网卡创建UdpClient</text>
  
  <!-- Calculate Broadcast -->
  <rect x="270" y="200" width="160" height="30" rx="5" class="node"/>
  <text x="350" y="220" class="text">计算网段广播地址</text>
  
  <!-- Send Detection -->
  <rect x="270" y="250" width="160" height="30" rx="5" class="node"/>
  <text x="350" y="270" class="text">发送检测消息到端口50001</text>
  
  <!-- Start Receive Client -->
  <rect x="270" y="320" width="160" height="30" rx="5" class="node"/>
  <text x="350" y="340" class="text">启动接收客户端端口50002</text>
  
  <!-- Wait for Reply -->
  <rect x="270" y="370" width="160" height="30" rx="5" class="node"/>
  <text x="350" y="390" class="text">等待回复3秒</text>
  
  <!-- Receive Reply Decision -->
  <polygon points="350,420 390,440 350,460 310,440" class="node decision"/>
  <text x="350" y="445" class="text">收到回复?</text>
  
  <!-- Analyze Reply -->
  <rect x="70" y="480" width="120" height="30" rx="5" class="node"/>
  <text x="130" y="500" class="text">分析回复来源</text>
  
  <!-- Other Machine Decision -->
  <polygon points="130,530 170,550 130,570 90,550" class="node decision"/>
  <text x="130" y="555" class="text">是否来自其他机器?</text>
  
  <!-- Confirm Other Instance -->
  <rect x="200" y="480" width="120" height="30" rx="5" class="node"/>
  <text x="260" y="500" class="text">确认有其他实例</text>
  
  <!-- Ignore Local Reply -->
  <rect x="200" y="530" width="120" height="30" rx="5" class="node"/>
  <text x="260" y="550" class="text">忽略本机回复</text>
  
  <!-- No Other Instance -->
  <rect x="380" y="480" width="140" height="30" rx="5" class="node"/>
  <text x="450" y="500" class="text">未检测到其他实例</text>
  
  <!-- Enter Standby -->
  <rect x="200" y="600" width="120" height="30" rx="15" class="node standby"/>
  <text x="260" y="620" class="text">进入备用模式</text>
  
  <!-- Become Main -->
  <rect x="380" y="600" width="120" height="30" rx="15" class="node main"/>
  <text x="440" y="620" class="text">成为主实例</text>
  
  <!-- Standby Mode Module -->
  <rect x="50" y="680" width="300" height="280" class="group-border"/>
  <text x="200" y="700" class="text subtitle">备用模式监控</text>
  
  <!-- Start Standby Mode -->
  <rect x="70" y="720" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="740" class="text">StartStandbyMode</text>
  
  <!-- Wait 30 Seconds -->
  <rect x="70" y="770" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="790" class="text">等待30秒</text>
  
  <!-- Re-execute Detection -->
  <rect x="70" y="820" width="140" height="30" rx="5" class="node"/>
  <text x="140" y="840" class="text">重新执行广播检测</text>
  
  <!-- Main Still Exists Decision -->
  <polygon points="140,870 180,890 140,910 100,890" class="node decision"/>
  <text x="140" y="895" class="text">主实例还存在?</text>
  
  <!-- Upgrade to Main -->
  <rect x="230" y="820" width="100" height="30" rx="5" class="node"/>
  <text x="280" y="840" class="text">升级为主实例</text>
  
  <!-- Set Reset Event -->
  <rect x="230" y="870" width="100" height="30" rx="5" class="node"/>
  <text x="280" y="890" class="text">设置_resetEvent.Set</text>
  
  <!-- Task Management Module -->
  <rect x="500" y="60" width="350" height="600" class="group-border"/>
  <text x="675" y="80" class="text subtitle">定时任务管理</text>
  
  <!-- Check and Setup Auto Task -->
  <rect x="520" y="100" width="160" height="30" rx="5" class="node process"/>
  <text x="600" y="120" class="text">CheckAndSetupAutoTask</text>
  
  <!-- Get Executable Path -->
  <rect x="520" y="150" width="160" height="30" rx="5" class="node"/>
  <text x="600" y="170" class="text">获取可执行文件路径</text>
  
  <!-- Check Task Exists -->
  <rect x="520" y="200" width="160" height="30" rx="5" class="node"/>
  <text x="600" y="220" class="text">检查任务是否已存在</text>
  
  <!-- Task Exists Decision -->
  <polygon points="600,250 640,270 600,290 560,270" class="node decision"/>
  <text x="600" y="275" class="text">任务存在?</text>
  
  <!-- Skip Creation -->
  <rect x="520" y="320" width="100" height="30" rx="5" class="node"/>
  <text x="570" y="340" class="text">跳过创建</text>
  
  <!-- Check Admin Rights -->
  <rect x="700" y="250" width="120" height="30" rx="5" class="node"/>
  <text x="760" y="270" class="text">检查管理员权限</text>
  
  <!-- Is Admin Decision -->
  <polygon points="760,300 800,320 760,340 720,320" class="node decision"/>
  <text x="760" y="325" class="text">是否管理员?</text>
  
  <!-- Record Insufficient Rights -->
  <rect x="700" y="370" width="120" height="30" rx="5" class="node exit"/>
  <text x="760" y="390" class="text">记录权限不足</text>
  
  <!-- Generate Task XML -->
  <rect x="700" y="420" width="120" height="30" rx="5" class="node"/>
  <text x="760" y="440" class="text">生成任务XML配置</text>
  
  <!-- Set Task Parameters -->
  <rect x="700" y="470" width="120" height="30" rx="5" class="node"/>
  <text x="760" y="490" class="text">设置任务参数</text>
  
  <!-- Interval 5 Minutes -->
  <rect x="700" y="520" width="120" height="30" rx="5" class="node"/>
  <text x="760" y="540" class="text">间隔时间: 5分钟</text>
  
  <!-- Execution Limit -->
  <rect x="520" y="470" width="120" height="30" rx="5" class="node"/>
  <text x="580" y="490" class="text">执行时限: 10分钟</text>
  
  <!-- Retry Count -->
  <rect x="520" y="520" width="120" height="30" rx="5" class="node"/>
  <text x="580" y="540" class="text">失败重试: 3次</text>
  
  <!-- Save Temp XML -->
  <rect x="520" y="570" width="120" height="30" rx="5" class="node"/>
  <text x="580" y="590" class="text">保存临时XML文件</text>
  
  <!-- Execute schtasks -->
  <rect x="700" y="570" width="120" height="30" rx="5" class="node"/>
  <text x="760" y="590" class="text">执行schtasks命令</text>
  
  <!-- schtasks Command -->
  <rect x="520" y="620" width="300" height="30" rx="5" class="node"/>
  <text x="670" y="640" class="text">schtasks /create /tn N2PurgeAutoMonitor /xml</text>
  
  <!-- Create Success Decision -->
  <polygon points="670,670 710,690 670,710 630,690" class="node decision"/>
  <text x="670" y="695" class="text">创建成功?</text>
  
  <!-- Task Created -->
  <rect x="520" y="740" width="120" height="30" rx="5" class="node end"/>
  <text x="580" y="760" class="text">任务创建完成</text>
  
  <!-- Record Failure -->
  <rect x="750" y="740" width="120" height="30" rx="5" class="node exit"/>
  <text x="810" y="760" class="text">记录失败原因</text>
  
  <!-- Clean Temp Files -->
  <rect x="630" y="800" width="120" height="30" rx="5" class="node"/>
  <text x="690" y="820" class="text">清理临时文件</text>
  
  <!-- UDP Message Processing Module -->
  <rect x="900" y="60" width="250" height="200" class="group-border"/>
  <text x="1025" y="80" class="text subtitle">UDP消息处理</text>
  
  <!-- Receive UDP Message -->
  <rect x="920" y="100" width="120" height="30" rx="5" class="node"/>
  <text x="980" y="120" class="text">收到UDP消息</text>
  
  <!-- Parse Message -->
  <rect x="920" y="150" width="120" height="30" rx="5" class="node"/>
  <text x="980" y="170" class="text">解析消息内容</text>
  
  <!-- Is Detection Message -->
  <polygon points="980,200 1020,220 980,240 940,220" class="node decision"/>
  <text x="980" y="225" class="text">是检测消息?</text>
  
  <!-- Select Best Reply IP -->
  <rect x="1060" y="150" width="120" height="30" rx="5" class="node"/>
  <text x="1120" y="170" class="text">选择最佳回复IP</text>
  
  <!-- Send Reply -->
  <rect x="1060" y="200" width="120" height="30" rx="5" class="node"/>
  <text x="1120" y="220" class="text">发送回复到端口50002</text>
  
  <!-- Ignore Message -->
  <rect x="920" y="280" width="120" height="30" rx="5" class="node"/>
  <text x="980" y="300" class="text">忽略消息</text>
  
  <!-- Final Node -->
  <rect x="600" y="1000" width="140" height="40" rx="20" class="node start"/>
  <text x="670" y="1025" class="text">程序正常启动</text>
  
  <!-- Arrows for Network Detection -->
  <line x1="140" y1="130" x2="140" y2="150" class="arrow"/>
  <line x1="140" y1="180" x2="140" y2="200" class="arrow"/>
  <line x1="140" y1="230" x2="140" y2="250" class="arrow"/>
  <line x1="140" y1="280" x2="140" y2="300" class="arrow"/>
  
  <line x1="350" y1="130" x2="350" y2="150" class="arrow"/>
  <line x1="350" y1="180" x2="350" y2="200" class="arrow"/>
  <line x1="350" y1="230" x2="350" y2="250" class="arrow"/>
  <line x1="350" y1="280" x2="350" y2="320" class="arrow"/>
  <line x1="350" y1="350" x2="350" y2="370" class="arrow"/>
  <line x1="350" y1="400" x2="350" y2="420" class="arrow"/>
  
  <!-- Decision arrows -->
  <line x1="310" y1="440" x2="190" y2="480" class="arrow"/>
  <text x="250" y="460" class="text" style="font-size: 10px;">是</text>
  
  <line x1="390" y1="440" x2="450" y2="480" class="arrow"/>
  <text x="420" y="460" class="text" style="font-size: 10px;">否</text>
  
  <line x1="130" y1="510" x2="130" y2="530" class="arrow"/>
  
  <line x1="90" y1="550" x2="260" y2="480" class="arrow"/>
  <text x="175" y="515" class="text" style="font-size: 10px;">是</text>
  
  <line x1="170" y1="550" x2="260" y2="530" class="arrow"/>
  <text x="215" y="540" class="text" style="font-size: 10px;">否</text>
  
  <line x1="260" y1="510" x2="260" y2="600" class="arrow"/>
  <line x1="450" y1="510" x2="440" y2="600" class="arrow"/>
  
  <!-- Standby Mode arrows -->
  <line x1="260" y1="630" x2="140" y2="720" class="arrow"/>
  <line x1="140" y1="750" x2="140" y2="770" class="arrow"/>
  <line x1="140" y1="800" x2="140" y2="820" class="arrow"/>
  <line x1="140" y1="850" x2="140" y2="870" class="arrow"/>
  
  <line x1="100" y1="890" x2="70" y2="790" class="arrow"/>
  <text x="85" y="840" class="text" style="font-size: 10px;">是</text>
  
  <line x1="180" y1="890" x2="230" y2="840" class="arrow"/>
  <text x="205" y="865" class="text" style="font-size: 10px;">否</text>
  
  <line x1="280" y1="850" x2="280" y2="870" class="arrow"/>
  
  <!-- Task Management arrows -->
  <line x1="600" y1="130" x2="600" y2="150" class="arrow"/>
  <line x1="600" y1="180" x2="600" y2="200" class="arrow"/>
  <line x1="600" y1="230" x2="600" y2="250" class="arrow"/>
  
  <line x1="560" y1="270" x2="570" y2="320" class="arrow"/>
  <text x="565" y="295" class="text" style="font-size: 10px;">是</text>
  
  <line x1="640" y1="270" x2="700" y2="270" class="arrow"/>
  <text x="670" y="265" class="text" style="font-size: 10px;">否</text>
  
  <line x1="760" y1="280" x2="760" y2="300" class="arrow"/>
  
  <line x1="720" y1="320" x2="760" y2="370" class="arrow"/>
  <text x="740" y="345" class="text" style="font-size: 10px;">否</text>
  
  <line x1="800" y1="320" x2="760" y2="420" class="arrow"/>
  <text x="780" y="370" class="text" style="font-size: 10px;">是</text>
  
  <line x1="760" y1="450" x2="760" y2="470" class="arrow"/>
  <line x1="760" y1="500" x2="760" y2="520" class="arrow"/>
  <line x1="760" y1="550" x2="760" y2="570" class="arrow"/>
  
  <line x1="700" y1="490" x2="640" y2="490" class="arrow"/>
  <line x1="580" y1="500" x2="580" y2="520" class="arrow"/>
  <line x1="580" y1="550" x2="580" y2="570" class="arrow"/>
  <line x1="580" y1="600" x2="700" y2="590" class="arrow"/>
  
  <line x1="670" y1="620" x2="670" y2="670" class="arrow"/>
  
  <line x1="630" y1="690" x2="580" y2="740" class="arrow"/>
  <text x="605" y="715" class="text" style="font-size: 10px;">是</text>
  
  <line x1="710" y1="690" x2="810" y2="740" class="arrow"/>
  <text x="760" y="715" class="text" style="font-size: 10px;">否</text>
  
  <line x1="580" y1="770" x2="630" y2="800" class="arrow"/>
  <line x1="810" y1="770" x2="750" y2="800" class="arrow"/>
  <line x1="570" y1="350" x2="630" y2="800" class="arrow"/>
  <line x1="760" y1="400" x2="750" y2="800" class="arrow"/>
  
  <!-- UDP Message Processing arrows -->
  <line x1="980" y1="130" x2="980" y2="150" class="arrow"/>
  <line x1="980" y1="180" x2="980" y2="200" class="arrow"/>
  
  <line x1="1020" y1="220" x2="1060" y2="170" class="arrow"/>
  <text x="1040" y="195" class="text" style="font-size: 10px;">是</text>
  
  <line x1="940" y1="220" x2="980" y2="280" class="arrow"/>
  <text x="960" y="250" class="text" style="font-size: 10px;">否</text>
  
  <line x1="1120" y1="180" x2="1120" y2="200" class="arrow"/>
  
  <!-- Final convergence -->
  <line x1="440" y1="630" x2="600" y2="1000" class="arrow"/>
  <line x1="280" y1="900" x2="600" y2="1000" class="arrow"/>
  <line x1="690" y1="830" x2="670" y2="1000" class="arrow"/>
</svg>
