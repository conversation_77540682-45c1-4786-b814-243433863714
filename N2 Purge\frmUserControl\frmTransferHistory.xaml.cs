﻿using DBEntity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmTransferHistory.xaml 的交互逻辑
    /// </summary>
    public partial class frmTransferHistory : UserControl
    {
        public frmTransferHistory()
        {
            InitializeComponent();
            dtstart.Value = DateTime.Now.AddDays(-1);
            dtend.Value = DateTime.Now;
            cbxCommandState.Items.Add("Wait");
            cbxCommandState.Items.Add("Start");
            cbxCommandState.Items.Add("Complete");
            cbxCommandState.Items.Add("Abort");
            dgv.ItemsSource = transferHistories;
        }
        private ObservableCollection<TransferHistory>  transferHistories = new ObservableCollection<TransferHistory>();
        public async void GetInfoFromDb()
        {
            try
            {
                transferHistories.Clear();
                var query = await GlobalData.dbHelper.thtransferdb.GetTransferHistoryByTimeRangeAsync((DateTime)dtstart.Value, (DateTime)dtend.Value);
                if (query != null)
                {
                    if (txtCommandID.Text.Length > 0)
                        query.Where(x => x.Cmd_ID.Contains(txtCommandID.Text)).ToList();
                    if (txtCommandSrc.Text.Length > 0)
                        query.Where(x => x.Cmd_Source.Contains(txtCommandSrc.Text)).ToList();
                    if (txtCarrierID.Text.Length > 0)
                        query.Where(x => x.Carrier_ID.Contains(txtCarrierID.Text)).ToList();
                    if (cbxCommandState.Text != null && cbxCommandState.Text.ToString().Length > 0)
                        query.Where(x => x.Cmd_State.Equals(cbxCommandState.Text.ToString())).ToList();
                    foreach (var item in query)
                    {
                        transferHistories.Add(item);
                    }
                }
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
           
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            GetInfoFromDb();
        }
    }
}
