﻿<UserControl x:Class="N2Purge.userControls.TopScInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="800" Loaded="UserControl_Loaded" >
    <Grid>
        <Grid.ColumnDefinitions>
            <!--<ColumnDefinition Width="2*"/>-->
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2.5*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="1.2*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="1.2*"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <!--<Image x:Name="myImage"  Grid.Column="0" Stretch="Fill" Grid.RowSpan="2"/>-->
        <Label Content="Alarm" BorderThickness="1" BorderBrush="White" Grid.Column="0" TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Server" BorderThickness="1" BorderBrush="White"  Grid.Column="1" TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="HSMS State" BorderThickness="1" BorderBrush="White"  Grid.Column="2" TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="MCS" Grid.Column="3" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Control State" Grid.Column="4" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="SC State" Grid.Column="5" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="SC Mode" Grid.Column="6" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Machine State" Grid.Column="7" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="White" Background="RoyalBlue" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Button x:Name="btnRun" Content="Run" Grid.Column="8" Background="Snow" Click="btnRun_Click"/>
        <Button x:Name="btnPause" Content="Pause" BorderThickness="1"   Grid.Column="8" Grid.Row="1" Background="Snow" Click="btnPause_Click"/>

        <Label Content="Total Shelf" BorderThickness="1" BorderBrush="White" Grid.Column="9" Grid.Row="0" TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding TotlaShelf}" BorderThickness="1" BorderBrush="White"  Grid.Column="10" Grid.Row="0" TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Cassette Cnt" BorderThickness="1" BorderBrush="White"  Grid.Column="11" TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding Cassette}" Grid.Column="12" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Empty Shelf" Grid.Column="9" Grid.Row="1" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding  EmptyShelf}" Grid.Column="10" Grid.Row="1" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="Command Cnt" Grid.Column="11" Grid.Row="1" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding CommandCnt}" Grid.Column="12" Grid.Row="1" BorderThickness="1" BorderBrush="White"  TextBlock.FontSize="12" TextBlock.Foreground="Black" Background="Snow" VerticalContentAlignment="Center" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalContentAlignment="Center"/>



        <TextBlock Grid.Column="13" Grid.RowSpan="2" x:Name="TimeTextBlock" FontSize="16" Foreground="Blue" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="3"/>

      
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="0" Grid.Row="1" 
       TextBlock.FontSize="10" TextBlock.Foreground="White" 
       VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
       VerticalAlignment="Stretch" HorizontalContentAlignment="Center" MouseLeftButtonDown="Label_MouseLeftButtonDown">
            <Label.Style>
                <Style TargetType="Label">
                    <!--<Setter Property="Content" Value="{Binding Alarm}" />-->
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Alarm}" Value="Error">
                            <Setter Property="Background" Value="Red" />
                            <!--<Setter Property="Content" Value="Error: Immediate Attention Required" />-->
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Alarm}" Value="Normal">
                            <Setter Property="Background" Value="#32CD32" />
                            <!--<Setter Property="Content" Value="System Normal" />-->
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="1" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding Server}" />
                    <Style.Triggers>
                        <!--<DataTrigger Binding="{Binding Server}" Value="Connected">
                            <Setter Property="Background" Value="Yellow" />
                            <Setter Property="Content" Value="Warning: Check System" />
                        </DataTrigger>-->
                        <DataTrigger Binding="{Binding Server}" Value="0">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="DisConnect" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Server}" Value="1">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Connect" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="2" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding HSMSState}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding HSMSState}" Value="NotConnected">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="DisConnect" />
                        </DataTrigger>

                        <DataTrigger Binding="{Binding HSMSState}" Value="Connected">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Connected" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="3" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding MCS}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding MCS}" Value="Disabled">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="DisConnect" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding MCS}" Value="Error">
                            <Setter Property="Background" Value="Red" />
                            <Setter Property="Content" Value="Error: Immediate Attention Required" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding MCS}" Value="Communicating">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Communicating" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="4" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding ControlState}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding ControlState}" Value="EqOffline">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="EqOffline" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ControlState}" Value="AttemptOnline">
                            <Setter Property="Background" Value="Blue" />
                            <!--<Setter Property="Content" Value="Error: Immediate Attention Required" />-->
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ControlState}" Value="HostOffline">
                            <Setter Property="Background" Value="Yellow" />
                            <Setter Property="Content" Value="System Normal" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ControlState}" Value="OnlineLocal">
                            <Setter Property="Background" Value="GreenYellow" />
                            <Setter Property="Content" Value="OnlineLocal" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ControlState}" Value="OnlineRemote">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="OnlineRemote" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="5" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding ScState}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding ScState}" Value="None">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="None" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScState}" Value="SCInit">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="SCInit" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScState}" Value="Paused">
                            <Setter Property="Background" Value="Red" />
                            <Setter Property="Content" Value="Paused" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScState}" Value="Auto">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Auto" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScState}" Value="Pausing">
                            <Setter Property="Background" Value="Red" />
                            <Setter Property="Content" Value="Paused" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="6" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding ScMode}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding ScMode}" Value="Normal">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Normal" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScMode}" Value="Maintenance">
                            <Setter Property="Background" Value="Gray" />
                            <Setter Property="Content" Value="Maintenance" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScMode}" Value="Test">
                            <Setter Property="Background" Value="Yellow" />
                            <Setter Property="Content" Value="Test" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ScMode}" Value="Simulation">
                            <Setter Property="Background" Value="DimGray" />
                            <Setter Property="Content" Value="Simulation" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <Label BorderThickness="1" BorderBrush="White" Grid.Column="7" Grid.Row="1" 
TextBlock.FontSize="10" TextBlock.Foreground="White" 
VerticalContentAlignment="Center" HorizontalAlignment="Stretch" 
VerticalAlignment="Stretch" HorizontalContentAlignment="Center">
            <Label.Style>
                <Style TargetType="Label">
                    <Setter Property="Content" Value="{Binding MachineState}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding MachineState}" Value="None">
                            <Setter Property="Background" Value="Yellow" />
                            <Setter Property="Content" Value="None" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding MachineState}" Value="Down">
                            <Setter Property="Background" Value="Red" />
                            <Setter Property="Content" Value="Down" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding MachineState}" Value="Normal">
                            <Setter Property="Background" Value="#32CD32" />
                            <Setter Property="Content" Value="Normal" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
      
    </Grid>
</UserControl>
