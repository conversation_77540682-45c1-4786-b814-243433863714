<Window x:Class="N2Purge.frmUserControl.FrmUpdatePriority"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="更新优先级" Height="200" Width="300" WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <TextBlock Text="Command:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="0,0,10,0"/>
        <TextBlock x:Name="txtCommand" Text="Unknown" Grid.Row="0" Grid.Column="1" VerticalAlignment="Center"/>

        <TextBlock Text="Priority:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="0,10,10,0"/>
        <TextBox x:Name="txtPriority" Grid.Row="1" Grid.Column="1" Margin="0,10,0,0"/>

        <Button x:Name="btnSubmit" Content="Submit" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                Width="80" Height="25" Margin="0,10,0,0" Click="btnSubmit_Click"/>
    </Grid>
</Window>