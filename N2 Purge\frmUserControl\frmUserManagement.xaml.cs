﻿using DBEntity;
using LiveCharts.Defaults;
using MySqlX.XDevAPI.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmUserManagement.xaml 的交互逻辑
    /// </summary>
    public partial class frmUserManagement : UserControl
    {
        public bool IsLoad=false;
        private ObservableCollection<User> users = new ObservableCollection<User>();
        public frmUserManagement()
        {
            InitializeComponent();
            if (IsLoad) return;
           // InitializeUserList();
            IsLoad = true;
            dgv.ItemsSource=users;

        }
        public async void InitializeUserList()
        {
            try
            {
                var userHistories = await GlobalData.dbHelper._userManagement.GetAllUsersAsync();
                users.Clear();
               
                foreach (var user in userHistories)
                {
                    User cur=new User();
                    cur.UserName = user.UserName;
                    cur.Password = user.Password;
                    cur.UserLevel = user.UserLevel;
                    users.Add(cur);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取用户列表失败：{ex.Message}");
            }
        }

        private async void btnadd_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddUserWindow();
            addWindow.Title = "添加用户";
            if (addWindow.ShowDialog() == true)
            {
                users.Add(addWindow.NewUser);
                UserHistory userHistory = new UserHistory();
                userHistory.UserLevel = addWindow.NewUser.UserLevel;
                userHistory.UserName = addWindow.NewUser.UserName;
                userHistory.Password = addWindow.NewUser.Password;
                userHistory.CreateTime = DateTime.Now;
             var result=   await GlobalData.dbHelper._userManagement.AddUser(userHistory, GlobalData.dbHelper._userManagement._currentUserLevel);
                if (result)
                {
                    MessageBox.Show("添加成功");
                }
                else { MessageBox.Show("添加失败"); }
                // 如果需要保存到数据库，可以在这里添加相关代码
                InitializeUserList();
            }
        }

        private async void btndel_Click(object sender, RoutedEventArgs e)
        {
            var sel = dgv.SelectedItem as User;
            if (sel == null) return;
            var dr = MessageBox.Show("确定要删除该用户吗", "提示", MessageBoxButton.YesNo);
            if (dr == MessageBoxResult.Yes)
            {
             var result=   await GlobalData.dbHelper._userManagement.DeleteUser(sel.UserName, GlobalData.dbHelper._userManagement._currentUserLevel);
                if (result)
                {
                    InitializeUserList();
                    MessageBox.Show("删除成功");
                }
                else { MessageBox.Show("删除失败"); }
            }
        }

        private async void btnupdate_Click(object sender, RoutedEventArgs e)
        {
            var sel = dgv.SelectedItem as User;
            if (sel == null) return;
            var addWindow = new AddUserWindow();
            addWindow.txtUsername.Text = sel.UserName;
            addWindow.txtUsername.IsEnabled=false; addWindow.txtPassword.IsEnabled=false;
            addWindow.txtPassword.Password = sel.Password;
            addWindow.cmbUserLevel.Text = sel.UserLevel.ToString();
            addWindow.Title = "更新用户";
            if (addWindow.ShowDialog() == true)
            {
                users.Add(addWindow.NewUser);
                UserHistory userHistory = new UserHistory();
                userHistory.UserLevel = addWindow.NewUser.UserLevel;
                userHistory.UserName = addWindow.NewUser.UserName;
                userHistory.Password = addWindow.NewUser.Password;
                userHistory.CreateTime = DateTime.Now;
               var result= await GlobalData.dbHelper._userManagement.UpdateUser(userHistory, GlobalData.dbHelper._userManagement._currentUserLevel);
                if (result)
                {
                   
                    MessageBox.Show("更新成功");
                }
                else { MessageBox.Show("更新失败"); }
                // 如果需要保存到数据库，可以在这里添加相关代码
                InitializeUserList();
            }
        }
    }
    public class User
    {
        public string UserName { get; set; }
        public string Password { get; set; }
        public int UserLevel { get; set; }
    }
}
