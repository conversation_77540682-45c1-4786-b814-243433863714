﻿<UserControl x:Class="N2Purge.Conveyor.frmConvryorMain"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.Conveyor"
             xmlns:localctr="clr-namespace:N2Purge.silan"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000">
    <Grid x:Name="mainGrid" Margin="30">
        <!--最初设计是在xaml文件中定义,新的方案是从数据库中读取并且生成表格数据-->
        <!--<Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <localctr:Shelf x:Name="CV_5_4" Grid.Column="5" Grid.Row="4" Margin="2"/>
        <localctr:Shelf x:Name="CV_8_4" Grid.Column="8" Grid.Row="4" Margin="2"/>

        <localctr:Shelf x:Name="CV_0_5" Grid.Column="0" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_1_5" Grid.Column="1" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_2_5" Grid.Column="2" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_3_5" Grid.Column="3" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_4_5" Grid.Column="4" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_5_5" Grid.Column="5" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_6_5" Grid.Column="6" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_7_5" Grid.Column="7" Grid.Row="5" Margin="2"/>
        <localctr:Shelf x:Name="CV_8_5" Grid.Column="8" Grid.Row="5" Margin="2"/>

        <localctr:Shelf x:Name="CV_5_2" Grid.Column="5" Grid.Row="2" Margin="2"/>
        <localctr:Shelf x:Name="CV_8_2" Grid.Column="8" Grid.Row="2" Margin="2"/>
        
      
        <localctr:Shelf x:Name="CV_5_3" Grid.Column="5" Grid.Row="3" Margin="2"/>
        <localctr:Shelf x:Name="CV_8_3" Grid.Column="8" Grid.Row="3" Margin="2"/>-->

    </Grid>
</UserControl>
