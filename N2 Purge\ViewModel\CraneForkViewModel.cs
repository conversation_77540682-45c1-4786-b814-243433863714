﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// CraneFork 后续没有使用
    /// </summary>
    public class CraneForkViewModel:VmPropertyChange
    {
        private double _cranePosition;
        public double CranePosition
        {
            get => _cranePosition;
            set
            {
                if (_cranePosition != value)
                {
                    _cranePosition = value;
                    OnPropertyChanged(nameof(CranePosition));
                }
            }
        }
        private bool _isVisible;
        private bool _isVisible2;
        private bool _isVisible3;
        private bool _isVisible4;


        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged(nameof(IsVisible));
                }
            }
        }
        public bool IsVisible2
        {
            get => _isVisible2;
            set
            {
                if (_isVisible2 != value)
                {
                    _isVisible2 = value;
                    OnPropertyChanged(nameof(IsVisible2));
                }
            }
        }

        public bool IsVisible3
        {
            get => _isVisible3;
            set
            {
                if (_isVisible3 != value)
                {
                    _isVisible3 = value;
                    OnPropertyChanged(nameof(IsVisible3));
                }
            }
        }

        public bool IsVisible4
        {
            get => _isVisible4;
            set
            {
                if (_isVisible4 != value)
                {
                    _isVisible4 = value;
                    OnPropertyChanged(nameof(IsVisible4));
                }
            }
        }

    }
}
