﻿<UserControl x:Class="N2Purge.frmUserControl.frmUserManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="80"/>
        </Grid.ColumnDefinitions>
        <DataGrid x:Name="dgv" Grid.Column="0" Background="Transparent" CanUserAddRows="False" BorderBrush="Black" BorderThickness="1"/>
        <Border Grid.Column="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="1*"/>
                   
                </Grid.RowDefinitions>
                <Button Background="Snow" Content="增加" x:Name="btnadd" Grid.Row="1" Margin="5" Click="btnadd_Click"/>
                <Button Background="Snow" Content="删除" x:Name="btndel" Grid.Row="2" Margin="5" Click="btndel_Click"/>
                <Button Background="Snow" Content="修改" x:Name="btnupdate" Grid.Row="3" Margin="5" Click="btnupdate_Click"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
