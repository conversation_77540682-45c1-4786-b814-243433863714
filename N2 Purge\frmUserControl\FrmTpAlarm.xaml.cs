﻿using DBEntity;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// FrmTpAlarm.xaml 的交互逻辑
    /// </summary>
    public partial class FrmTpAlarm : Window
    {
        private ObservableCollection<TpAlarm> _alarms;
        public FrmTpAlarm()
        {
            InitializeComponent();
            _alarms = new ObservableCollection<TpAlarm>();
            dgvAlarms.ItemsSource = _alarms;
            StartDataRefresh();
            RefreshAlarm();
        }

        // 如果需要定时刷新数据，可以添加以下方法
        private void StartDataRefresh()
        {
            System.Windows.Threading.DispatcherTimer timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5); // 每5秒刷新一次
            timer.Tick += (s, e) => RefreshAlarm();
            timer.Start();
        }
        private async void RefreshAlarm()
        {
            try
            {
                var alarms = await GlobalData.dbHelper.tpAlarmdb.GetAllTpAlarmAsync();
                if (alarms.Count == 0)
                {
                    this.Close();
                }
                else
                {
                    _alarms.Clear();
                    foreach (var alarm in alarms)
                    {
                        _alarms.Add(alarm);
                    }
                    TpAlarm newestAlarm = alarms.OrderByDescending(x => x.Last_Time).First();
                    string strAlarmText = newestAlarm.Unit + " [" + newestAlarm.Code.ToString() + "] " + newestAlarm.Comment;
                    lblAlarm.Text = "Alarm: " + strAlarmText;
                }
              
              
            }
            catch (Exception ex)
            {
                Proj.Log.Logger.Instance.ExceptionLog("RefreshAlarm: " + ex.Message + ", Stack: " + ex.StackTrace);
                
            }
           
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
