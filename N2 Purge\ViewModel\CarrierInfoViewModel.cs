﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// CarrierInfoViewModel
    /// </summary>
    public class CarrierInfoViewModel : VmPropertyChange
    {
        private string _id;
        private string _location;
        private string _state;
        private string _idReadStatus;
        private DateTime _installTime;
        private string _comment;
        //CarrierID
        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }
        //CarrierLocation
        public string Location
        {
            get => _location;
            set
            {
                _location = value;
                OnPropertyChanged(nameof(Location));
            }
        }
        //CarrierState
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                OnPropertyChanged(nameof(State));
            }
        }
        
        public string IdReadStatus
        {
            get => _idReadStatus;
            set
            {
                _idReadStatus = value;
                OnPropertyChanged(nameof(IdReadStatus));
            }
        }

        public DateTime InstallTime
        {
            get => _installTime;
            set
            {
                _installTime = value;
                OnPropertyChanged(nameof(InstallTime));
            }
        }

        public string Comment
        {
            get => _comment;
            set
            {
                _comment = value;
                OnPropertyChanged(nameof(Comment));
            }
        }

        
    }
}
