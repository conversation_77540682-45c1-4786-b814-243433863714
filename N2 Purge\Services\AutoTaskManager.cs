using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Security.Principal;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 自动计划任务管理器 - 自动检查并创建5分钟间隔的计划任务
    /// </summary>
    public class AutoTaskManager
    {
        private const string TASK_NAME = "N2PurgeAutoMonitor";
        private const string TASK_DESCRIPTION = "N2 Purge 智能传送带控制系统自动监控任务 - 每5分钟检查一次";
        private const int INTERVAL_MINUTES = 5;

        /// <summary>
        /// 检查并自动设置计划任务
        /// </summary>
        /// <returns>操作结果</returns>
        public static bool CheckAndSetupAutoTask()
        {
            try
            {
                Logger.Instance?.OperationLog("=== [自动任务] 开始检查计划任务状态 ===");
                Logger.Instance?.OperationLog($"[自动任务] 当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 首先检查可执行文件路径
                string executablePath = GetExecutablePath();
                Logger.Instance?.OperationLog($"[自动任务] 检测到的可执行文件路径: {executablePath}");

                if (string.IsNullOrEmpty(executablePath) || !File.Exists(executablePath))
                {
                    Logger.Instance?.ExceptionLog($"[自动任务] 无法找到有效的可执行文件路径: {executablePath}");
                    return false;
                }

                // 检查任务是否已存在
                if (IsTaskExists())
                {
                    Logger.Instance?.OperationLog($"[自动任务] 计划任务 '{TASK_NAME}' 已存在，跳过创建");
                    return true;
                }

                Logger.Instance?.OperationLog($"[自动任务] 计划任务 '{TASK_NAME}' 不存在，开始创建");

                // 检查管理员权限
                bool isAdmin = IsAdministrator();
                Logger.Instance?.OperationLog($"[自动任务] 管理员权限检查结果: {isAdmin}");

                if (!isAdmin)
                {
                    Logger.Instance?.ExceptionLog("[自动任务] 创建计划任务需要管理员权限");
                    Logger.Instance?.OperationLog("[自动任务] 当前以普通用户权限运行，跳过自动任务创建");
                    Logger.Instance?.OperationLog("[自动任务] 提示：如需自动监控功能，请以管理员身份运行程序或使用AutoTask界面手动创建");

                    // 不尝试提升权限，避免弹出UAC对话框
                    // 用户可以通过AutoTask界面手动创建
                    return false;
                }

                // 创建计划任务
                Logger.Instance?.OperationLog("[自动任务] 具有管理员权限，直接创建计划任务");
                bool createResult = CreateAutoTask();
                Logger.Instance?.OperationLog($"[自动任务] CreateAutoTask() 返回结果: {createResult}");
                Logger.Instance?.OperationLog("=== [自动任务] 检查计划任务状态完成 ===");
                return createResult;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[自动任务] 检查并设置计划任务失败: {ex.Message}");
                Logger.Instance?.ExceptionLog($"[自动任务] 异常详情: {ex}");
                Logger.Instance?.OperationLog("=== [自动任务] 检查计划任务状态完成(异常) ===");
                return false;
            }
        }

        /// <summary>
        /// 检查计划任务是否存在
        /// </summary>
        /// <returns>任务是否存在</returns>
        public static bool IsTaskExists()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/query /tn \"{TASK_NAME}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        bool exists = process.ExitCode == 0;
                        Logger.Instance?.OperationLog($"[自动任务] 任务存在性检查结果: {exists}");
                        return exists;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[自动任务] 检查任务存在性失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 创建自动监控计划任务
        /// </summary>
        /// <returns>创建是否成功</returns>
        private static bool CreateAutoTask()
        {
            try
            {
                string executablePath = GetExecutablePath();
                if (!File.Exists(executablePath))
                {
                    Logger.Instance?.ExceptionLog($"[自动任务] 可执行文件不存在: {executablePath}");
                    return false;
                }

                // 删除已存在的任务（如果有）
                DeleteTask();

                // 创建XML配置
                Logger.Instance?.OperationLog("[自动任务] 开始生成任务XML配置...");
                string taskXml = GenerateTaskXml(executablePath);
                string tempXmlPath = Path.Combine(Path.GetTempPath(), $"{TASK_NAME}.xml");

                Logger.Instance?.OperationLog($"[自动任务] XML配置生成完成，长度: {taskXml.Length} 字符");
                Logger.Instance?.OperationLog($"[自动任务] 临时XML文件路径: {tempXmlPath}");

                // 记录XML内容的前几行用于调试
                string[] xmlLines = taskXml.Split('\n');
                Logger.Instance?.OperationLog($"[自动任务] XML前5行内容:");
                for (int i = 0; i < Math.Min(5, xmlLines.Length); i++)
                {
                    Logger.Instance?.OperationLog($"[自动任务]   {i + 1}: {xmlLines[i].Trim()}");
                }

                // 使用UTF-16编码保存XML文件，这是Windows任务计划程序的标准编码
                File.WriteAllText(tempXmlPath, taskXml, Encoding.Unicode);
                Logger.Instance?.OperationLog($"[自动任务] 临时XML文件已创建: {tempXmlPath} (UTF-16编码)");

                // 验证XML文件内容
                try
                {
                    string savedContent = File.ReadAllText(tempXmlPath, Encoding.Unicode);
                    Logger.Instance?.OperationLog($"[自动任务] 验证XML文件大小: {new FileInfo(tempXmlPath).Length} 字节");
                    Logger.Instance?.OperationLog($"[自动任务] 验证XML内容长度: {savedContent.Length} 字符");

                    // 检查XML是否有效
                    var xmlDoc = new System.Xml.XmlDocument();
                    xmlDoc.LoadXml(savedContent);
                    Logger.Instance?.OperationLog("[自动任务] ✅ XML格式验证通过");
                }
                catch (Exception xmlEx)
                {
                    Logger.Instance?.ExceptionLog($"[自动任务] ❌ XML格式验证失败: {xmlEx.Message}");
                }

                // 使用schtasks命令创建任务
                string schtasksCommand = $"/create /tn \"{TASK_NAME}\" /xml \"{tempXmlPath}\"";
                Logger.Instance?.OperationLog($"[自动任务] 准备执行schtasks命令: schtasks {schtasksCommand}");

                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = schtasksCommand,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                Logger.Instance?.OperationLog("[自动任务] 开始执行schtasks进程...");

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        Logger.Instance?.OperationLog("[自动任务] schtasks进程已启动，等待完成...");

                        // 设置超时时间，避免无限等待
                        bool finished = process.WaitForExit(30000); // 30秒超时

                        if (!finished)
                        {
                            Logger.Instance?.ExceptionLog("[自动任务] schtasks命令执行超时，强制终止进程");
                            process.Kill();
                            return false;
                        }

                        string output = process.StandardOutput.ReadToEnd();
                        string error = process.StandardError.ReadToEnd();
                        int exitCode = process.ExitCode;

                        Logger.Instance?.OperationLog($"[自动任务] schtasks执行完成，退出代码: {exitCode}");
                        Logger.Instance?.OperationLog($"[自动任务] 标准输出: {(string.IsNullOrEmpty(output) ? "(无)" : output)}");
                        Logger.Instance?.OperationLog($"[自动任务] 错误输出: {(string.IsNullOrEmpty(error) ? "(无)" : error)}");

                        if (exitCode == 0)
                        {
                            Logger.Instance?.OperationLog($"[自动任务] ✅ 计划任务创建成功: {TASK_NAME}");
                            Logger.Instance?.OperationLog($"[自动任务] 执行间隔: {INTERVAL_MINUTES} 分钟");
                            Logger.Instance?.OperationLog($"[自动任务] 应用程序: {executablePath}");
                            return true;
                        }
                        else
                        {
                            Logger.Instance?.ExceptionLog($"[自动任务] ❌ 创建计划任务失败，退出代码: {exitCode}");
                            if (!string.IsNullOrEmpty(error))
                            {
                                Logger.Instance?.ExceptionLog($"[自动任务] 错误详情: {error}");
                            }
                            if (!string.IsNullOrEmpty(output))
                            {
                                Logger.Instance?.ExceptionLog($"[自动任务] 输出详情: {output}");
                            }
                            return false;
                        }
                    }
                    else
                    {
                        Logger.Instance?.ExceptionLog("[自动任务] 无法启动schtasks进程");
                        return false;
                    }
                }

                // 清理临时文件
                if (File.Exists(tempXmlPath))
                {
                    File.Delete(tempXmlPath);
                    Logger.Instance?.OperationLog("[自动任务] 临时XML文件已清理");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[自动任务] 创建计划任务异常: {ex.Message}");
                return false;
            }

            return false;
        }

        /// <summary>
        /// 删除计划任务
        /// </summary>
        /// <returns>删除是否成功</returns>
        public static bool DeleteTask()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/delete /tn \"{TASK_NAME}\" /f",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        if (process.ExitCode == 0)
                        {
                            Logger.Instance?.OperationLog($"[自动任务] 计划任务删除成功: {TASK_NAME}");
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[自动任务] 删除计划任务异常: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 尝试提升权限并创建任务
        /// </summary>
        /// <returns>操作是否成功</returns>
        private static bool TryElevateAndCreateTask()
        {
            try
            {
                // 创建一个临时的PowerShell脚本来创建任务
                string scriptContent = GenerateElevationScript();
                string scriptPath = Path.Combine(Path.GetTempPath(), "N2PurgeCreateTask.ps1");
                
                File.WriteAllText(scriptPath, scriptContent, Encoding.UTF8);

                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = $"-ExecutionPolicy Bypass -File \"{scriptPath}\"",
                    UseShellExecute = true,
                    Verb = "runas", // 请求管理员权限
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        
                        // 清理临时脚本
                        if (File.Exists(scriptPath))
                        {
                            File.Delete(scriptPath);
                        }

                        // 检查任务是否创建成功
                        Thread.Sleep(2000); // 等待2秒让任务创建完成
                        bool success = IsTaskExists();
                        
                        if (success)
                        {
                            Logger.Instance?.OperationLog("[自动任务] 通过权限提升成功创建计划任务");
                        }
                        else
                        {
                            Logger.Instance?.ExceptionLog("[自动任务] 权限提升后仍未能创建计划任务");
                        }
                        
                        return success;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[自动任务] 权限提升失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 生成权限提升脚本
        /// </summary>
        /// <returns>PowerShell脚本内容</returns>
        private static string GenerateElevationScript()
        {
            string executablePath = GetExecutablePath();
            string workingDirectory = Path.GetDirectoryName(executablePath) ?? "";

            return $@"
# N2 Purge 自动任务创建脚本
try {{
    $action = New-ScheduledTaskAction -Execute '{executablePath}' -WorkingDirectory '{workingDirectory}'
    $trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes {INTERVAL_MINUTES}) -RepetitionDuration ([TimeSpan]::MaxValue)
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd
    $settings.MultipleInstances = 'IgnoreNew'
    $settings.ExecutionTimeLimit = 'PT0S'
    $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive -RunLevel Highest
    
    Register-ScheduledTask -TaskName '{TASK_NAME}' -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description '{TASK_DESCRIPTION}' -Force
    
    Write-Host 'Task created successfully'
    exit 0
}} catch {{
    Write-Host ""Error: $($_.Exception.Message)""
    exit 1
}}
";
        }

        /// <summary>
        /// 检查是否具有管理员权限
        /// </summary>
        /// <returns>是否为管理员</returns>
        public static bool IsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取当前可执行文件路径
        /// </summary>
        /// <returns>可执行文件路径</returns>
        private static string GetExecutablePath()
        {
            try
            {
                // 方法1: 尝试使用Process.GetCurrentProcess().MainModule.FileName
                string processPath = Process.GetCurrentProcess().MainModule?.FileName ?? "";
                if (!string.IsNullOrEmpty(processPath) && File.Exists(processPath))
                {
                    Logger.Instance?.OperationLog($"[路径获取] 使用进程路径: {processPath}");
                    return processPath;
                }

                // 方法2: 使用Assembly.GetEntryAssembly()
                var entryAssembly = Assembly.GetEntryAssembly();
                if (entryAssembly != null)
                {
                    string entryPath = entryAssembly.Location;
                    if (!string.IsNullOrEmpty(entryPath))
                    {
                        // 如果是DLL，尝试替换为EXE
                        if (entryPath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                        {
                            entryPath = entryPath.Replace(".dll", ".exe");
                        }

                        if (File.Exists(entryPath))
                        {
                            Logger.Instance?.OperationLog($"[路径获取] 使用入口程序集路径: {entryPath}");
                            return entryPath;
                        }
                    }
                }

                // 方法3: 使用当前程序集路径
                string assemblyPath = Assembly.GetExecutingAssembly().Location;
                if (!string.IsNullOrEmpty(assemblyPath))
                {
                    if (assemblyPath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                    {
                        assemblyPath = assemblyPath.Replace(".dll", ".exe");
                    }

                    if (File.Exists(assemblyPath))
                    {
                        Logger.Instance?.OperationLog($"[路径获取] 使用执行程序集路径: {assemblyPath}");
                        return assemblyPath;
                    }
                }

                // 方法4: 在当前目录查找EXE文件
                string currentDir = Directory.GetCurrentDirectory();
                string[] exeFiles = Directory.GetFiles(currentDir, "*.exe");
                foreach (string exeFile in exeFiles)
                {
                    string fileName = Path.GetFileNameWithoutExtension(exeFile);
                    if (fileName.Equals("N2Purge", StringComparison.OrdinalIgnoreCase) ||
                        fileName.Contains("N2Purge"))
                    {
                        Logger.Instance?.OperationLog($"[路径获取] 在当前目录找到EXE: {exeFile}");
                        return exeFile;
                    }
                }

                Logger.Instance?.ExceptionLog("[路径获取] 所有方法都无法找到可执行文件路径");
                return "";
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[路径获取] 获取可执行文件路径失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 生成任务XML配置
        /// </summary>
        /// <param name="executablePath">可执行文件路径</param>
        /// <returns>XML配置字符串</returns>
        private static string GenerateTaskXml(string executablePath)
        {
            string workingDirectory = Path.GetDirectoryName(executablePath) ?? "";
            string currentUser = Environment.UserName;
            string currentDomain = Environment.UserDomainName;

            // 使用更安全的日期时间格式
            string currentDateTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffffffK");
            string startBoundary = DateTime.Now.AddMinutes(1).ToString("yyyy-MM-ddTHH:mm:ss");

            // 转义XML特殊字符
            string safeExecutablePath = System.Security.SecurityElement.Escape(executablePath);
            string safeWorkingDirectory = System.Security.SecurityElement.Escape(workingDirectory);
            string safeCurrentUser = System.Security.SecurityElement.Escape(currentUser);
            string safeCurrentDomain = System.Security.SecurityElement.Escape(currentDomain);
            string safeDescription = System.Security.SecurityElement.Escape(TASK_DESCRIPTION);

            return $@"<?xml version=""1.0"" encoding=""UTF-16""?>
<Task version=""1.4"" xmlns=""http://schemas.microsoft.com/windows/2004/02/mit/task"">
  <RegistrationInfo>
    <Date>{currentDateTime}</Date>
    <Author>{safeCurrentUser}</Author>
    <Description>{safeDescription}</Description>
  </RegistrationInfo>
  <Triggers>
    <TimeTrigger>
      <Enabled>true</Enabled>
      <StartBoundary>{startBoundary}</StartBoundary>
      <Repetition>
        <Interval>PT{INTERVAL_MINUTES}M</Interval>
        <Duration>P1D</Duration>
        <StopAtDurationEnd>false</StopAtDurationEnd>
      </Repetition>
    </TimeTrigger>
  </Triggers>
  <Principals>
    <Principal id=""Author"">
      <UserId>{safeCurrentDomain}\\{safeCurrentUser}</UserId>
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <DisallowStartOnRemoteAppSession>false</DisallowStartOnRemoteAppSession>
    <UseUnifiedSchedulingEngine>true</UseUnifiedSchedulingEngine>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT10M</ExecutionTimeLimit>
    <Priority>7</Priority>
    <RestartOnFailure>
      <Interval>PT1M</Interval>
      <Count>3</Count>
    </RestartOnFailure>
  </Settings>
  <Actions Context=""Author"">
    <Exec>
      <Command>{safeExecutablePath}</Command>
      <WorkingDirectory>{safeWorkingDirectory}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>";
        }
    }
}
