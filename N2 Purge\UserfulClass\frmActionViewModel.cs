﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.UserfulClass
{
    public class frmActionViewModel
    {
        public MoveViewModel? moveViewModel { get; set; }=new MoveViewModel();
        public TransferExcuteViewModel transferExcuteViewModel { get; set; } = new TransferExcuteViewModel() { Priority=30};
        public InstallViewModel installViewModel { get; set; } = new InstallViewModel();
        public ScanViewModel scanViewModel { get; set; } = new ScanViewModel();
        public RemoveViewModel removeViewModel { get; set; } = new RemoveViewModel();
    }
}
