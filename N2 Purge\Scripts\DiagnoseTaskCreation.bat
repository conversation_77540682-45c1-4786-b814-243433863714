@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    N2 Purge 计划任务创建诊断工具
echo ========================================
echo.

set "TASK_NAME=N2PurgeAutoMonitor"
set "APP_PATH=%~dp0..\bin\Debug\N2Purge.exe"

echo [诊断] 开始检查计划任务创建环境...
echo.

:: 1. 检查管理员权限
echo [步骤 1] 检查管理员权限...
net session >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 当前具有管理员权限
    set "HAS_ADMIN=true"
) else (
    echo ❌ 当前没有管理员权限
    set "HAS_ADMIN=false"
)
echo.

:: 2. 检查应用程序文件
echo [步骤 2] 检查应用程序文件...
if exist "%APP_PATH%" (
    echo ✅ 找到应用程序文件: %APP_PATH%
    for %%i in ("%APP_PATH%") do echo    文件大小: %%~zi 字节
    for %%i in ("%APP_PATH%") do echo    修改时间: %%~ti
) else (
    echo ❌ 未找到应用程序文件: %APP_PATH%
    echo.
    echo 尝试在其他位置查找...
    for %%d in (
        "%~dp0..\bin\Release\N2Purge.exe"
        "%~dp0..\N2Purge.exe"
        "%~dp0..\..\bin\Debug\N2Purge.exe"
        "%~dp0..\..\bin\Release\N2Purge.exe"
    ) do (
        if exist "%%d" (
            echo ✅ 在其他位置找到: %%d
            set "APP_PATH=%%d"
            goto :found_app
        )
    )
    echo ❌ 在所有位置都未找到N2Purge.exe
    goto :no_app
)
:found_app
echo.

:: 3. 检查任务计划程序服务
echo [步骤 3] 检查任务计划程序服务...
sc query Schedule >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 任务计划程序服务正在运行
) else (
    echo ❌ 任务计划程序服务未运行
    echo 尝试启动服务...
    sc start Schedule >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ 任务计划程序服务启动成功
    ) else (
        echo ❌ 无法启动任务计划程序服务
    )
)
echo.

:: 4. 检查现有任务
echo [步骤 4] 检查现有任务...
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 任务 '%TASK_NAME%' 已存在
    echo.
    echo 任务详细信息:
    schtasks /query /tn "%TASK_NAME%" /fo LIST /v | findstr /C:"任务名" /C:"状态" /C:"下次运行时间" /C:"上次运行时间" /C:"任务要运行"
) else (
    echo ❌ 任务 '%TASK_NAME%' 不存在
)
echo.

:: 5. 测试schtasks命令
echo [步骤 5] 测试schtasks命令可用性...
schtasks /? >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ schtasks命令可用
) else (
    echo ❌ schtasks命令不可用
)
echo.

:: 6. 检查系统策略
echo [步骤 6] 检查相关系统策略...
echo [信息] 检查组策略设置...

:: 检查是否禁用了任务计划程序
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v DisableTaskMgr >nul 2>&1
if %errorLevel% equ 0 (
    echo ⚠️ 检测到任务管理器可能被禁用的策略
) else (
    echo ✅ 未检测到任务管理器禁用策略
)
echo.

:: 7. 尝试创建测试任务
if "%HAS_ADMIN%"=="true" (
    echo [步骤 7] 尝试创建测试任务...
    
    set "TEST_TASK_NAME=N2PurgeTest_%RANDOM%"
    set "TEMP_XML=%TEMP%\!TEST_TASK_NAME!.xml"
    
    :: 创建简单的测试任务XML
    (
    echo ^<?xml version="1.0" encoding="UTF-16"?^>
    echo ^<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task"^>
    echo   ^<RegistrationInfo^>
    echo     ^<Description^>N2 Purge 测试任务^</Description^>
    echo   ^</RegistrationInfo^>
    echo   ^<Triggers^>
    echo     ^<TimeTrigger^>
    echo       ^<Enabled^>true^</Enabled^>
    echo       ^<StartBoundary^>2030-01-01T00:00:00^</StartBoundary^>
    echo     ^</TimeTrigger^>
    echo   ^</Triggers^>
    echo   ^<Principals^>
    echo     ^<Principal^>
    echo       ^<UserId^>%USERDOMAIN%\%USERNAME%^</UserId^>
    echo       ^<LogonType^>InteractiveToken^</LogonType^>
    echo       ^<RunLevel^>LeastPrivilege^</RunLevel^>
    echo     ^</Principal^>
    echo   ^</Principals^>
    echo   ^<Settings^>
    echo     ^<Enabled^>true^</Enabled^>
    echo     ^<AllowStartOnDemand^>true^</AllowStartOnDemand^>
    echo   ^</Settings^>
    echo   ^<Actions^>
    echo     ^<Exec^>
    echo       ^<Command^>notepad.exe^</Command^>
    echo     ^</Exec^>
    echo   ^</Actions^>
    echo ^</Task^>
    ) > "!TEMP_XML!"
    
    echo [测试] 创建测试任务: !TEST_TASK_NAME!
    schtasks /create /tn "!TEST_TASK_NAME!" /xml "!TEMP_XML!" >nul 2>&1
    
    if !errorLevel! equ 0 (
        echo ✅ 测试任务创建成功
        
        echo [测试] 删除测试任务...
        schtasks /delete /tn "!TEST_TASK_NAME!" /f >nul 2>&1
        if !errorLevel! equ 0 (
            echo ✅ 测试任务删除成功
        ) else (
            echo ⚠️ 测试任务删除失败
        )
    ) else (
        echo ❌ 测试任务创建失败
        echo.
        echo 详细错误信息:
        schtasks /create /tn "!TEST_TASK_NAME!" /xml "!TEMP_XML!"
    )
    
    :: 清理临时文件
    if exist "!TEMP_XML!" del "!TEMP_XML!" >nul 2>&1
    
    echo.
) else (
    echo [步骤 7] 跳过测试任务创建（需要管理员权限）
    echo.
)

:: 8. 检查日志文件
echo [步骤 8] 检查应用程序日志...
set "LOG_DIR=%~dp0..\Logs"
if exist "%LOG_DIR%" (
    echo ✅ 找到日志目录: %LOG_DIR%
    
    for %%f in ("%LOG_DIR%\*.log") do (
        echo [日志] %%~nxf - 大小: %%~zf 字节
    )
    
    echo.
    echo 最近的自动任务相关日志:
    if exist "%LOG_DIR%\Operation.log" (
        findstr /C:"[自动任务]" "%LOG_DIR%\Operation.log" | tail -10 2>nul
    )
    if exist "%LOG_DIR%\Exception.log" (
        findstr /C:"[自动任务]" "%LOG_DIR%\Exception.log" | tail -5 2>nul
    )
) else (
    echo ❌ 未找到日志目录: %LOG_DIR%
)
echo.

:no_app
:: 总结
echo ========================================
echo 诊断总结
echo ========================================
echo.

if "%HAS_ADMIN%"=="true" (
    echo ✅ 管理员权限: 正常
) else (
    echo ❌ 管理员权限: 缺失 - 这是创建计划任务失败的主要原因
)

if exist "%APP_PATH%" (
    echo ✅ 应用程序文件: 正常
) else (
    echo ❌ 应用程序文件: 缺失 - 无法创建有效的计划任务
)

echo.
echo 建议操作:
if "%HAS_ADMIN%"=="false" (
    echo 1. 以管理员身份重新运行N2Purge程序
    echo 2. 或者以管理员身份运行此诊断工具
)

if not exist "%APP_PATH%" (
    echo 3. 确保N2Purge.exe文件存在于正确位置
    echo 4. 重新编译项目确保生成可执行文件
)

echo 5. 检查防病毒软件是否阻止了计划任务创建
echo 6. 查看应用程序日志了解详细错误信息
echo.

pause
