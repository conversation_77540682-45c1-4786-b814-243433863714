﻿<UserControl x:Class="N2Purge.userControls.Crane"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="50">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    <Canvas Margin="0" x:Name="cav" Width="50">
        <!-- Rectangle -->
        <Border Height="50" x:Name="bd">
            <Grid>
                <Border BorderThickness="1" BorderBrush="Black" Height="30" Width="50">
                    <Grid>
                        <Rectangle Width="50" Height="10" Stroke="Black"  HorizontalAlignment="Left" Margin="0 0,0,0" StrokeThickness="1" VerticalAlignment="Center">
                            <!--<Rectangle.RenderTransform>
                                <TranslateTransform x:Name="rectTransform" />
                            </Rectangle.RenderTransform>-->
                        </Rectangle>

                        <!-- Triangle -->
                        <Polygon Points="25,0 40,9 10,9"  Fill="Yellow" Visibility="{Binding IsVisible, Converter={StaticResource BooleanToVisibilityConverter}}" >
                            <!--<Polygon.RenderTransform>
                                <TranslateTransform x:Name="triangleTransform" />
                            </Polygon.RenderTransform>-->
                        </Polygon>
                        <Polygon Points="25,-10 40,-1 10,-1" Fill="Green" Canvas.Top="25" Visibility="{Binding IsVisible2, Converter={StaticResource BooleanToVisibilityConverter}}" >
                            <!--<Polygon.RenderTransform>
                                <TranslateTransform x:Name="triangleTransform1" />
                            </Polygon.RenderTransform>-->
                        </Polygon>
                        <Polygon Points="25,28 40,19 10,19" Fill="Yellow" Visibility="{Binding IsVisible3, Converter={StaticResource BooleanToVisibilityConverter}}"  >
                            <!--<Polygon.RenderTransform>
                                <TranslateTransform x:Name="triangleTransform2" />
                            </Polygon.RenderTransform>-->
                        </Polygon>

                    </Grid>
                </Border>
                <Polygon Points="25,50 40,40 10,40" Fill="Green"  Visibility="{Binding IsVisible4, Converter={StaticResource BooleanToVisibilityConverter}}" >
                    <!--<Polygon.RenderTransform>
                        <TranslateTransform x:Name="triangleTransform3" />
                    </Polygon.RenderTransform>-->
                </Polygon>
            </Grid>
        </Border>
        
    </Canvas>
</UserControl>
