# N2 Purge 智能传送带控制系统
## 软件架构分析与数据流设计

---

## 📋 目录
1. [项目概述与技术特点](#1-项目概述)
2. [系统架构设计](#2-系统分层架构)
3. [核心模块分析](#3-核心模块分析)
4. [数据流程设计](#4-核心数据流程)
5. [关键技术实现](#5-关键技术实现)
6. [设计模式应用](#6-设计模式应用)
7. [性能优化策略](#7-性能优化策略)
8. [部署架构方案](#8-部署架构方案)
9. [技术亮点总结](#9-技术亮点总结)

---

## 1. 项目概述

### 🎯 系统定位
- **工业自动化传送带控制系统**
- **基于.NET 8.0 + WPF技术栈**
- **实时监控与智能控制平台**

### 🔧 核心技术栈
- **前端**: WPF + MVVM模式
- **后端**: C# 12.0 (.NET 8.0)
- **数据库**: MySQL 8.0
- **通信**: PLC通信 + WCF + SignalR

### ⭐ 关键特性
- 🔄 **实时监控**: 150ms间隔PLC数据读取
- 🤖 **智能控制**: 状态机驱动的货物流转
- 📊 **数据管理**: 完整的数据持久化方案
- 🎮 **录制回放**: 创新的操作自动化功能
- 👥 **权限管理**: 多级用户权限控制体系
- 🔄 **主备模式**: 多网卡高可用性保障

---

## 2. 系统分层架构

### 🏗️ 分层结构图
```
┌─────────────────────────────────────────┐
│ 表示层 (Presentation Layer)             │
│ MainWindow | ConveyorUI | Management    │
├─────────────────────────────────────────┤
│ 视图模型层 (ViewModel Layer)            │
│ ShelfViewModel | TransferViewModel      │
├─────────────────────────────────────────┤
│ 业务逻辑层 (Business Logic Layer)       │
│ Controllers | Services | Managers       │
├─────────────────────────────────────────┤
│ 数据访问层 (Data Access Layer)          │
│ DbHelper | PlcHelper | CsvHelper        │
├─────────────────────────────────────────┤
│ 基础设施层 (Infrastructure Layer)       │
│ MySQL | PLC Device | File System        │
└─────────────────────────────────────────┘
```

### 🔗 模块依赖关系
```
N2Purge (主程序)
├── DBEntity (数据实体)
├── PlcTools (PLC通信)
├── Proj.Log (日志系统)
├── Proj.RecordReplay (录制回放)
├── CSVTools (CSV处理)
├── WCFClient (WCF通信)
└── EmulatorClient (模拟器客户端)
```

---

## 3. 核心模块分析

### 🔧 传送带控制模块 (Conveyor Control)
- **核心类**: `frmConvryorMain`, `ShelfViewModel`
- **MainLoop后台线程持续监控**
- **状态机模式**: Entry → OnTrans → Exit
- **异步UI更新机制**

### 📊 数据管理模块 (Data Management)
- **核心类**: `DbHelper`, `GlobalData`
- **泛型数据访问层**: `DatabaseHelper<T>`
- **连接池管理与事务控制**
- **配置文件管理**

### 🔌 PLC通信模块 (PLC Communication)
- **核心类**: `PlcHelper`, `PLCTools`
- **实时数据读写**: 150ms间隔
- **自动重连机制**
- **异常处理与超时控制**

### 👥 用户管理模块 (User Management)
- **多级权限控制**: 0-3级
- **密码加密存储**
- **操作权限验证**

### 🎮 录制回放模块 (Record & Replay)
- **全局钩子捕获用户操作**
- **JSON格式存储操作序列**
- **可配置热键系统**

### 🔄 单例检测模块 (Singleton Detection)
- **Mutex本地单例检测**
- **UDP网络广播检测**
- **多网卡主备模式**
- **自动故障转移**

---

## 4. 核心数据流程

### 🔄 PLC数据流
```
PLC设备 → PLC读取 → MainLoop → Process函数 → 状态机 → PLC写入 → PLC设备
```
- **实时监控**: 150ms间隔读取PLC数据
- **状态检测**: 货物到达/离开检测
- **控制指令**: 设备状态控制和参数设置

### 💾 数据库数据流
```
实体层 → 数据库读取 → MySQL数据库 → 数据库写入 → 实体层
```
- **数据持久化**: 货物信息、传送记录、用户数据
- **配置管理**: 点位配置、系统参数
- **历史记录**: 操作日志、报警记录

### 🖥️ UI数据流
```
用户输入 → UI事件处理 → ViewModel更新 → UI刷新 → 用户输入
```
- **事件驱动**: 鼠标点击、键盘输入、右键菜单
- **数据绑定**: MVVM模式，自动UI更新
- **实时反馈**: 状态变化即时显示

### ⚙️ 业务逻辑数据流
```
Process → 状态机 → 传送路径 → 时间跟踪 → 数据库写入
```
- **状态管理**: Entry → OnTrans → Exit状态转换
- **路径跟踪**: 完整的货物传送路径记录
- **时间记录**: 精确的到达/离开时间

### 🌐 网络检测数据流
```
UDP广播 → 多网卡发送 → 网络传输 → 目标接收 → 回复确认 → 主备判断
```
- **多网卡支持**: 自动检测所有网络接口
- **广播机制**: 同网段设备发现
- **主备切换**: 自动故障转移

---

## 5. 关键技术实现

### 🔄 MainLoop机制
- **后台线程持续监控**
- **状态机驱动处理**
- **异步UI更新机制**
- **完善的异常处理**
- **性能优化策略**

### 📱 MVVM数据绑定
- **INotifyPropertyChanged接口**
- **双向数据绑定**
- **命令模式实现**
- **属性变更通知**
- **UI与业务逻辑分离**

### ⚡ 异步编程模式
- **async/await模式**
- **Task并行处理**
- **Dispatcher线程调度**
- **非阻塞UI操作**
- **异常传播处理**

### 🎯 状态机设计
- **Entry → OnTrans → Exit**
- **状态转换控制**
- **事件驱动机制**
- **异常状态处理**

### 🔐 安全机制
- **多级权限控制**
- **操作权限验证**
- **数据加密存储**
- **审计日志记录**

### 🌐 网络通信技术
- **UDP广播通信**
- **多网卡自动检测**
- **网段智能识别**
- **主备自动切换**

---

## 6. 设计模式应用

### 🎨 MVVM模式
- **Model**: 数据实体 (DBEntity)
- **View**: XAML界面文件
- **ViewModel**: ShelfViewModel, TransferViewModel等

### 🔄 单例模式
- **GlobalData**: 全局数据管理
- **PLCTools.Instance**: PLC工具实例
- **WCFClient.Instance**: WCF客户端实例

### 👁️ 观察者模式
- **INotifyPropertyChanged**: 属性变更通知
- **事件驱动**: ControlLeftClick, ShelfLeftDbClick

### 🏭 工厂模式
- **DatabaseHelper<T>**: 泛型数据访问工厂
- **ObjectContent**: 控件内容工厂

### 📋 策略模式
- **不同的PLC通信策略**
- **多种数据导出策略**

### 🔗 依赖注入
- **接口驱动开发**
- **模块解耦设计**
- **可测试性提升**

### 🎯 状态模式
- **ActionState状态机**
- **状态转换逻辑**
- **行为封装**

---

## 7. 性能优化策略

### 🚀 数据库优化
- **连接池管理**: 复用数据库连接
- **异步操作**: 非阻塞数据访问
- **缓存机制**: 热点数据缓存
- **批量处理**: 大量数据优化
- **索引优化**: 查询性能提升

### ⚡ UI性能优化
- **虚拟化**: 大数据集显示优化
- **异步更新**: 避免UI线程阻塞
- **数据绑定优化**: 减少不必要的更新
- **控件复用**: 减少内存占用
- **延迟加载**: 按需加载数据

### 🔧 PLC通信优化
- **批量读写**: 减少通信次数
- **数据缓存**: 避免重复读取
- **异常重连**: 自动恢复机制
- **超时控制**: 避免长时间等待
- **并发控制**: 多线程安全

### 💾 内存管理
- **对象池**: 减少GC压力
- **弱引用**: 避免内存泄漏
- **及时释放**: 资源管理
- **监控工具**: 性能分析

### 🌐 网络优化
- **UDP通信优化**: 减少网络开销
- **多网卡负载均衡**: 提高通信效率
- **智能重连**: 网络故障自动恢复

---

## 8. 部署架构方案

### 🏗️ 单机部署
- **应用程序**: N2Purge.exe
- **数据库**: 本地MySQL实例
- **PLC设备**: 直连通信
- **适用场景**: 小型生产线

### 🌐 分布式部署
- **主控节点**: 核心业务逻辑
- **数据节点**: MySQL集群
- **通信节点**: PLC网关
- **监控节点**: 实时监控

### ☁️ 云端部署
- **容器化**: Docker部署
- **微服务**: 模块化拆分
- **负载均衡**: 高可用保障
- **数据同步**: 多地备份

### 🔒 安全部署
- **网络隔离**: 内网部署
- **访问控制**: VPN接入
- **数据加密**: 传输安全
- **备份策略**: 数据保护

### 📊 监控运维
- **日志收集**: 集中化管理
- **性能监控**: 实时告警
- **自动部署**: CI/CD流程
- **故障恢复**: 快速响应

---

## 9. 技术亮点总结

### 🌟 创新特性
- **录制回放功能**: 全局钩子技术
- **实时监控系统**: 150ms精度
- **智能状态机**: 自动化流程控制
- **多级权限管理**: 安全可控
- **多网卡主备模式**: 高可用性保障

### 🔧 技术优势
- **现代化技术栈**: .NET 8.0 + WPF
- **模块化设计**: 高内聚低耦合
- **异步编程**: 高性能响应
- **设计模式**: 可维护性强

### 📈 性能表现
- **实时性**: 毫秒级响应
- **稳定性**: 7×24小时运行
- **可扩展性**: 模块化架构
- **可维护性**: 清晰的代码结构

### 🎯 业务价值
- **提升效率**: 自动化控制
- **降低成本**: 减少人工干预
- **保障质量**: 精确监控
- **数据驱动**: 决策支持

### 🚀 未来规划
- **云原生架构**: 微服务化
- **AI集成**: 智能预测
- **移动端支持**: 跨平台
- **国际化**: 多语言支持

---

## 🎯 总结

### 📋 架构特点
1. **分层清晰**: 严格的分层架构，职责明确
2. **模块化**: 高内聚低耦合的模块设计
3. **可扩展**: 基于接口的可扩展架构
4. **高性能**: 异步编程和优化策略
5. **可维护**: 清晰的代码结构和文档
6. **高可用**: 多网卡主备模式保障

### 🎯 技术价值
- **工业级稳定性**: 7×24小时稳定运行
- **实时性保障**: 毫秒级响应时间
- **智能化控制**: 状态机驱动的自动化
- **数据驱动**: 完整的数据分析能力
- **高可用性**: 主备自动切换机制

### 🚀 创新亮点
- **录制回放技术**: 全局钩子实现操作自动化
- **多网卡主备模式**: 高可用性保障
- **智能状态机**: 自适应的流程控制
- **模块化架构**: 易于扩展和维护
- **实时监控**: 150ms精度的PLC数据采集

---

**N2 Purge 智能传送带控制系统**  
**软件架构分析完成**

**欢迎交流讨论**

---

*文档生成时间: 2024年8月5日*  
*存储位置: N2 Purge\架构分析文档\*  
*文档版本: v1.0*
