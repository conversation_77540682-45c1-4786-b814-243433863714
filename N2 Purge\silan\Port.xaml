﻿<UserControl x:Class="N2Purge.silan.Port"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.silan"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid >
        <Border BorderThickness="1" BorderBrush="Black">
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Border BorderThickness="1" x:Name="bdlp" BorderBrush="Black" Grid.Column="0"  MouseLeftButtonDown="Border_MouseDown" DataContext="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.Lp}">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding PortColor}" Value="0">
                                    <Setter Property="Background" Value="Snow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="1">
                                    <Setter Property="Background" Value="Green"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="2">
                                    <Setter Property="Background" Value="Yellow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="3">
                                    <Setter Property="Background" Value="LightBlue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="4">
                                    <Setter Property="Background" Value="Gray"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Border.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="Input Mode" Click="MenuItem_Click"/>
                            <MenuItem Header="Output Mode" Click="LpOm_Click"/>
                            <MenuItem Header="Port Info" Click="LpPI_Click"/>
                            <MenuItem Header="Transfer Carrier" Click="LpTc_Click"/>
                            <MenuItem Header="Install Carrier" Click="LpIc_Click"/>
                            <MenuItem Header="Remove Carrier" Click="LpRc_Click"/>
                            <MenuItem Header="Enable" Click="LpEnable_Click"/>
                            <MenuItem Header="Disable" Click="LpDisable_Click"/>
                            <MenuItem Header="Set Port Speed" Click="LpSps_Click"/>
                            <MenuItem Header="IO Monitor" Click="LpIo_Click"/>
                            <MenuItem Header="Clear Reserved" Click="LpCr_Click"/>
                        </ContextMenu>
                    </Border.ContextMenu>
                </Border>
                <Border BorderThickness="1" x:Name="bdbd" BorderBrush="Black" Grid.Column="1"  MouseLeftButtonDown="Border1_MouseDown" DataContext="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.Bd}">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding PortColor}" Value="0">
                                    <Setter Property="Background" Value="Snow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="1">
                                    <Setter Property="Background" Value="Green"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="2">
                                    <Setter Property="Background" Value="Yellow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="3">
                                    <Setter Property="Background" Value="LightBlue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="4">
                                    <Setter Property="Background" Value="Gray"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Border.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="Input Mode" Click="MenuItem_Click"/>
                            <MenuItem Header="Output Mode" Click="LpOm_Click"/>
                            <MenuItem Header="Port Info" Click="LpPI_Click"/>
                            <MenuItem Header="Transfer Carrier" Click="LpTc_Click"/>
                            <MenuItem Header="Install Carrier" Click="LpIc_Click"/>
                            <MenuItem Header="Remove Carrier" Click="LpRc_Click"/>
                            <MenuItem Header="Enable" Click="LpEnable_Click"/>
                            <MenuItem Header="Disable" Click="LpDisable_Click"/>
                            <MenuItem Header="Set Port Speed" Click="LpSps_Click"/>
                            <MenuItem Header="IO Monitor" Click="LpIo_Click"/>
                            <MenuItem Header="Clear Reserved" Click="LpCr_Click"/>
                        </ContextMenu>
                    </Border.ContextMenu>
                 
                </Border>
                <Border BorderThickness="1" x:Name="bdop" BorderBrush="Black" Grid.Column="2" MouseLeftButtonDown="Border2_MouseDown" DataContext="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.Op}">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding PortColor}" Value="0">
                                    <Setter Property="Background" Value="Snow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="1">
                                    <Setter Property="Background" Value="Green"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="2">
                                    <Setter Property="Background" Value="Yellow"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="3">
                                    <Setter Property="Background" Value="LightBlue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding PortColor}" Value="4">
                                    <Setter Property="Background" Value="Gray"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Border.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="Input Mode" Click="MenuItem_Click"/>
                            <MenuItem Header="Output Mode" Click="LpOm_Click"/>
                            <MenuItem Header="Port Info" Click="LpPI_Click"/>
                            <MenuItem Header="Transfer Carrier" Click="LpTc_Click"/>
                            <MenuItem Header="Install Carrier" Click="LpIc_Click"/>
                            <MenuItem Header="Remove Carrier" Click="LpRc_Click"/>
                            <MenuItem Header="Enable" Click="LpEnable_Click"/>
                            <MenuItem Header="Disable" Click="LpDisable_Click"/>
                            <MenuItem Header="Set Port Speed" Click="LpSps_Click"/>
                            <MenuItem Header="IO Monitor" Click="LpIo_Click"/>
                            <MenuItem Header="Clear Reserved" Click="LpCr_Click"/>
                        </ContextMenu>
                    </Border.ContextMenu>
               
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
