﻿<UserControl x:Class="N2Purge.frmUserControl.frmCarrier"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Label  Content="Id" Grid.Column="0" HorizontalContentAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center"/>
                <TextBox Grid.Column="1" Background="White" Text="111" Width="100" Margin="3,10,3,10" VerticalAlignment="Stretch" HorizontalAlignment="Center" TextBlock.TextAlignment="Center" VerticalContentAlignment="Center"/>
                <Button VerticalAlignment="Stretch" x:Name="btnsearch" Grid.Column="2" Margin="3,10,3,10" Background="Snow"  Content="查询" Click="btnsearch_Click"/>
                <Button x:Name="btnDelete" Grid.Column="3" Background="Snow"  Margin="3,10,3,10" Content="删除" Click="btnDelete_Click"/>
            </Grid>
        </Border>
        <DataGrid Grid.Row="1" CanUserAddRows="False" AutoGenerateColumns="False" x:Name="dgv"> 
            <!--<DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="Cancel"/>
                    <MenuItem Header="Abort"/>
                    <MenuItem Header="Update Priority"/>
                </ContextMenu>
            </DataGrid.ContextMenu>-->
            <DataGrid.Columns>
                <DataGridTextColumn Header="Id" Binding="{Binding Id}" Width="1*"/>
                <DataGridTextColumn Header="LocationName" Binding="{Binding LocationName}" Width="1*"/>
                <DataGridTextColumn Header="StateName" Binding="{Binding StateName}" Width="1*"/>
                <DataGridTextColumn Header="IDReadStatusName" Binding="{Binding IDReadStatusName}" Width="1*"/>
                <DataGridTextColumn Header="InstallTime" Binding="{Binding InstallTime}" Width="1*"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="1*"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
