using N2Purge.ViewModel;

public class SettingViewModel : VmPropertyChange
{
    private bool _isOnline;
    private bool _isLocal;
    private bool _isAuto;
    private string _scMode = "Normal"; // Normal, Maintenance, Test, Simulation

    public bool IsOnline
    {
        get => _isOnline;
        set { _isOnline = value; OnPropertyChanged("IsOnline"); }
    }

    public bool IsLocal
    {
        get => _isLocal;
        set { _isLocal = value; OnPropertyChanged("IsLocal"); }
    }

    public bool IsAuto
    {
        get => _isAuto;
        set { _isAuto = value; OnPropertyChanged("IsAuto"); }
    }

    public string SCMode
    {
        get => _scMode;
        set { _scMode = value; OnPropertyChanged("SCMode"); }
    }

    //public ICommand OKCommand { get; }
    //public ICommand CancelCommand { get; }

    //public SettingViewModel()
    //{
    //    OKCommand = new RelayCommand(OnOK);
    //    CancelCommand = new RelayCommand(OnCancel);
    //}

    //private void OnOK()
    //{
    //    // 在这里实现确认逻辑
    //    // 例如：保存设置到配置文件或数据库
    //}

    //private void OnCancel()
    //{
    //    // 在这里实现取消逻辑
    //    // 例如：恢复原始设置
    //}

   
}