# N2 Purge 单例模式和自动计划任务功能说明

## 📋 功能概述

N2 Purge 项目已集成单例模式管理和自动计划任务功能，确保系统中只有一个实例运行，并自动创建每5分钟执行一次的监控任务。

## 🔒 单例模式功能

### 工作原理
1. **Mutex机制**: 使用命名Mutex确保全局唯一性
2. **主实例检测**: 第一个启动的实例成为主实例
3. **自动退出**: 后续实例检测到主实例后自动退出
4. **强制终止**: 支持强制终止其他实例（管理功能）

### 技术实现
- **Mutex名称**: `Global\N2Purge_{应用程序路径哈希}`
- **超时检测**: 3秒等待时间
- **异常处理**: 异常情况下默认允许运行
- **资源清理**: 应用退出时自动释放Mutex

### 关键特性
- ✅ **全局唯一**: 基于应用程序路径的全局Mutex
- ✅ **快速检测**: 3秒内完成实例检测
- ✅ **异常容错**: 网络或系统异常时不阻止启动
- ✅ **资源管理**: 自动清理系统资源

## ⏰ 自动计划任务功能

### 任务配置
- **任务名称**: N2PurgeAutoMonitor
- **执行间隔**: 每5分钟
- **执行时长**: 最大10分钟
- **失败重试**: 3次，间隔1分钟
- **权限级别**: 最高可用权限

### 工作流程
1. **启动检查**: 程序启动时自动检查任务是否存在
2. **自动创建**: 如果任务不存在则自动创建
3. **权限提升**: 需要管理员权限时自动提示
4. **后台执行**: 异步执行，不阻塞主程序启动

### 任务特性
- 🔄 **定期执行**: 每5分钟自动启动程序检查
- 🛡️ **单实例保护**: 多实例策略设为"忽略新实例"
- ⚡ **快速启动**: 系统可用时立即启动
- 🔋 **电源友好**: 支持电池模式运行

## 🎮 使用方法

### 自动功能
程序启动时会自动执行以下检查：
1. **单例检查**: 确保只有一个实例运行
2. **任务检查**: 检查计划任务是否存在
3. **自动创建**: 如果任务不存在则尝试创建

### 手动管理
通过主界面的"AutoTask"按钮进入管理界面：

#### 计划任务管理
- **创建任务**: 手动创建5分钟间隔的监控任务
- **删除任务**: 移除自动监控任务
- **查看状态**: 实时显示任务状态和详细信息
- **打开任务计划程序**: 直接打开Windows任务计划程序

#### 单例管理
- **检查实例**: 查看当前运行的实例数量
- **终止其他实例**: 强制终止其他N2Purge实例
- **实例信息**: 显示主实例状态和其他实例数量

## 🔧 管理界面功能

### 状态监控
- **任务状态**: 实时显示计划任务是否启用
- **单例状态**: 显示当前实例是否为主实例
- **系统信息**: 显示应用路径、用户权限、运行时长等
- **自动刷新**: 每30秒自动刷新状态信息

### 操作功能
- **一键创建**: 自动创建并配置计划任务
- **安全删除**: 确认后删除计划任务
- **实例管理**: 检查和终止其他实例
- **日志查看**: 实时显示操作日志

### 系统信息
- **应用程序路径**: 当前执行文件位置
- **当前用户**: 运行程序的用户账户
- **管理员权限**: 是否具有管理员权限
- **启动时间**: 程序启动时间
- **运行时长**: 程序运行总时长

## ⚙️ 配置说明

### 单例配置
```csharp
// SingletonManager.cs 中的配置
private readonly string _mutexName;  // 基于应用路径生成的唯一名称
private static readonly TimeSpan _waitTimeout = TimeSpan.FromSeconds(3);
```

### 计划任务配置
```csharp
// AutoTaskManager.cs 中的配置
private const string TASK_NAME = "N2PurgeAutoMonitor";           // 任务名称
private const int INTERVAL_MINUTES = 5;                          // 执行间隔(分钟)
private const string EXECUTION_TIME_LIMIT = "PT10M";             // 最大执行时间
private const int RETRY_COUNT = 3;                               // 重试次数
```

### XML任务配置
```xml
<Triggers>
  <TimeTrigger>
    <Repetition>
      <Interval>PT5M</Interval>        <!-- 5分钟间隔 -->
      <Duration>P1D</Duration>         <!-- 持续1天 -->
    </Repetition>
  </TimeTrigger>
</Triggers>
<Settings>
  <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>  <!-- 忽略新实例 -->
  <ExecutionTimeLimit>PT10M</ExecutionTimeLimit>                <!-- 10分钟超时 -->
</Settings>
```

## 🔍 故障排除

### 单例问题
1. **Mutex创建失败**: 检查系统权限和资源限制
2. **实例检测错误**: 查看日志中的详细错误信息
3. **强制终止失败**: 确保具有足够的进程管理权限

### 计划任务问题
1. **创建失败**: 确保以管理员身份运行
2. **任务不执行**: 检查任务计划程序中的任务状态
3. **权限不足**: 右键选择"以管理员身份运行"

### 权限问题
1. **管理员权限**: 创建计划任务需要管理员权限
2. **用户权限**: 单例检查在普通用户权限下也可正常工作
3. **权限提升**: 程序会自动提示权限提升

## 📊 日志和监控

### 日志类型
- **操作日志**: 记录所有用户操作和系统状态变化
- **异常日志**: 记录错误和异常信息
- **调试日志**: 详细的执行过程信息

### 监控指标
- **实例数量**: 当前运行的实例数量
- **任务状态**: 计划任务的启用状态
- **执行历史**: 任务的执行历史记录
- **系统资源**: 内存和CPU使用情况

### 日志位置
```
应用程序目录/Logs/
├── Operation.log     # 操作日志
├── Exception.log     # 异常日志
└── Debug.log        # 调试日志
```

## 🚀 最佳实践

### 部署建议
1. **首次部署**: 以管理员身份运行，确保计划任务创建成功
2. **权限设置**: 确保应用程序具有必要的文件和网络权限
3. **防火墙配置**: 如果使用网络检测，确保防火墙允许UDP通信

### 运维建议
1. **定期检查**: 通过管理界面定期检查任务状态
2. **日志监控**: 定期查看日志文件，及时发现问题
3. **权限维护**: 确保用户账户权限不变

### 安全建议
1. **权限最小化**: 只在必要时使用管理员权限
2. **实例控制**: 谨慎使用"终止其他实例"功能
3. **任务管理**: 定期检查计划任务的安全性

## 📈 性能影响

### 启动性能
- 单例检查延迟: < 3秒
- 任务检查延迟: < 2秒 (异步执行)
- 内存占用: < 5MB (检测过程)

### 运行时性能
- 后台任务: 每5分钟启动一次
- 资源占用: 最小化系统资源使用
- 网络影响: 无持续网络活动

### 系统影响
- 计划任务: 1个系统计划任务
- 进程数量: 确保只有1个主进程
- 文件句柄: 最小化文件句柄使用

## 🔄 版本兼容性

### 支持系统
- Windows 10 及以上版本
- Windows Server 2016 及以上版本
- .NET 8.0 运行时环境

### 功能兼容性
- 向后兼容旧版本配置
- 自动升级检测机制
- 平滑迁移支持

这个功能确保了N2Purge系统的稳定性和可靠性，通过自动化管理减少了人工干预的需求。
