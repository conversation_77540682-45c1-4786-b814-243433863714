﻿<UserControl x:Class="N2Purge.userControls.Port"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="100">
    <Grid>
       
        <Border BorderThickness="1" BorderBrush="Black" >
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                <!-- Top Rectangle -->
                <Grid Grid.Row="0" >
                    <Rectangle Fill="{Binding TopFill}" Stroke="Black"/>
                    <TextBlock Text="{Binding TopText}" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Bold"/>
                </Grid>

                <!-- Middle Rectangle -->
                <Grid Grid.Row="1">
                    <Rectangle Fill="{Binding MiddleFill}" Stroke="Black"/>
                    <!--<Path Fill="{Binding MiddleFill}" Stroke="Black">
                        <Path.Data>
                            <PathGeometry>
                                <PathFigure StartPoint="20,0">
                                    --><!-- 顶边起点 --><!--
                                    <LineSegment Point="80,0" />
                                    --><!-- 顶边终点 --><!--
                                    <LineSegment Point="100,50" />
                                    --><!-- 右边 --><!--
                                    <LineSegment Point="0,50" />
                                    --><!-- 底边 --><!--
                                    <LineSegment Point="20,0" />
                                    --><!-- 左边 --><!--
                                </PathFigure>
                            </PathGeometry>
                        </Path.Data>
                    </Path>-->
                    <TextBlock Text="{Binding MiddleText}" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Bold"/>
                </Grid>

                <!-- Bottom Rectangle -->
                <Grid Grid.Row="2">
                    <Rectangle Fill="{Binding BottomFill}" Stroke="Black"/>
                    <TextBlock Text="{Binding BottomText}" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Bold" Foreground="White"/>
                </Grid>
                <Grid Grid.Row="3">
                    <Button x:Name="btn" Content="More" Click="btn_Click"/>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
