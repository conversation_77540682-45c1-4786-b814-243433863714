﻿<UserControl x:Class="N2Purge.frmUserControl.FrmLoopRun"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" Loaded="UserControl_Loaded">
    <Grid>
        <GroupBox Header="Loop Settings" Margin="10">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Carrier ID and Cycle Times -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="Carrier ID:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox x:Name="cmbCarrierID" Grid.Column="1" Margin="0,5"/>
                    
                    <TextBlock Text="Cycle Times:" Grid.Column="2" VerticalAlignment="Center" Margin="20,0,10,0"/>
                    <TextBox x:Name="numericCycleTimes" Grid.Column="3" Margin="0,5"/>
                </Grid>

                <!-- Location Lists -->
                <Grid Grid.Row="2" Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="Available Location List:"/>
                        <ListBox x:Name="lstAllLocation" Grid.Row="1" Margin="0,5,0,0"/>
                    </Grid>

                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                        <Button x:Name="btnMoveRight" Background="Snow" Content=">>" Margin="5" Width="50" Click="btnMoveRight_Click"/>
                        <Button x:Name="btnMoveLeft" Background="Snow" Content="&lt;&lt;" Margin="5" Width="50" Click="btnMoveLeft_Click"/>
                        <Button x:Name="btnSelectAll" Background="Snow" Content="Select all" Margin="5" Width="70" Click="btnSelectAll_Click"/>
                        <Button x:Name="btnClear" Background="Snow" Content="Clear all" Margin="5" Width="70" Click="btnClear_Click"/>
                    </StackPanel>

                    <Grid Grid.Column="2">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="Selected Location For Loop:"/>
                        <ListBox x:Name="lstSelectedLocation" Grid.Row="1" Margin="0,5,0,0"/>
                    </Grid>
                </Grid>

                <!-- Double Check and Test Mode -->
                <StackPanel Grid.Row="3" Margin="0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="Carrier ID (Double Check):" VerticalAlignment="Center"/>
                        <ComboBox x:Name="cmbCarrierToUse" Background="Snow" Grid.Column="1" Margin="10,5"/>
                    </Grid>
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="Cycle Test Mode:" VerticalAlignment="Center"/>
                        <ComboBox x:Name="cmbCycleMode" Grid.Column="1" Margin="10,5"/>
                    </Grid>
                </StackPanel>

                <!-- Port Selection -->
                <Grid Grid.Row="4" Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <CheckBox x:Name="chkIsUsePort" Content="Use Port" VerticalAlignment="Center"/>
                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                        <RadioButton x:Name="rbnPortA" Content="50001" Margin="0,0,10,0"/>
                        <RadioButton x:Name="rbnPortB" Content="PortB"/>
                    </StackPanel>
                </Grid>

                <!-- Buttons -->
                <StackPanel Grid.Row="4" HorizontalAlignment="Right" Orientation="Horizontal">
                    <Button x:Name="btnSave" Background="Snow" Content="保存" Width="80" Height="30" Margin="5" Click="btnSave_Click"/>
                    <Button x:Name="btnStart" Background="Snow" Content="开始" Width="80" Height="30" Margin="5" Click="btnStart_Click"/>
                    <Button x:Name="btnStop" Content="停止" Background="Snow" Width="80" Height="30" Margin="5" Click="btnStop_Click"/>
                </StackPanel>
            </Grid>
        </GroupBox>
    </Grid>
</UserControl>
