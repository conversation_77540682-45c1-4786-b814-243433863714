﻿<UserControl x:Class="N2Purge.Conveyor.frmCVIO"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.Conveyor"
             xmlns:localctr="clr-namespace:N2Purge.silan"
             xmlns:userControls="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="2*"/>
        </Grid.RowDefinitions>
        <localctr:frmInfo x:Name="storageinfo" Grid.Row="0"/>
        <DataGrid  Grid.Row="1" x:Name="dgv" CanUserAddRows="False" BorderThickness="1" BorderBrush="Black" AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="CVNmae" Binding="{Binding CVNmae}" Width="1*"/>
                <DataGridTextColumn Header="CVIoNmae" Binding="{Binding CVIoNmae}" Width="1*"/>
                <DataGridTemplateColumn Header="CVIoValue" Width="1*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <userControls:CmdStatus DataContext="{Binding StatusViewModel}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
