@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置变量
set "TASK_NAME=N2PurgeAutoMonitor"
set "APP_NAME=N2Purge"
set "CURRENT_DIR=%~dp0"
set "APP_PATH=%CURRENT_DIR%..\bin\Debug\N2Purge.exe"

:: 显示标题
echo ========================================
echo    N2 Purge 快速设置工具
echo ========================================
echo.
echo 此工具将自动配置N2Purge的单例模式和自动监控任务
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] 检测到当前不是管理员权限
    echo.
    echo 为了完整设置计划任务，建议以管理员身份运行此脚本
    echo 但程序的单例模式功能在普通权限下也能正常工作
    echo.
    set /p continue=是否继续设置？(Y/N): 
    if /i "!continue!" neq "Y" (
        echo 设置已取消
        pause
        exit /b 0
    )
    echo.
) else (
    echo [信息] 管理员权限检查通过
    echo.
)

:: 检查应用程序文件
if not exist "%APP_PATH%" (
    echo [错误] 找不到应用程序文件: %APP_PATH%
    echo.
    echo 请确保：
    echo 1. N2Purge.exe 文件存在于 bin\Debug 目录中
    echo 2. 或者修改此脚本中的 APP_PATH 变量指向正确路径
    echo.
    pause
    exit /b 1
)

echo [信息] 应用程序文件检查通过: %APP_PATH%
echo.

:: 显示功能说明
echo ========================================
echo 功能说明：
echo ========================================
echo.
echo 1. 单例模式管理
echo    - 确保系统中只有一个N2Purge实例运行
echo    - 使用Mutex机制进行全局实例控制
echo    - 后续实例自动检测并退出
echo.
echo 2. 自动监控任务
echo    - 创建每5分钟执行一次的计划任务
echo    - 确保系统持续监控和自动恢复
echo    - 支持失败重试和异常处理
echo.
echo 3. 管理界面
echo    - 通过主程序的"AutoTask"按钮访问
echo    - 实时状态监控和手动管理功能
echo    - 详细的操作日志和系统信息
echo.

:: 确认开始设置
set /p confirm=是否开始自动设置？(Y/N): 
if /i "%confirm%" neq "Y" (
    echo 设置已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo 开始设置...
echo ========================================
echo.

:: 步骤1：检查现有任务
echo [步骤 1/4] 检查现有计划任务...
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo [信息] 发现现有任务，将先删除后重新创建
    schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1
    if %errorLevel% equ 0 (
        echo [成功] 现有任务已删除
    ) else (
        echo [警告] 删除现有任务失败，继续创建新任务
    )
) else (
    echo [信息] 未发现现有任务，准备创建新任务
)
echo.

:: 步骤2：创建临时XML配置
echo [步骤 2/4] 生成任务配置...
set "TEMP_XML=%TEMP%\%TASK_NAME%_setup.xml"
set "WORK_DIR=%~dp1"
set "WORK_DIR=%WORK_DIR:~0,-1%"

(
echo ^<?xml version="1.0" encoding="UTF-16"?^>
echo ^<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task"^>
echo   ^<RegistrationInfo^>
echo     ^<Date^>%date%T%time%^</Date^>
echo     ^<Author^>%USERNAME%^</Author^>
echo     ^<Description^>N2 Purge 智能传送带控制系统自动监控任务 - 每5分钟检查一次^</Description^>
echo   ^</RegistrationInfo^>
echo   ^<Triggers^>
echo     ^<TimeTrigger^>
echo       ^<Enabled^>true^</Enabled^>
echo       ^<StartBoundary^>%date:~0,4%-%date:~5,2%-%date:~8,2%T%time:~0,2%:%time:~3,2%:00^</StartBoundary^>
echo       ^<Repetition^>
echo         ^<Interval^>PT5M^</Interval^>
echo         ^<Duration^>P1D^</Duration^>
echo         ^<StopAtDurationEnd^>false^</StopAtDurationEnd^>
echo       ^</Repetition^>
echo     ^</TimeTrigger^>
echo   ^</Triggers^>
echo   ^<Principals^>
echo     ^<Principal id="Author"^>
echo       ^<UserId^>%USERDOMAIN%\%USERNAME%^</UserId^>
echo       ^<LogonType^>InteractiveToken^</LogonType^>
echo       ^<RunLevel^>HighestAvailable^</RunLevel^>
echo     ^</Principal^>
echo   ^</Principals^>
echo   ^<Settings^>
echo     ^<MultipleInstancesPolicy^>IgnoreNew^</MultipleInstancesPolicy^>
echo     ^<DisallowStartIfOnBatteries^>false^</DisallowStartIfOnBatteries^>
echo     ^<StopIfGoingOnBatteries^>false^</StopIfGoingOnBatteries^>
echo     ^<AllowHardTerminate^>true^</AllowHardTerminate^>
echo     ^<StartWhenAvailable^>true^</StartWhenAvailable^>
echo     ^<RunOnlyIfNetworkAvailable^>false^</RunOnlyIfNetworkAvailable^>
echo     ^<AllowStartOnDemand^>true^</AllowStartOnDemand^>
echo     ^<Enabled^>true^</Enabled^>
echo     ^<Hidden^>false^</Hidden^>
echo     ^<RunOnlyIfIdle^>false^</RunOnlyIfIdle^>
echo     ^<WakeToRun^>false^</WakeToRun^>
echo     ^<ExecutionTimeLimit^>PT10M^</ExecutionTimeLimit^>
echo     ^<Priority^>7^</Priority^>
echo     ^<RestartOnFailure^>
echo       ^<Interval^>PT1M^</Interval^>
echo       ^<Count^>3^</Count^>
echo     ^</RestartOnFailure^>
echo   ^</Settings^>
echo   ^<Actions Context="Author"^>
echo     ^<Exec^>
echo       ^<Command^>%APP_PATH%^</Command^>
echo       ^<WorkingDirectory^>%WORK_DIR%^</WorkingDirectory^>
echo     ^</Exec^>
echo   ^</Actions^>
echo ^</Task^>
) > "%TEMP_XML%"

echo [成功] 任务配置文件已生成
echo.

:: 步骤3：创建计划任务
echo [步骤 3/4] 创建计划任务...
schtasks /create /tn "%TASK_NAME%" /xml "%TEMP_XML%" >nul 2>&1
if %errorLevel% equ 0 (
    echo [成功] 计划任务创建成功
    echo [信息] 任务名称: %TASK_NAME%
    echo [信息] 执行间隔: 每5分钟
    echo [信息] 应用程序: %APP_PATH%
) else (
    echo [错误] 计划任务创建失败
    echo [提示] 可能的原因：
    echo         1. 权限不足（需要管理员权限）
    echo         2. 任务计划程序服务未启动
    echo         3. 系统策略限制
    echo.
    echo [建议] 请尝试：
    echo         1. 以管理员身份重新运行此脚本
    echo         2. 或通过程序内的AutoTask界面手动创建
)

:: 清理临时文件
if exist "%TEMP_XML%" del "%TEMP_XML%" >nul 2>&1

echo.

:: 步骤4：验证设置
echo [步骤 4/4] 验证设置结果...
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo [成功] 计划任务验证通过
    echo.
    echo ========================================
    echo 设置完成！
    echo ========================================
    echo.
    echo 功能状态：
    echo ✅ 单例模式: 已集成到程序中，启动时自动生效
    echo ✅ 自动任务: 已创建，每5分钟自动执行一次
    echo ✅ 管理界面: 可通过主程序"AutoTask"按钮访问
    echo.
    echo 下一步操作：
    echo 1. 启动 N2Purge 程序验证功能
    echo 2. 通过"AutoTask"按钮查看详细状态
    echo 3. 检查任务计划程序确认任务正常
    echo.
    echo 注意事项：
    echo - 程序会自动检测并确保只有一个实例运行
    echo - 计划任务会每5分钟启动程序进行监控
    echo - 如需修改设置，请使用程序内的管理界面
    echo.
) else (
    echo [警告] 计划任务验证失败
    echo.
    echo ========================================
    echo 部分设置完成
    echo ========================================
    echo.
    echo 功能状态：
    echo ✅ 单例模式: 已集成到程序中，启动时自动生效
    echo ❌ 自动任务: 创建失败，需要手动设置
    echo ✅ 管理界面: 可通过主程序"AutoTask"按钮访问
    echo.
    echo 建议操作：
    echo 1. 以管理员身份重新运行此脚本
    echo 2. 或启动程序后通过"AutoTask"界面手动创建任务
    echo 3. 检查系统权限和任务计划程序服务状态
    echo.
)

echo 感谢使用 N2 Purge 快速设置工具！
echo.
pause
