using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static N2Purge.ViewModel.TransferOrderViewModel;
using System.Windows.Input;
using N2Purge.userControls;
using PlcTools;

namespace N2Purge.ViewModel
{
   public class IOViewModel:VmPropertyChange
    {
        private string _cvname;
        public string CVNmae
        {
            get => _cvname;
            set
            {
                _cvname = value;
                OnPropertyChanged(nameof(CVNmae));
            }
        }

        private string _cvioname;
        public string CVIoNmae
        {
            get => _cvioname;
            set
            {
                _cvioname = value;
                OnPropertyChanged(nameof(CVIoNmae));
            }
        }
        
        private string _cviovalue;
        public string CVIoValue
        {
            get => _cviovalue;
            set
            {
                _cviovalue = value;
                OnPropertyChanged(nameof(CVIoValue));
                
                // 更新StatusViewModel的状态
                if (StatusViewModel != null)
                {
                    // 根据CVIoValue的值设置Status
                    // 值为"1"时表示状态为1（绿色），值为"0"时为0（灰色），其他值为2（红色）
                    if (value == "1")
                        StatusViewModel.Status = 1;  // 绿色 - 正常/开启
                    else if (value == "0")
                        StatusViewModel.Status = 0;  // 灰色 - 关闭/未激活
                    else if (value == "2")
                        StatusViewModel.Status = 2;  // 红色 - 错误
                    else if (value == "3")
                        StatusViewModel.Status = 3;  // 黄色 - 警告
                    else
                        StatusViewModel.Status = 0;  // 默认灰色
                        
                    StatusViewModel.DisplayText = _cvioname;
                }
            }
        }
        
        private IOStatusViewModel _statusViewModel;
        public IOStatusViewModel StatusViewModel
        {
            get 
            { 
                if (_statusViewModel == null)
                {
                    _statusViewModel = new IOStatusViewModel
                    {
                        IoName = _cvioname,
                        DisplayText = _cvioname ?? string.Empty
                    };
                    
                    // 单独设置Status属性，以确保触发UpdateBackgroundColor方法
                    if (_cviovalue == "1")
                        _statusViewModel.Status = 1;
                    else if (_cviovalue == "0")
                        _statusViewModel.Status = 0;
                    else if (_cviovalue == "2")
                        _statusViewModel.Status = 2;
                    else if (_cviovalue == "3")
                        _statusViewModel.Status = 3;
                    else
                        _statusViewModel.Status = 0;
                }
                return _statusViewModel; 
            }
            set
            {
                _statusViewModel = value;
                OnPropertyChanged(nameof(StatusViewModel));
            }
        }
        
        // 确保在设置属性时初始化StatusViewModel
        
        private string _plcTagName;
        public string PLCTagName
        {
            get => _plcTagName;
            set
            {
                _plcTagName = value;
                OnPropertyChanged(nameof(PLCTagName));
            }
        }
        public IOViewModel()
        {
            // 确保StatusViewModel被初始化
            _statusViewModel = new IOStatusViewModel
            {
                IoName = string.Empty,
                DisplayText = string.Empty
            };
            
            // 设置Status属性，这会触发UpdateBackgroundColor方法
            _statusViewModel.Status = 0;
        }
    }
}
