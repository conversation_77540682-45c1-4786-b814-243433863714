using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 单例管理器 - 确保应用程序只有一个实例运行
    /// </summary>
    public class SingletonManager : IDisposable
    {
        private static SingletonManager? _instance;
        private static readonly object _lock = new object();
        private Mutex? _mutex;
        private readonly string _mutexName;
        private bool _disposed = false;

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static SingletonManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new SingletonManager();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 是否为主实例
        /// </summary>
        public bool IsMainInstance { get; private set; }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private SingletonManager()
        {
            // 使用固定的Mutex名称，确保所有N2Purge实例使用相同的Mutex
            _mutexName = "Global\\N2Purge_SingleInstance_Mutex";

            InitializeMutex();
        }

        /// <summary>
        /// 初始化Mutex
        /// </summary>
        private void InitializeMutex()
        {
            try
            {
                Logger.Instance?.OperationLog($"[单例检查] 开始初始化Mutex: {_mutexName}");

                // 创建或获取命名Mutex
                _mutex = new Mutex(true, _mutexName, out bool createdNew);

                if (createdNew)
                {
                    // 当前进程是第一个创建Mutex的，为主实例
                    IsMainInstance = true;
                    Logger.Instance?.OperationLog($"[单例检查] ✅ 当前进程为主实例 (新创建Mutex)");
                }
                else
                {
                    // Mutex已存在，说明有其他实例在运行
                    Logger.Instance?.OperationLog($"[单例检查] ⚠️ Mutex已存在，检测到其他实例");

                    // 尝试等待一小段时间，看是否能获取到Mutex（其他实例可能正在退出）
                    Logger.Instance?.OperationLog("[单例检查] 尝试等待获取Mutex (3秒超时)...");
                    if (_mutex.WaitOne(TimeSpan.FromSeconds(3), false))
                    {
                        IsMainInstance = true;
                        Logger.Instance?.OperationLog("[单例检查] ✅ 成功获取Mutex，当前进程成为主实例");
                    }
                    else
                    {
                        IsMainInstance = false;
                        Logger.Instance?.OperationLog("[单例检查] ❌ 无法获取Mutex，其他实例仍在运行");
                    }
                }

                Logger.Instance?.OperationLog($"[单例检查] 最终结果: IsMainInstance = {IsMainInstance}");
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[单例检查] 初始化Mutex失败: {ex.Message}");
                Logger.Instance?.ExceptionLog($"[单例检查] 异常堆栈: {ex.StackTrace}");
                // 异常情况下默认允许运行
                IsMainInstance = true;
                Logger.Instance?.OperationLog("[单例检查] 异常情况下默认允许运行");
            }
        }

        /// <summary>
        /// 检查是否有其他实例正在运行
        /// </summary>
        /// <returns>如果有其他实例返回true</returns>
        public static bool HasOtherInstance()
        {
            return !Instance.IsMainInstance;
        }

        /// <summary>
        /// 强制退出其他实例（谨慎使用）
        /// </summary>
        /// <returns>是否成功</returns>
        public bool ForceKillOtherInstances()
        {
            try
            {
                string currentProcessName = Process.GetCurrentProcess().ProcessName;
                string currentProcessPath = Assembly.GetExecutingAssembly().Location;
                int currentProcessId = Process.GetCurrentProcess().Id;

                var processes = Process.GetProcessesByName(currentProcessName);
                int killedCount = 0;

                foreach (var process in processes)
                {
                    try
                    {
                        // 跳过当前进程
                        if (process.Id == currentProcessId)
                            continue;

                        // 检查进程路径是否相同
                        string processPath = process.MainModule?.FileName ?? "";
                        if (string.Equals(processPath, currentProcessPath, StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Instance?.OperationLog($"[强制退出] 终止进程: PID={process.Id}, Path={processPath}");
                            process.Kill();
                            process.WaitForExit(5000); // 等待最多5秒
                            killedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance?.ExceptionLog($"[强制退出] 终止进程失败: PID={process.Id}, Error={ex.Message}");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                if (killedCount > 0)
                {
                    Logger.Instance?.OperationLog($"[强制退出] 成功终止 {killedCount} 个其他实例");
                    
                    // 重新初始化Mutex
                    ReleaseMutex();
                    Thread.Sleep(1000); // 等待1秒确保资源释放
                    InitializeMutex();
                }

                return killedCount > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[强制退出] 操作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取其他实例信息
        /// </summary>
        /// <returns>其他实例的进程信息</returns>
        public Process[] GetOtherInstances()
        {
            try
            {
                string currentProcessName = Process.GetCurrentProcess().ProcessName;
                string currentProcessPath = Assembly.GetExecutingAssembly().Location;
                int currentProcessId = Process.GetCurrentProcess().Id;

                var allProcesses = Process.GetProcessesByName(currentProcessName);
                var otherInstances = new List<Process>();

                foreach (var process in allProcesses)
                {
                    try
                    {
                        // 跳过当前进程
                        if (process.Id == currentProcessId)
                        {
                            process.Dispose();
                            continue;
                        }

                        // 检查进程路径是否相同
                        string processPath = process.MainModule?.FileName ?? "";
                        if (string.Equals(processPath, currentProcessPath, StringComparison.OrdinalIgnoreCase))
                        {
                            otherInstances.Add(process);
                        }
                        else
                        {
                            process.Dispose();
                        }
                    }
                    catch
                    {
                        process.Dispose();
                    }
                }

                return otherInstances.ToArray();
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[获取实例] 操作失败: {ex.Message}");
                return Array.Empty<Process>();
            }
        }

        /// <summary>
        /// 释放Mutex
        /// </summary>
        private void ReleaseMutex()
        {
            try
            {
                if (_mutex != null)
                {
                    if (IsMainInstance)
                    {
                        _mutex.ReleaseMutex();
                        Logger.Instance?.OperationLog("[单例管理] Mutex已释放");
                    }
                    _mutex.Dispose();
                    _mutex = null;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[单例管理] 释放Mutex失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                ReleaseMutex();
                _disposed = true;
                Logger.Instance?.OperationLog("[单例管理] 资源已释放");
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~SingletonManager()
        {
            Dispose();
        }
    }
}
