# N2 Purge 网络实例检测和自启动功能说明

## 📋 功能概述

N2 Purge 项目已集成网络实例检测和自启动管理功能，确保在网络环境中只有一个主实例运行，并支持系统开机自动启动。

## 🔍 网络实例检测功能

### 工作原理
1. **UDP广播检测**: 应用启动时通过UDP广播检测网络中是否有其他实例
2. **主实例确认**: 第一个启动的实例成为主实例，后续实例自动退出
3. **实时监听**: 主实例持续监听网络，响应其他实例的检测请求

### 技术实现
- **广播端口**: 50001 (检测请求)
- **响应端口**: 50002 (检测响应)
- **应用标识**: "N2Purge_v1.0"
- **超时时间**: 3秒
- **检测延迟**: 500毫秒

### 关键特性
- ✅ **Debug模式跳过**: 开发调试时不进行网络检测
- ✅ **异常处理**: 网络异常时默认允许启动
- ✅ **资源清理**: 应用退出时自动清理网络资源
- ✅ **日志记录**: 详细的检测过程日志

## 🚀 自启动管理功能

### 支持方式
1. **计划任务方式** (推荐)
   - 更可靠的启动机制
   - 支持启动延迟设置
   - 支持失败重试
   - 需要管理员权限

2. **注册表方式** (备选)
   - 无需管理员权限
   - 兼容性更好
   - 功能相对简单

### 功能特点
- 🔧 **智能选择**: 优先使用计划任务，失败时自动降级到注册表
- ⏰ **启动延迟**: 默认30秒延迟，避免系统启动冲突
- 🔄 **失败重试**: 计划任务支持3次重试，间隔1分钟
- 🛡️ **权限检查**: 自动检测管理员权限并提示
- 📝 **状态查询**: 支持查询当前自启动状态

## 🎮 使用方法

### 应用内设置
1. **快捷键**: 按 `Ctrl+Shift+A` 打开自启动设置对话框
2. **菜单操作**: 根据提示选择设置或移除自启动
3. **权限提升**: 如需管理员权限会自动提示

### 脚本工具设置

#### 批处理脚本 (Windows)
```batch
# 以管理员身份运行
Scripts\SetupAutoStart.bat
```

#### PowerShell脚本 (推荐)
```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\Scripts\SetupAutoStart.ps1
```

#### 命令行参数
```powershell
# 直接设置自启动
.\SetupAutoStart.ps1 -Action setup -StartupDelay 30

# 移除自启动
.\SetupAutoStart.ps1 -Action remove

# 查看状态
.\SetupAutoStart.ps1 -Action status
```

## ⚙️ 配置说明

### 网络检测配置
```csharp
// App.xaml.cs 中的配置
private static readonly string _appIdentifier = "N2Purge_v1.0";  // 应用标识
private static readonly int _broadcastPort = 50001;              // 广播端口
private static readonly int _responsePort = 50002;               // 响应端口
```

### 自启动配置
```csharp
// 默认配置
const string TASK_NAME = "N2PurgeAutoStart";           // 任务名称
const int DEFAULT_STARTUP_DELAY = 30;                  // 启动延迟(秒)
const int RETRY_COUNT = 3;                             // 重试次数
const string RETRY_INTERVAL = "PT1M";                  // 重试间隔
```

## 🔧 故障排除

### 网络检测问题
1. **防火墙阻止**: 确保防火墙允许UDP端口50001和50002
2. **网络隔离**: 检查网络是否支持UDP广播
3. **端口冲突**: 确认端口未被其他应用占用

### 自启动问题
1. **权限不足**: 确保以管理员身份运行设置程序
2. **路径错误**: 检查应用程序路径是否正确
3. **任务被禁用**: 在任务计划程序中检查任务状态

### 日志查看
```csharp
// 日志位置 (通常在应用程序目录下的Logs文件夹)
- 网络检测日志: "网络检测完成，是否为主实例: true/false"
- 自启动日志: "已设置计划任务自启动" / "已设置注册表自启动"
- 错误日志: 详细的异常信息和堆栈跟踪
```

## 📊 状态监控

### 检查方法
1. **应用内检查**: 使用 `Ctrl+Shift+A` 快捷键
2. **任务计划程序**: Windows任务计划程序中查看"N2PurgeAutoStart"任务
3. **注册表检查**: `HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run`
4. **脚本检查**: 运行PowerShell脚本的状态查询功能

### 状态说明
- ✅ **已启用**: 计划任务或注册表项存在且有效
- ❌ **未启用**: 未设置任何自启动方式
- ⚠️ **部分启用**: 只设置了注册表方式(建议升级到计划任务)

## 🛡️ 安全考虑

### 网络安全
- UDP通信仅用于本地网络检测
- 不传输敏感信息
- 支持网络隔离环境

### 系统安全
- 计划任务使用当前用户权限
- 不修改系统关键设置
- 支持完全卸载

## 📈 性能影响

### 启动性能
- 网络检测延迟: < 3.5秒 (正常网络环境)
- 内存占用: < 1MB (检测过程)
- CPU占用: 可忽略不计

### 运行时性能
- 后台监听: 最小资源占用
- 无定期网络活动
- 不影响主程序性能

## 🔄 版本兼容性

### 支持系统
- Windows 10 及以上版本
- Windows Server 2016 及以上版本
- .NET 8.0 运行时环境

### 功能兼容性
- 向后兼容旧版本配置
- 自动升级检测机制
- 平滑迁移支持

## 📞 技术支持

如遇到问题，请检查：
1. 应用程序日志文件
2. Windows事件查看器
3. 网络连接状态
4. 用户权限设置

详细的错误信息将记录在日志中，便于问题诊断和解决。
