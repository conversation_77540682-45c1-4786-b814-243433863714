﻿<UserControl x:Class="N2Purge.silan.frmInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.silan"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300">
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="120"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Cassette ID" Grid.Row="0" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox BorderThickness="1" BorderBrush="LightGray" Text="{Binding CassetteID}" Grid.Row="0" Grid.Column="1" VerticalContentAlignment="Center" VerticalAlignment="Stretch" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Location" Grid.Row="1" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding Location}" Grid.Row="1" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Install Time" Grid.Row="2" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding InstallTime}" Grid.Row="2" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Status" Grid.Row="3" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding Status}" Grid.Row="3" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="ID Status" Grid.Row="4" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding IDStatus}" Grid.Row="4" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>

        <!--<Label BorderThickness="1" BorderBrush="LightGray" Content="Carrier Type" Grid.Row="5" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding CarrierType}" Grid.Row="5" Grid.Column="1" VerticalAlignment="Stretch" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Empty State" Grid.Row="6" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding EmptyState}" Grid.Row="6" Grid.Column="1" VerticalAlignment="Stretch" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Quantity" Grid.Row="7" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding Quantity}" Grid.Row="7" Grid.Column="1" VerticalAlignment="Stretch" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Lot ID" Grid.Row="8" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding LotID}" Grid.Row="8" Grid.Column="1" VerticalAlignment="Stretch" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="CST Test Type" Grid.Row="9" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding CSTTestType}" Grid.Row="9" Grid.Column="1" VerticalAlignment="Center" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Host ID" Grid.Row="10" Grid.Column="0" VerticalAlignment="Center" Margin="5,0"/>
        <TextBox Text="{Binding HostID}" Grid.Row="10" Grid.Column="1" VerticalAlignment="Stretch" Margin="5,0"/>-->

        <!--<Label BorderThickness="1" BorderBrush="LightGray" Content="Location Type" Grid.Row="5" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding LocationType}" Grid.Row="5" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Zone" Grid.Row="6" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding Zone}" Grid.Row="6" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>-->

        <Label BorderThickness="1" BorderBrush="LightGray" Content="Prohibit" Grid.Row="5" Grid.Column="0" VerticalAlignment="Stretch" Margin="5,0"/>
        <TextBox Text="{Binding Prohibit}" Grid.Row="5" Grid.Column="1" VerticalAlignment="Stretch" VerticalContentAlignment="Center" Margin="5,0"/>
    </Grid>
</UserControl>
