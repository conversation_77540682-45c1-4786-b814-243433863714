﻿using N2Purge.frmUserControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.userControls
{
    /// <summary>
    /// CmdControl.xaml 的交互逻辑
    /// </summary>
    public partial class CmdControl : UserControl
    {
        public CmdControl()
        {
            InitializeComponent();
            this.Visibility = Visibility.Visible;
            this.HorizontalAlignment = HorizontalAlignment.Left;
            this.VerticalAlignment = VerticalAlignment.Top;
        }
        public string Cmd = string.Empty;
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(Cmd))
            {
                frmCmdInfo frmCmdInfo = new frmCmdInfo();
              frmCmdInfo.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                frmCmdInfo.Width = 300;
                frmCmdInfo.Height = 300;
                frmCmdInfo.Show();
            }
        }
    }
}
