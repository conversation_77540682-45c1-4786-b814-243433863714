using System;
using System.Collections.ObjectModel;
using System.Windows.Controls;

namespace N2Purge.userControls
{
    public partial class DeviceStatus : UserControl
    {
        public ObservableCollection<DeviceStatusItem> DeviceItems { get; set; }

        public DeviceStatus()
        {
            InitializeComponent();
            DeviceItems = new ObservableCollection<DeviceStatusItem>();
            dgDeviceStatus.ItemsSource = DeviceItems;

            // 添加示例数据
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            DeviceItems.Add(new DeviceStatusItem
            {
                DeviceId = "DEV001",
                DeviceName = "设备1",
                Status = "运行中",
                RunningTime = "12:30:45",
                LastUpdate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            DeviceItems.Add(new DeviceStatusItem
            {
                DeviceId = "DEV002",
                DeviceName = "设备2",
                Status = "待机",
                RunningTime = "08:15:30",
                LastUpdate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });
        }
    }

    public class DeviceStatusItem
    {
        public string DeviceId { get; set; }
        public string DeviceName { get; set; }
        public string Status { get; set; }
        public string RunningTime { get; set; }
        public string LastUpdate { get; set; }
    }
}