﻿<UserControl x:Class="N2Purge.userControls.CmdInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>
            <Label Content="Cassette ID" Background="Blue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <Label Content="Source Location" Background="Blue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <Label Content="Dest Location" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <Label Content="Priority" Background="Blue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <TextBlock Text="{Binding }" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="White" />
            <ComboBox ItemsSource="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <ComboBox ItemsSource="{Binding DestLocation}" Background="Transparent" Grid.Column="2" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
            <TextBlock Text="{Binding Priority}" Background="Transparent" Grid.Column="3" Grid.Row="3" TextBlock.Foreground="White" />
        </Grid>
    </Grid>
</UserControl>
