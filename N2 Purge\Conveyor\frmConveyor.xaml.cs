﻿using DBEntity;
using N2Purge.UserfulClass;
using N2Purge.ViewModel;
using Proj.Log;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.Conveyor
{
    /// <summary>
    /// frmConveyor.xaml 的交互逻辑
    /// </summary>
    public partial class frmConveyor : UserControl
    {
        private Dictionary<string, ShelfViewModel> dicshelf=new Dictionary<string, ShelfViewModel>();
        List<string> strings = new List<string> { "40105", "4010501", "4010502", "4010503", "4010504", "4010505" };
        private bool isLoad=false;
        public frmConveyor()
        {
            if (isLoad) return;
            InitializeComponent();
            for (int i = 0; i < 6; i++)
            {
                ShelfViewModel model = new ShelfViewModel();
                dicshelf.Add(strings[i], model);
                TpResult tpResult = new TpResult();
                previousOccupiedStates.Add(strings[i], tpResult);
            }
            S40105.DataContext = dicshelf["40105"];
            S4010501.DataContext = dicshelf["4010501"];
            S4010502.DataContext = dicshelf["4010502"];
            S4010503.DataContext = dicshelf["4010503"];
            S4010504.DataContext = dicshelf["4010504"];
            S4010505.DataContext = dicshelf["4010505"];
            isLoad = true;
            RefreshDt();
        }
        private static Dictionary<string, TpResult> previousOccupiedStates = new Dictionary<string, TpResult>();
        public void RefreshDt()
        {
            Task.Run(async () => { 
            
             while (true)
                {
                    List<TpLocation> dt = new List<TpLocation>();
                    dt = await GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync();
                    await Task.Delay(100);
                    foreach (TpLocation location in dt) 
                    {
                        if (location.Address=="40105"|| location.Address == "4010501" || location.Address == "4010502" || location.Address == "4010503" || location.Address == "4010504" || location.Address == "4010505" )
                        {
                            if (location.Is_Occupied != previousOccupiedStates[location.Address].Is_Occupied|| location.Is_Prohibited != previousOccupiedStates[location.Address].Is_Prohibited||location.Is_Reserved != previousOccupiedStates[location.Address].Is_Reserved)
                            {
                                if (location.Is_Prohibited == 1)
                                {
                                    dicshelf[location.Address].ShelfColor = 4;
                                }
                                else if (location.Is_Reserved == 1)
                                {
                                    dicshelf[location.Address].ShelfColor = 3;
                                }
                                else if (location.Is_Occupied == 1)
                                {
                                    dicshelf[location.Address].ShelfColor = 1;
                                }
                                else
                                {
                                    dicshelf[location.Address].ShelfColor = 0;
                                }
                            }
                        }
                      
                    }
                }
            
            });
        }
        private const string m_strCraneSign = "(Crane)";
        private async void btnExcute_Click(object sender, RoutedEventArgs e)
        {
            Dictionary<string, object> dicSCParams = new Dictionary<string, object>();
            dicSCParams.Add("NAME", "SC State");

            var strSCState = await WCFClient.Instance.SendMessage("GetState", dicSCParams);
            if (strSCState.ToString() != "Auto")
            {
                lblTransferResult.Text = "Please switch SC State to Auto.";
                return;
            }
           
            var carrierIdTextBox = CarrierId.Text;
            if (carrierIdTextBox != null)
            {
                if (carrierIdTextBox == "")
                {
                    lblTransferResult.Text = "There is no CST in the Source Location.";
                    return;
                }
                GlobalData.frmActionViewModel.transferExcuteViewModel.CarrierId = carrierIdTextBox;
            }
            var SourceLoc = SourceLocation.Text;
            if (SourceLocation != null)
            {
                if (SourceLocation.Text == "")
                {
                    lblTransferResult.Text = "Please input the Source Location.";
                    return;
                }
                // TODO: 这里需要修改为从ViewModel中获取SourceLocation
                //GlobalData.frmActionViewModel.transferExcuteViewModel.SourceLocation = SourceLocation.Text;
            }
            var DestLoc = DestLocation.Text;
            if (DestLocation != null)
            {
                if (DestLocation.Text == "")
                {
                    lblTransferResult.Text = "Please input the Dest Location.";
                    return;
                }
                //  GlobalData.frmActionViewModel.transferExcuteViewModel.DestLocation = DestLocation.Text;
            }
            var Pri= Priority.Text;
            if (Priority != null)
            {
                if (Priority.Text == "")
                {
                    lblTransferResult.Text = "Please input the Priority.";
                    return;
                }
                int iPriority = 0;
                if (Int32.TryParse(Priority.Text, out iPriority) == false)
                {
                    lblTransferResult.Text = "Please input the number for Priority.";
                    return;
                }
                GlobalData.frmActionViewModel.transferExcuteViewModel.Priority = string.IsNullOrEmpty(Priority.Text) ? 99 : Convert.ToInt16(Priority.Text);
            }

            string strSourceLocation = SourceLocation.Text;
            if (strSourceLocation.Contains(m_strCraneSign))
            {
                strSourceLocation = strSourceLocation.Replace(m_strCraneSign, "");
            }
            TpLocation sourceLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strSourceLocation);
            if (sourceLoc == null)
            {
                lblTransferResult.Text = "Can not find the Source Location.";
                return;
            }
            var tpCarriersources = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(sourceLoc.Address);
            if (tpCarriersources == null && tpCarriersources.Count == 0)
            {
                lblTransferResult.Text = "No Source Carrier.";
                return;
            }
            TpCarrier Sourcecst = tpCarriersources[0];
            //检查Dest Location
            string strDestLocation = DestLocation.Text;
            if (strDestLocation.Contains(m_strCraneSign))
            {
                strDestLocation = strDestLocation.Replace(m_strCraneSign, "");
            }

            var destloc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strDestLocation);
            if (destloc == null)
            {
                lblTransferResult.Text = "Can not find the Dest Location.";
                return;
            }
            var tpCarriers = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(destloc.Address);
            if (tpCarriers != null && tpCarriers.Count != 0)
            {
                lblTransferResult.Text = "Can not transfer, because the Dest Location has CST.";
                return;
            }

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("CARRIERID", carrierIdTextBox);
            dicParams.Add("SOURCE", sourceLoc.Address);
            dicParams.Add("DEST", destloc.Address);
            dicParams.Add("PRIORITY", Priority.Text);
            object objResult = await WCFClient.Instance.SendMessage("Transfer", dicParams);
            bool bResult = true;
            if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
            {
                bResult = false;
            }
            if (bResult)
            {
                lblTransferResult.Text = "Submit Transfer Successfully.";
                Logger.Instance.OperationLog("Submit Transfer Successfully: " + "CARRIERID: " + carrierIdTextBox + ", SOURCE: "
                + sourceLoc.Address + ", DEST: " + destloc.Address + ", PRIORITY: " + Priority.Text);
            }
            else
            {
                lblTransferResult.Text = "Submit Transfer Failure.";
                Logger.Instance.OperationLog("Submit Transfer Failure: " + "CARRIERID: " + carrierIdTextBox + ", SOURCE: "
                 + sourceLoc.Address + ", DEST: " + destloc.Address + ", PRIORITY: " + Priority.Text);
            }
        }
    }
}
