﻿<UserControl x:Class="N2Purge.silan.frmIoMap"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.silan"
             xmlns:ctrl="clr-namespace:N2Purge.userControls"
              xmlns:userControls="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <TabControl x:Name="tb">
            <!--<TabItem Header="CV" Background="AliceBlue" TextBlock.FontSize="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>
                    <GroupBox Header="Sever=>Host" Grid.Row="0">
                        <Border x:Name="cranebd1" Background="Silver">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    
                                </Grid.RowDefinitions>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="0" x:Name="CraneReady" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="0" x:Name="CraneAlarm" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="0" x:Name="CraneFault" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="0" x:Name="CraneSafetyOk" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="0" x:Name="CraneServoEnable" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="1" x:Name="CraneForkAtHome" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="1" x:Name="CraneCSTStorge" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="1" x:Name="CraneCSTPrensence" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="1" x:Name="CraneTaskComplete" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="1" x:Name="CraneAbortComplete" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="2" x:Name="CraneSourceReady" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="2" x:Name="CraneSourceStorge" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="2" x:Name="CraneDestReady" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="2" x:Name="CraneDestStorge" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="2" x:Name="CraneTaskStorgeReady" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="3" x:Name="CraneStandStill" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="3" x:Name="CraneServoOff" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="3" x:Name="CraneCraneAtHome" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="3" x:Name="CraneShelfCSTDetect" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="3" x:Name="CraneBCRBaypassed" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="4" x:Name="CraneBuzzerSilence" Margin="10,5"/>
                            </Grid>
                        </Border>
                    </GroupBox>
                    <GroupBox Header="Host=>Sever" Grid.Row="1">
                        <Border x:Name="cranebd2" Background="Silver">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                </Grid.RowDefinitions>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="0" x:Name="CraneSetAlarmReset" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="0" x:Name="CraneSetSafetyReset" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="0" x:Name="CraneSetServoOn" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="0" x:Name="CraneSetTaskCompleteAck" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="0" x:Name="CraneSetAbortCompleteAck" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="1" x:Name="CraneSetBCRBaypass" Margin="10,5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="1" x:Name="CraneSetBuzzerSilence" Margin="10,5"/>
                            </Grid>
                        </Border>
                    </GroupBox>
                    <GroupBox Header="Setting" Grid.Row="2">
                    </GroupBox>
                </Grid>
            </TabItem>-->
            <TabItem Header="CV" x:Name="MuPort" Background="AliceBlue" TextBlock.FontSize="16">
                <Grid>
                    <DataGrid  Grid.Row="1" x:Name="dgv" CanUserAddRows="False" BorderThickness="1" BorderBrush="Black" AutoGenerateColumns="False">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="CVNmae" Binding="{Binding CVNmae}" Width="1*"/>
                            <DataGridTextColumn Header="CVIoNmae" Binding="{Binding CVIoNmae}" Width="1*"/>
                            <DataGridTemplateColumn Header="CVIoValue" Width="1*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <userControls:CmdStatus DataContext="{Binding StatusViewModel}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            <TabItem Header="OHTPort" Background="AliceBlue" TextBlock.FontSize="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="50"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>
                    <WrapPanel Grid.Row="0" VerticalAlignment="Center">
                        <Label Content="请选择Port口"/>
                        <ComboBox x:Name="cmbOHT" Width="100" Margin="10,0,0,0" SelectionChanged="cmbOHT_SelectionChanged">
                            <ComboBoxItem Content="OHT1"/>
                            <ComboBoxItem Content="OHT2"/>
                            <ComboBoxItem Content="OHT3"/>
                            <ComboBoxItem Content="OHT4"/>
                        </ComboBox>
                    </WrapPanel>
                    <GroupBox Header="Sever=>Host" Grid.Row="1">
                        <Border x:Name="OHTbd1" Background="Silver">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                </Grid.RowDefinitions>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="0" x:Name="L_REQ" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="0" x:Name="VALID" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="0" x:Name="U_REQ" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="0" x:Name="CS_0" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="0" x:Name="READY" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="0" Grid.Row="1" x:Name="TR_REQ" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="1" Grid.Row="1" x:Name="HO_AVBL" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="2" Grid.Row="1" x:Name="BUSY" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="3" Grid.Row="1" x:Name="ES" Margin="5"/>
                                <ctrl:CmdStatus Grid.Column="4" Grid.Row="1" x:Name="COPMT" Margin="5"/>
                               
                            </Grid>
                        </Border>
                    </GroupBox>
              
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
