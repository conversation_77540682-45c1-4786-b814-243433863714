using LiveCharts;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    public class PioChartViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private ChartValues<double> _agoValues;
        private ChartValues<double> _acontValues;
        private ChartValues<double> _acomptValues;
        private ChartValues<double> _abusyValues;
        private ChartValues<double> _atrreqValues;
        private ChartValues<double> _acs0Values;
        private ChartValues<double> _avalidValues;
        private ChartValues<double> _pesValues;
        private ChartValues<double> _phoavblValues;
        private ChartValues<double> _preadyValues;
        private ChartValues<double> _pureqValues;
        private ChartValues<double> _plreqValues;

        public PioChartViewModel()
        {
            AGOValues = new ChartValues<double>();
            ACONTValues = new ChartValues<double>();
            ACOMPTValues = new ChartValues<double>();
            ABusyValues = new ChartValues<double>();
            ATRREQValues = new ChartValues<double>();
            ACS0Values = new ChartValues<double>();
            AVALIDValues = new ChartValues<double>();
            PESValues = new ChartValues<double>();
            PHOAVBLValues = new ChartValues<double>();
            PReadyValues = new ChartValues<double>();
            PUREQValues = new ChartValues<double>();
            PLREQValues = new ChartValues<double>();
        }

        public ChartValues<double> AGOValues
        {
            get { return _agoValues; }
            set
            {
                _agoValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(AGOValues)));
            }
        }

        public ChartValues<double> ACONTValues
        {
            get { return _acontValues; }
            set
            {
                _acontValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ACONTValues)));
            }
        }

        public ChartValues<double> ACOMPTValues
        {
            get { return _acomptValues; }
            set
            {
                _acomptValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ACOMPTValues)));
            }
        }

        public ChartValues<double> ABusyValues
        {
            get { return _abusyValues; }
            set
            {
                _abusyValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ABusyValues)));
            }
        }

        public ChartValues<double> ATRREQValues
        {
            get { return _atrreqValues; }
            set
            {
                _atrreqValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ATRREQValues)));
            }
        }

        public ChartValues<double> ACS0Values
        {
            get { return _acs0Values; }
            set
            {
                _acs0Values = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ACS0Values)));
            }
        }

        public ChartValues<double> AVALIDValues
        {
            get { return _avalidValues; }
            set
            {
                _avalidValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(AVALIDValues)));
            }
        }

        public ChartValues<double> PESValues
        {
            get { return _pesValues; }
            set
            {
                _pesValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PESValues)));
            }
        }

        public ChartValues<double> PHOAVBLValues
        {
            get { return _phoavblValues; }
            set
            {
                _phoavblValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PHOAVBLValues)));
            }
        }

        public ChartValues<double> PReadyValues
        {
            get { return _preadyValues; }
            set
            {
                _preadyValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PReadyValues)));
            }
        }

        public ChartValues<double> PUREQValues
        {
            get { return _pureqValues; }
            set
            {
                _pureqValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PUREQValues)));
            }
        }

        public ChartValues<double> PLREQValues
        {
            get { return _plreqValues; }
            set
            {
                _plreqValues = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PLREQValues)));
            }
        }

        public void UpdatePioValues(double ago, double acont, double acompt, double abusy, double atrreq, double acs0, 
            double avalid, double pes, double phoavbl, double pready, double pureq, double plreq)
        {
            AGOValues.Add(ago);
            ACONTValues.Add(acont);
            ACOMPTValues.Add(acompt);
            ABusyValues.Add(abusy);
            ATRREQValues.Add(atrreq);
            ACS0Values.Add(acs0);
            AVALIDValues.Add(avalid);
            PESValues.Add(pes);
            PHOAVBLValues.Add(phoavbl);
            PReadyValues.Add(pready);
            PUREQValues.Add(pureq);
            PLREQValues.Add(plreq);

            // 保持图表显示最新的60个数据点
            if (AGOValues.Count > 60)
            {
                AGOValues.RemoveAt(0);
                ACONTValues.RemoveAt(0);
                ACOMPTValues.RemoveAt(0);
                ABusyValues.RemoveAt(0);
                ATRREQValues.RemoveAt(0);
                ACS0Values.RemoveAt(0);
                AVALIDValues.RemoveAt(0);
                PESValues.RemoveAt(0);
                PHOAVBLValues.RemoveAt(0);
                PReadyValues.RemoveAt(0);
                PUREQValues.RemoveAt(0);
                PLREQValues.RemoveAt(0);
            }
        }
    }
}