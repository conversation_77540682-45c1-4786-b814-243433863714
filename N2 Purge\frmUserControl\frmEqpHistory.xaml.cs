﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmEqp_History.xaml 的交互逻辑
    /// </summary>
    public partial class frmEqpHistory : UserControl
    {
        public frmEqpHistory()
        {
            InitializeComponent();
            dgv.ItemsSource=thEQPViewModels; 
        }
        private ObservableCollection<ThEQPViewModel> thEQPViewModels = new ObservableCollection<ThEQPViewModel>();  
        private async void btnsearch_Click(object sender, RoutedEventArgs e)
        {
            if (tmstart.Value==null||tmend.Value==null)
            {
                MessageBox.Show("Please Check");
                return;
            }
            var query =await GlobalData.dbHelper.thEqpdb.GetEqpHistoryByTimeRangeAsync((DateTime)tmstart.Value, (DateTime)tmend.Value);
            if (query != null) {
                if (txtEventID.Text.Length > 0)
                {
                    int nEventID = 0;
                    if (Int32.TryParse(txtEventID.Text, out nEventID) == false)
                    {
                        MessageBox.Show("Please input the integer for 'Event ID'.");
                        return;
                    }
                    query.Where(x => x.Event_ID.Equals(nEventID)).ToList();
                }
                if (txtUnit.Text.Length > 0)
                    query.Where(x => x.Unit.Contains(txtUnit.Text)).ToList();
                if (txtEventName.Text.Length > 0)
                    query.Where(x => x.Event_Name.Contains(txtEventName.Text)).ToList();
                thEQPViewModels.Clear();
                if (query.Count!=0)
                {
                    for (int i = 0; i < query.Count; i++)
                    {
                        ThEQPViewModel current = new ThEQPViewModel();
                        current.EventId = query[i].Event_ID;
                        current.Unit = query[i].Unit;
                        current.Event_Name = query[i].Event_Name;
                        current.Id = query[i].Id;
                        current.Time = query[i].Time;
                        current.Text = query[i].Text;
                        thEQPViewModels.Add(current);
                    }
                }
               
            }
        }
    }
}
