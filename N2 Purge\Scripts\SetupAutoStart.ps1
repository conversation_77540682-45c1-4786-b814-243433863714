# N2 Purge 自启动设置 PowerShell 脚本
# 需要管理员权限运行

param(
    [string]$Action = "menu",
    [string]$AppPath = "",
    [int]$StartupDelay = 30
)

# 设置变量
$TaskName = "N2PurgeAutoStart"
$AppName = "N2Purge"
$Description = "N2 Purge 智能传送带控制系统自动启动任务"

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 获取应用程序路径
function Get-ApplicationPath {
    if ($AppPath -and (Test-Path $AppPath)) {
        return $AppPath
    }
    
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $possiblePaths = @(
        Join-Path $scriptDir "..\bin\Debug\N2Purge.exe",
        Join-Path $scriptDir "..\bin\Release\N2Purge.exe",
        Join-Path $scriptDir "..\N2Purge.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    
    return $null
}

# 创建计划任务
function New-AutoStartTask {
    param(
        [string]$ExePath,
        [int]$Delay = 30
    )
    
    try {
        # 删除已存在的任务
        Remove-AutoStartTask -Silent
        
        # 创建任务动作
        $action = New-ScheduledTaskAction -Execute $ExePath -WorkingDirectory (Split-Path $ExePath)
        
        # 创建触发器 - 系统启动时
        $bootTrigger = New-ScheduledTaskTrigger -AtStartup
        $bootTrigger.Delay = "PT${Delay}S"
        
        # 创建触发器 - 用户登录时
        $logonTrigger = New-ScheduledTaskTrigger -AtLogOn
        $logonTrigger.Delay = "PT${Delay}S"
        
        # 创建任务设置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd
        $settings.MultipleInstances = "IgnoreNew"
        $settings.ExecutionTimeLimit = "PT0S"  # 无时间限制
        $settings.RestartCount = 3
        $settings.RestartInterval = "PT1M"
        
        # 创建主体 - 使用最高权限
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive -RunLevel Highest
        
        # 注册任务
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger @($bootTrigger, $logonTrigger) -Settings $settings -Principal $principal -Description $Description -Force
        
        Write-Host "[成功] 开机自启动设置完成" -ForegroundColor Green
        Write-Host "- 任务名称: $TaskName" -ForegroundColor Gray
        Write-Host "- 启动延迟: $Delay 秒" -ForegroundColor Gray
        Write-Host "- 应用程序: $ExePath" -ForegroundColor Gray
        return $true
    }
    catch {
        Write-Host "[错误] 设置开机自启动失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 移除计划任务
function Remove-AutoStartTask {
    param([switch]$Silent)
    
    try {
        $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($task) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            if (-not $Silent) {
                Write-Host "[成功] 开机自启动已移除" -ForegroundColor Green
            }
            return $true
        } else {
            if (-not $Silent) {
                Write-Host "[信息] 未找到自启动任务或已经移除" -ForegroundColor Yellow
            }
            return $true
        }
    }
    catch {
        if (-not $Silent) {
            Write-Host "[错误] 移除开机自启动失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        return $false
    }
}

# 检查任务状态
function Get-AutoStartStatus {
    try {
        $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($task) {
            Write-Host "[状态] 开机自启动: 已启用" -ForegroundColor Green
            Write-Host ""
            Write-Host "任务详细信息:" -ForegroundColor Cyan
            Write-Host "- 任务名称: $($task.TaskName)" -ForegroundColor Gray
            Write-Host "- 状态: $($task.State)" -ForegroundColor Gray
            Write-Host "- 描述: $($task.Description)" -ForegroundColor Gray
            
            $info = Get-ScheduledTaskInfo -TaskName $TaskName -ErrorAction SilentlyContinue
            if ($info) {
                Write-Host "- 上次运行: $($info.LastRunTime)" -ForegroundColor Gray
                Write-Host "- 下次运行: $($info.NextRunTime)" -ForegroundColor Gray
                Write-Host "- 上次结果: $($info.LastTaskResult)" -ForegroundColor Gray
            }
        } else {
            Write-Host "[状态] 开机自启动: 未启用" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "[错误] 检查状态失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示菜单
function Show-Menu {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    N2 Purge 自启动设置工具 (PowerShell)" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "请选择操作:" -ForegroundColor White
    Write-Host "1. 设置开机自启动 (推荐)" -ForegroundColor Green
    Write-Host "2. 移除开机自启动" -ForegroundColor Red
    Write-Host "3. 查看当前状态" -ForegroundColor Yellow
    Write-Host "4. 退出" -ForegroundColor Gray
    Write-Host ""
}

# 主程序
function Main {
    # 检查管理员权限
    if (-not (Test-Administrator)) {
        Write-Host "[错误] 此脚本需要管理员权限运行" -ForegroundColor Red
        Write-Host "请右键点击PowerShell，选择'以管理员身份运行'，然后执行此脚本" -ForegroundColor Yellow
        Read-Host "按任意键退出"
        exit 1
    }
    
    Write-Host "[信息] 管理员权限检查通过" -ForegroundColor Green
    Write-Host ""
    
    # 获取应用程序路径
    $appPath = Get-ApplicationPath
    if (-not $appPath) {
        Write-Host "[错误] 找不到N2Purge.exe文件" -ForegroundColor Red
        Write-Host "请确保应用程序文件存在于正确位置" -ForegroundColor Yellow
        Read-Host "按任意键退出"
        exit 1
    }
    
    Write-Host "[信息] 应用程序文件检查通过: $appPath" -ForegroundColor Green
    Write-Host ""
    
    # 处理命令行参数
    switch ($Action.ToLower()) {
        "setup" {
            New-AutoStartTask -ExePath $appPath -Delay $StartupDelay
            return
        }
        "remove" {
            Remove-AutoStartTask
            return
        }
        "status" {
            Get-AutoStartStatus
            return
        }
    }
    
    # 交互式菜单
    do {
        Show-Menu
        $choice = Read-Host "请输入选择 (1-4)"
        
        switch ($choice) {
            "1" {
                Write-Host ""
                Write-Host "[信息] 正在设置开机自启动..." -ForegroundColor Cyan
                New-AutoStartTask -ExePath $appPath -Delay $StartupDelay
                Write-Host ""
                Read-Host "按任意键继续"
            }
            "2" {
                Write-Host ""
                Write-Host "[信息] 正在移除开机自启动..." -ForegroundColor Cyan
                Remove-AutoStartTask
                Write-Host ""
                Read-Host "按任意键继续"
            }
            "3" {
                Write-Host ""
                Write-Host "[信息] 检查当前状态..." -ForegroundColor Cyan
                Get-AutoStartStatus
                Write-Host ""
                Read-Host "按任意键继续"
            }
            "4" {
                Write-Host ""
                Write-Host "感谢使用 N2 Purge 自启动设置工具" -ForegroundColor Cyan
                Write-Host ""
                exit 0
            }
            default {
                Write-Host ""
                Write-Host "[错误] 无效选择，请重新输入" -ForegroundColor Red
                Start-Sleep -Seconds 1
            }
        }
    } while ($true)
}

# 执行主程序
Main
