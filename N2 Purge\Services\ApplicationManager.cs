using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Windows;
using Microsoft.Win32;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 应用程序管理器
    /// </summary>
    public class ApplicationManager
    {
        private const string REGISTRY_KEY = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
        private const string APP_NAME = "N2Purge";
        
        /// <summary>
        /// 设置开机自启动
        /// </summary>
        /// <param name="useTaskScheduler">是否使用计划任务(推荐)</param>
        /// <param name="startupDelay">启动延迟(秒)</param>
        /// <returns>设置是否成功</returns>
        public static bool SetAutoStart(bool useTaskScheduler = true, int startupDelay = 30)
        {
            try
            {
                string executablePath = GetExecutablePath();
                
                if (useTaskScheduler)
                {
                    // 使用计划任务方式
                    if (TaskSchedulerManager.IsAdministrator())
                    {
                        bool result = TaskSchedulerManager.CreateStartupTask(executablePath, startupDelay);
                        if (result)
                        {
                            Logger.Instance?.OperationLog("已设置计划任务自启动");
                            // 同时清除注册表自启动(避免重复)
                            RemoveRegistryAutoStart();
                            return true;
                        }
                        else
                        {
                            Logger.Instance?.ExceptionLog("计划任务设置失败，尝试注册表方式");
                            return SetRegistryAutoStart(executablePath);
                        }
                    }
                    else
                    {
                        Logger.Instance?.ExceptionLog("需要管理员权限设置计划任务，使用注册表方式");
                        return SetRegistryAutoStart(executablePath);
                    }
                }
                else
                {
                    // 使用注册表方式
                    return SetRegistryAutoStart(executablePath);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"设置自启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除开机自启动
        /// </summary>
        /// <returns>移除是否成功</returns>
        public static bool RemoveAutoStart()
        {
            bool taskResult = true;
            bool registryResult = true;

            try
            {
                // 移除计划任务
                if (TaskSchedulerManager.TaskExists())
                {
                    taskResult = TaskSchedulerManager.DeleteStartupTask();
                }

                // 移除注册表项
                registryResult = RemoveRegistryAutoStart();

                Logger.Instance?.OperationLog("已移除自启动设置");
                return taskResult && registryResult;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"移除自启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否已设置自启动
        /// </summary>
        /// <returns>是否已设置自启动</returns>
        public static bool IsAutoStartEnabled()
        {
            return TaskSchedulerManager.TaskExists() || IsRegistryAutoStartEnabled();
        }

        /// <summary>
        /// 显示自启动设置对话框
        /// </summary>
        public static void ShowAutoStartDialog()
        {
            try
            {
                bool isEnabled = IsAutoStartEnabled();
                string message = isEnabled ? 
                    "N2Purge当前已设置为开机自启动。\n\n是否要移除自启动设置？" : 
                    "N2Purge当前未设置开机自启动。\n\n是否要设置为开机自启动？\n(推荐使用计划任务方式，需要管理员权限)";
                
                string title = isEnabled ? "移除自启动" : "设置自启动";
                
                var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    if (isEnabled)
                    {
                        if (RemoveAutoStart())
                        {
                            MessageBox.Show("自启动设置已移除", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("移除自启动设置失败，请检查权限", "操作失败", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        if (SetAutoStart(true, 30))
                        {
                            MessageBox.Show("自启动设置成功\n\n程序将在系统启动后30秒自动运行", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("设置自启动失败，请检查权限", "操作失败", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"显示自启动对话框异常: {ex.Message}");
                MessageBox.Show($"操作异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重启应用程序为管理员权限
        /// </summary>
        public static void RestartAsAdministrator()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = GetExecutablePath(),
                    UseShellExecute = true,
                    Verb = "runas" // 请求管理员权限
                };

                Process.Start(startInfo);
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"重启为管理员失败: {ex.Message}");
                MessageBox.Show($"重启为管理员失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取当前可执行文件路径
        /// </summary>
        private static string GetExecutablePath()
        {
            return Assembly.GetExecutingAssembly().Location.Replace(".dll", ".exe");
        }

        /// <summary>
        /// 设置注册表自启动
        /// </summary>
        private static bool SetRegistryAutoStart(string executablePath)
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true))
                {
                    if (key != null)
                    {
                        key.SetValue(APP_NAME, $"\"{executablePath}\"");
                        Logger.Instance?.OperationLog("已设置注册表自启动");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"设置注册表自启动失败: {ex.Message}");
            }
            return false;
        }

        /// <summary>
        /// 移除注册表自启动
        /// </summary>
        private static bool RemoveRegistryAutoStart()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true))
                {
                    if (key?.GetValue(APP_NAME) != null)
                    {
                        key.DeleteValue(APP_NAME);
                        Logger.Instance?.OperationLog("已移除注册表自启动");
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"移除注册表自启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查注册表自启动是否启用
        /// </summary>
        private static bool IsRegistryAutoStartEnabled()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    return key?.GetValue(APP_NAME) != null;
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
