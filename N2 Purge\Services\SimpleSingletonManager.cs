using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 简单单例管理器 - 使用更直接的方法确保单例
    /// </summary>
    public static class SimpleSingletonManager
    {
        private static Mutex? _mutex;
        private static bool _isMainInstance = false;
        private static readonly string _mutexName = "Global\\N2Purge_SingleInstance_2024";

        /// <summary>
        /// 检查是否为主实例
        /// </summary>
        /// <returns>如果是主实例返回true，否则返回false</returns>
        public static bool CheckSingleInstance()
        {
            try
            {
                Logger.Instance?.OperationLog($"[简单单例] 开始检查单例，Mutex名称: {_mutexName}");

                // 调试输出到控制台
                Console.WriteLine($"[DEBUG] 开始单例检查，Mutex: {_mutexName}");

                // 尝试创建命名Mutex
                bool createdNew;
                _mutex = new Mutex(true, _mutexName, out createdNew);

                Console.WriteLine($"[DEBUG] Mutex创建结果: createdNew={createdNew}");

                if (createdNew)
                {
                    // 成功创建新的Mutex，说明是第一个实例
                    _isMainInstance = true;
                    Logger.Instance?.OperationLog("[简单单例] ✅ 成功创建Mutex，当前为主实例");
                    Console.WriteLine("[DEBUG] ✅ 当前为主实例");
                    return true;
                }
                else
                {
                    // Mutex已存在，说明有其他实例在运行
                    Logger.Instance?.OperationLog("[简单单例] ⚠️ Mutex已存在，检测到其他实例");
                    Console.WriteLine("[DEBUG] ⚠️ Mutex已存在，检测到其他实例");

                    // 尝试获取Mutex的所有权（等待3秒）
                    Logger.Instance?.OperationLog("[简单单例] 尝试获取Mutex所有权...");
                    Console.WriteLine("[DEBUG] 尝试获取Mutex所有权...");
                    bool acquired = _mutex.WaitOne(3000, false);
                    Console.WriteLine($"[DEBUG] Mutex获取结果: {acquired}");

                    if (acquired)
                    {
                        _isMainInstance = true;
                        Logger.Instance?.OperationLog("[简单单例] ✅ 成功获取Mutex所有权，当前为主实例");
                        Console.WriteLine("[DEBUG] ✅ 成功获取Mutex所有权，当前为主实例");
                        return true;
                    }
                    else
                    {
                        _isMainInstance = false;
                        Logger.Instance?.OperationLog("[简单单例] ❌ 无法获取Mutex所有权，其他实例正在运行");
                        Console.WriteLine("[DEBUG] ❌ 无法获取Mutex所有权，其他实例正在运行");

                        // 释放Mutex资源
                        _mutex?.Close();
                        _mutex?.Dispose();
                        _mutex = null;

                        return false;
                    }
                }
            }
            catch (AbandonedMutexException ex)
            {
                // 前一个实例异常退出，Mutex被遗弃
                Logger.Instance?.OperationLog($"[简单单例] ⚠️ 检测到遗弃的Mutex，可能前一个实例异常退出: {ex.Message}");
                _isMainInstance = true;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[简单单例] 检查单例时发生异常: {ex.Message}");
                Logger.Instance?.ExceptionLog($"[简单单例] 异常详情: {ex}");
                
                // 异常情况下，检查进程列表
                return CheckByProcessList();
            }
        }

        /// <summary>
        /// 通过进程列表检查是否有其他实例
        /// </summary>
        /// <returns>如果没有其他实例返回true</returns>
        private static bool CheckByProcessList()
        {
            try
            {
                Logger.Instance?.OperationLog("[简单单例] 使用进程列表检查方法");
                
                string currentProcessName = Process.GetCurrentProcess().ProcessName;
                string currentExecutablePath = Assembly.GetExecutingAssembly().Location.Replace(".dll", ".exe");
                int currentProcessId = Process.GetCurrentProcess().Id;

                Logger.Instance?.OperationLog($"[简单单例] 当前进程: {currentProcessName}, PID: {currentProcessId}");
                Logger.Instance?.OperationLog($"[简单单例] 当前路径: {currentExecutablePath}");

                var processes = Process.GetProcessesByName(currentProcessName);
                int samePathProcessCount = 0;

                foreach (var process in processes)
                {
                    try
                    {
                        if (process.Id == currentProcessId)
                        {
                            // 跳过当前进程
                            continue;
                        }

                        string processPath = process.MainModule?.FileName ?? "";
                        Logger.Instance?.OperationLog($"[简单单例] 检查进程: PID={process.Id}, Path={processPath}");

                        if (string.Equals(processPath, currentExecutablePath, StringComparison.OrdinalIgnoreCase))
                        {
                            samePathProcessCount++;
                            Logger.Instance?.OperationLog($"[简单单例] 发现相同路径的进程: PID={process.Id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance?.OperationLog($"[简单单例] 检查进程 {process.Id} 时出错: {ex.Message}");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                bool isMainInstance = samePathProcessCount == 0;
                Logger.Instance?.OperationLog($"[简单单例] 进程检查结果: 发现 {samePathProcessCount} 个相同实例，当前为主实例: {isMainInstance}");
                
                _isMainInstance = isMainInstance;
                return isMainInstance;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[简单单例] 进程列表检查失败: {ex.Message}");
                // 最后的异常情况，默认允许运行
                _isMainInstance = true;
                return true;
            }
        }

        /// <summary>
        /// 是否为主实例
        /// </summary>
        public static bool IsMainInstance => _isMainInstance;

        /// <summary>
        /// 释放资源
        /// </summary>
        public static void Release()
        {
            try
            {
                if (_mutex != null && _isMainInstance)
                {
                    Logger.Instance?.OperationLog("[简单单例] 释放Mutex资源");
                    _mutex.ReleaseMutex();
                    _mutex.Close();
                    _mutex.Dispose();
                    _mutex = null;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[简单单例] 释放资源时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制终止其他实例
        /// </summary>
        /// <returns>终止的实例数量</returns>
        public static int KillOtherInstances()
        {
            try
            {
                Logger.Instance?.OperationLog("[简单单例] 开始强制终止其他实例");
                
                string currentProcessName = Process.GetCurrentProcess().ProcessName;
                string currentExecutablePath = Assembly.GetExecutingAssembly().Location.Replace(".dll", ".exe");
                int currentProcessId = Process.GetCurrentProcess().Id;
                int killedCount = 0;

                var processes = Process.GetProcessesByName(currentProcessName);

                foreach (var process in processes)
                {
                    try
                    {
                        if (process.Id == currentProcessId)
                        {
                            continue; // 跳过当前进程
                        }

                        string processPath = process.MainModule?.FileName ?? "";
                        if (string.Equals(processPath, currentExecutablePath, StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Instance?.OperationLog($"[简单单例] 终止进程: PID={process.Id}, Path={processPath}");
                            process.Kill();
                            process.WaitForExit(5000); // 等待最多5秒
                            killedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance?.ExceptionLog($"[简单单例] 终止进程 {process.Id} 失败: {ex.Message}");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                Logger.Instance?.OperationLog($"[简单单例] 强制终止完成，共终止 {killedCount} 个实例");
                return killedCount;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[简单单例] 强制终止其他实例失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取其他实例的信息
        /// </summary>
        /// <returns>其他实例的进程ID列表</returns>
        public static int[] GetOtherInstanceIds()
        {
            try
            {
                string currentProcessName = Process.GetCurrentProcess().ProcessName;
                string currentExecutablePath = Assembly.GetExecutingAssembly().Location.Replace(".dll", ".exe");
                int currentProcessId = Process.GetCurrentProcess().Id;
                var otherIds = new List<int>();

                var processes = Process.GetProcessesByName(currentProcessName);

                foreach (var process in processes)
                {
                    try
                    {
                        if (process.Id == currentProcessId)
                        {
                            continue;
                        }

                        string processPath = process.MainModule?.FileName ?? "";
                        if (string.Equals(processPath, currentExecutablePath, StringComparison.OrdinalIgnoreCase))
                        {
                            otherIds.Add(process.Id);
                        }
                    }
                    catch
                    {
                        // 忽略无法访问的进程
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return otherIds.ToArray();
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[简单单例] 获取其他实例信息失败: {ex.Message}");
                return Array.Empty<int>();
            }
        }
    }
}
