﻿<UserControl x:Class="N2Purge.userControls.ButtomInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1000">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="8*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TabControl Grid.Column="0">
            <TabItem Header="Transfer Info" TextBlock.FontSize="20">
                <DataGrid>
                    <DataGrid.Columns>
                        <DataGridTextColumn Width="*" Header="ID" Binding="{Binding ID}"/>
                        <DataGridTextColumn Header="Location" Binding="{Binding Location}" Width="*"/>
                        <DataGridTextColumn Header="State" Binding="{Binding State}" Width="*"/>
                        <DataGridTextColumn Header="IdReadStatus" Binding="{Binding IdReadStatus}" Width="*"/>
                        <DataGridTextColumn Header="InstallTime" Binding="{Binding InstallTime}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
            <TabItem Header="Carrier Info" TextBlock.FontSize="20">
                <DataGrid>
                    <DataGrid.Columns>
                        <DataGridTextColumn Width="*" Header="ID" Binding="{Binding ID}"/>
                        <DataGridTextColumn Header="Location" Binding="{Binding Location}" Width="*"/>
                        <DataGridTextColumn Header="State" Binding="{Binding State}" Width="*"/>
                        <DataGridTextColumn Header="IdReadStatus" Binding="{Binding IdReadStatus}" Width="*"/>
                        <DataGridTextColumn Header="InstallTime" Binding="{Binding InstallTime}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>
        <Grid Grid.Column="1" Grid.Row="0" Grid.RowSpan="2">
            <TabControl>
                <TabItem Header="Transfer">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>
                        <Label Content="Cassette ID" Background="Blue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Source Location" Background="Blue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Dest Location" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Priority" Background="Blue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <TextBlock Text="{Binding }" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="White" />
                        <ComboBox ItemsSource="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <ComboBox ItemsSource="{Binding DestLocation}" Background="Transparent" Grid.Column="2" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <TextBlock Text="{Binding Priority}" Background="Transparent" Grid.Column="3" Grid.Row="3" TextBlock.Foreground="White" />
                    </Grid>
                </TabItem>
                <TabItem Header="Install">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>
                        <Label Content="Cassette ID" Background="Blue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Location" Background="Blue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="CST State" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="IDR Result" Background="Blue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <TextBlock Text="{Binding }" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="White" />
                        <ComboBox ItemsSource="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <ComboBox ItemsSource="{Binding DestLocation}" Background="Transparent" Grid.Column="2" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <ComboBox ItemsSource="{Binding Priority}" Background="Transparent" Grid.Column="3" Grid.Row="3" TextBlock.Foreground="White" />
                    </Grid>
                </TabItem>
                <TabItem Header="Remove">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>
                        <Label Content="Cassette ID" Background="Blue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Location" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                    
                    
                        <ComboBox ItemsSource="{Binding CstID}" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <ComboBox ItemsSource="{Binding Location}" Background="Transparent" Grid.Column="2" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                     
                    </Grid>
                </TabItem>
                <TabItem Header="Scan">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>
                        <Label Content="Cassette ID" Background="Blue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <Label Content="Source Location" Background="Blue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                    
                        <Label Content="Priority" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                        <TextBlock Text="{Binding }" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="White" />
                        <ComboBox ItemsSource="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                    
                        <TextBlock Text="{Binding Priority}" Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="White" />
                    </Grid>
                </TabItem>
                <TabItem Header="Move">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>
                
                        <Label Content="Dest Location" Background="Blue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>

                        <Label Content="Priority" Background="Blue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>
                    
                        <ComboBox ItemsSource="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center"/>

                        <TextBlock Text="{Binding Priority}" Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="White" />
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>
        <Grid Grid.Row="2" Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="10"/>
                <RowDefinition Height="100"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Button x:Name="btnExcute" Grid.Row="0" Content="Excute"/>
            <Label x:Name="bd" Grid.Row="2"  Background="LightYellow"/>
        </Grid>
        <!-- 左侧表格 -->
   
    </Grid>
</UserControl>
