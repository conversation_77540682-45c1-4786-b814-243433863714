﻿<UserControl x:Class="N2Purge.frmUserControl.frmEqpHistory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             xmlns:xctr="http://schemas.xceed.com/wpf/xaml/toolkit"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="900">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
          
        </Grid.RowDefinitions>
        <Border BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Label  Content="开始时间" Grid.Column="0" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Margin="0,5"/>
                <xctr:DateTimePicker x:Name="tmstart" Grid.Column="1" Background="White" Width="200" Margin="0,5"  TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="结束时间" Grid.Column="2" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Margin="0,5"/>
                <xctr:DateTimePicker x:Name="tmend" Grid.Column="3" Margin="2,5" Background="White" Width="200" TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="单元设备" Grid.Column="4" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBox x:Name="txtEventID"  Width="100" Background="White" Grid.Column="5" Margin="0,5" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Label  Content="事件名称" Grid.Column="6" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBox x:Name="txtUnit"  Width="100" Background="White" Grid.Column="7" Margin="0,5" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center"/>
            </Grid>
        </Border>
        <Border Grid.Row="1" BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="80"/>
                   
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
              
                <Label  Content="单元设备" Grid.Column="0" HorizontalContentAlignment="Center"  VerticalContentAlignment="Center"/>
                <TextBox x:Name="txtEventName"  Width="100" Background="White" Grid.Column="1"  VerticalContentAlignment="Center" Margin="0,5" TextBlock.TextAlignment="Center"/>
                <Button x:Name="btnsearch" Content="查询" Grid.Column="2" Margin="3,5" Background="Snow" Click="btnsearch_Click"/>
              
            </Grid>
        </Border>
        <DataGrid Grid.Row="2" CanUserAddRows="False" x:Name="dgv" AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Id" Binding="{Binding Id}" Width="1*"/>
                <DataGridTextColumn Header="EventId" Binding="{Binding EventId}" Width="1*"/>
                <DataGridTextColumn Header="Event_Name" Binding="{Binding Event_Name}" Width="1*"/>
                <DataGridTextColumn Header="Text" Binding="{Binding Text}" Width="1*"/>
                <DataGridTextColumn Header="Time" Binding="{Binding Time}" Width="1*"/>
                <DataGridTextColumn Header="Unit" Binding="{Binding Unit}" Width="1*"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
