<UserControl x:Class="N2Purge.userControls.DeviceStatus"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 设备状态表格 -->
        <DataGrid x:Name="dgDeviceStatus" Grid.Row="1" AutoGenerateColumns="False" 
                  IsReadOnly="True" GridLinesVisibility="All" 
                  AlternatingRowBackground="#F5F5F5">
            <DataGrid.Columns>
                <DataGridTextColumn Header="设备ID" Binding="{Binding DeviceId}" Width="100"/>
                <DataGridTextColumn Header="设备名称" Binding="{Binding DeviceName}" Width="150"/>
                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="100"/>
                <DataGridTextColumn Header="运行时间" Binding="{Binding RunningTime}" Width="120"/>
                <DataGridTextColumn Header="最后更新" Binding="{Binding LastUpdate}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>