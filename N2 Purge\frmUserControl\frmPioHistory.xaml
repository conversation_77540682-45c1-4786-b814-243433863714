﻿<UserControl x:Class="N2Purge.frmUserControl.frmPioHistory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
                 xmlns:xctr="http://schemas.xceed.com/wpf/xaml/toolkit"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>

            </Grid.RowDefinitions>
            <Border BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="250"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="250"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Label Margin="2,5" Content="开始时间" Grid.Column="0" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                    <xctr:DateTimePicker x:Name="dtstart" Grid.Column="1" Background="White" Margin="2,5" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                    <Label  Content="结束时间" Grid.Column="2" HorizontalContentAlignment="Center" Margin="2,5" VerticalContentAlignment="Center"/>
                    <xctr:DateTimePicker x:Name="dtend" Grid.Column="3" Background="White" Margin="2,5" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                    <Label  Content="方向" Grid.Column="4" HorizontalContentAlignment="Center" Margin="2,5" VerticalContentAlignment="Center"/>
                    <ComboBox x:Name="cbxDirection" Margin="2,5"  Grid.Column="5" Background="White" TextBlock.TextAlignment="Center">
                        <ComboBoxItem Content="Send"/>
                        <ComboBoxItem Content="Receive"/>
                    </ComboBox>
                   <Label  Content="Port名称" Grid.Column="6" Margin="2,5" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                    <TextBox  x:Name="txtPortName" Margin="2,5" Background="White" Grid.Column="7"  TextBlock.TextAlignment="Center"/>
                </Grid>
            </Border>
            <Border Grid.Row="1" BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="80"/>

                        <ColumnDefinition Width="97*"/>
                        <ColumnDefinition Width="62*"/>
                    </Grid.ColumnDefinitions>

                    <Label Margin="2,5,2,5" Content="连接模块" Grid.Column="0" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                    <TextBox x:Name="txtConnectModule" Margin="2,5,2,5" VerticalContentAlignment="Center" Background="White" Grid.Column="1"  TextBlock.TextAlignment="Center"/>
                    <Label Margin="2,5,2,5" VerticalContentAlignment="Center"  Content="IO名称" Grid.Column="2"  HorizontalContentAlignment="Center"/>
                    <TextBox x:Name="txtIOName" Margin="2,5,2,5" VerticalContentAlignment="Center" Background="White" Grid.Column="3"  TextBlock.TextAlignment="Center"/>
                    <Button x:Name="btnSearch" Margin="2,5,2,5" VerticalContentAlignment="Center"   Content="查询" Grid.Column="4"  Background="Snow" Click="btnSearch_Click"/>

                </Grid>
            </Border>
            <DataGrid Grid.Row="2" x:Name="dgv" AutoGenerateColumns="False" CanUserAddRows="False"  ItemsSource="{Binding PioHistories, UpdateSourceTrigger=PropertyChanged}"> 
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding ID}" Width="1*"/>
                    <DataGridTextColumn Header="Time" Binding="{Binding Time}" Width="1*"/>
                    <DataGridTextColumn Header="PortName" Binding="{Binding Port_Name}" Width="1*"/>
                    <DataGridTextColumn Header="Direction" Binding="{Binding Direction}" Width="1*"/>
                    <DataGridTextColumn Header="ConnectModule" Binding="{Binding Connect_Module}" Width="1*"/>
                    <DataGridTextColumn Header="IOName" Binding="{Binding IO_Name}" Width="1*"/>
                    <DataGridTextColumn Header="IOValue" Binding="{Binding Io_Value, Mode=OneWay}" Width="1*">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
