﻿<UserControl x:Class="N2Purge.frmUserControl.frmUserCtr"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <TabControl >
            <TabItem x:Name="userlogin" Header="UserLogin" TextBlock.FontSize="20">
                <local:frmUser HorizontalAlignment="Center" UserLoginTabClicked="frmUser_UserLoginTabClicked"/>
            </TabItem>
            <TabItem x:Name="usermanager" Header="UserManagerment" TextBlock.FontSize="20">
                <local:frmUserManagement x:Name="frmum"/>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
