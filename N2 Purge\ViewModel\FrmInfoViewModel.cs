using N2Purge.ViewModel;
using System;
namespace N2Purge.ViewModel
{

    /// <summary>
    /// FrmInfo Vm
    /// </summary>
    public class FrmInfoViewModel : VmPropertyChange
    {
        private string _cassetteID;
        public string CassetteID
        {
            get { return _cassetteID; }
            set { _cassetteID = value; OnPropertyChanged(nameof(CassetteID)); }
        }

        private string _location;
        public string Location
        {
            get { return _location; }
            set { _location = value; OnPropertyChanged(nameof(Location)); }
        }

        private string _installTime;
        public string InstallTime
        {
            get { return _installTime; }
            set { _installTime = value; OnPropertyChanged(nameof(InstallTime)); }
        }

        private string _status;
        public string Status
        {
            get { return _status; }
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }

        private string _idStatus;
        public string IDStatus
        {
            get { return _idStatus; }
            set { _idStatus = value; OnPropertyChanged(nameof(IDStatus)); }
        }

        private string _carrierType;
        public string CarrierType
        {
            get { return _carrierType; }
            set { _carrierType = value; OnPropertyChanged(nameof(CarrierType)); }
        }

        private string _emptyState;
        public string EmptyState
        {
            get { return _emptyState; }
            set { _emptyState = value; OnPropertyChanged(nameof(EmptyState)); }
        }

        private string _quantity;
        public string Quantity
        {
            get { return _quantity; }
            set { _quantity = value; OnPropertyChanged(nameof(Quantity)); }
        }

        private string _lotID;
        public string LotID
        {
            get { return _lotID; }
            set { _lotID = value; OnPropertyChanged(nameof(LotID)); }
        }

        private string _cstTestType;
        public string CSTTestType
        {
            get { return _cstTestType; }
            set { _cstTestType = value; OnPropertyChanged(nameof(CSTTestType)); }
        }

        private string _hostID;
        public string HostID
        {
            get { return _hostID; }
            set { _hostID = value; OnPropertyChanged(nameof(HostID)); }
        }

        private string _locationType;
        public string LocationType
        {
            get { return _locationType; }
            set { _locationType = value; OnPropertyChanged(nameof(LocationType)); }
        }

        private string _zone;
        public string Zone
        {
            get { return _zone; }
            set { _zone = value; OnPropertyChanged(nameof(Zone)); }
        }

        private string _prohibit;
        public string Prohibit
        {
            get { return _prohibit; }
            set { _prohibit = value; OnPropertyChanged(nameof(Prohibit)); }
        }
    }
}