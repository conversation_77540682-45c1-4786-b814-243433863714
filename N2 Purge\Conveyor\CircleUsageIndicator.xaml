﻿<UserControl x:Class="N2Purge.Conveyor.CircleUsageIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.Conveyor"
             xmlns:converter="clr-namespace:N2Purge.Converter"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="200"
             SizeChanged="UserControl_SizeChanged">
    <UserControl.Resources>
        <converter:PointConverter x:Key="PointConverter"/>
        <converter:HalfConverter x:Key="HalfConverter"/>
    </UserControl.Resources>
    <Grid>
        <!-- 背景圆环 -->
        <Ellipse 
            Fill="Transparent" 
            Stroke="{Binding BackgroundRingBrush, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}" 
            StrokeThickness="{Binding RingThickness, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}"
            Margin="{Binding RingThickness, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}"/>

        <!-- 进度圆环 -->
        <Path x:Name="ProgressPath"
            Stroke="{Binding ProgressRingBrush, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}" 
            StrokeThickness="{Binding RingThickness, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}"
            StrokeStartLineCap="Round" 
            StrokeEndLineCap="Round"
            Fill="Transparent">
        </Path>

        <!-- 中心文本 -->
        <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock 
                Text="{Binding UsagePercentage, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}, StringFormat={}{0}%}" 
                FontSize="24" 
                FontWeight="Bold"
                Foreground="{Binding TextColor, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}"
                HorizontalAlignment="Center"/>
            <TextBlock 
                Text="{Binding Caption, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}" 
                FontSize="14"
                Foreground="{Binding TextColor, RelativeSource={RelativeSource AncestorType=local:CircleUsageIndicator}}"
                HorizontalAlignment="Center"/>
        </StackPanel>
    </Grid>
</UserControl>
