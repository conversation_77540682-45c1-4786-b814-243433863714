﻿<UserControl x:Class="N2Purge.frmUserControl.frmMain"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
          
             xmlns:ctr="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800"
             Loaded="UserControl_Loaded"
             >
    <Grid>
        <Grid.RowDefinitions>
            <!--<RowDefinition Height="1*"/>
            --><!--<RowDefinition Height="100"/>--><!--
            <RowDefinition Height="1*"/>-->
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="30"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="100"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="30"/>
        
        </Grid.ColumnDefinitions>
        <Border Grid.Column="2" BorderThickness="1" BorderBrush="Black">
            <Grid>
                <ctr:Crane x:Name="crane" Margin="89,0,0,0">
                    <ctr:Crane.RenderTransform>
                        <TransformGroup>
                            <TranslateTransform X="{Binding CranePosition}" />
                            <RotateTransform Angle="90" CenterX="0" CenterY="0"/>
                        </TransformGroup>
                    </ctr:Crane.RenderTransform>
                </ctr:Crane>
                <Rectangle Stroke="Black"  StrokeThickness="1" Margin="20,0,20,0"/>
            </Grid>
        </Border>
        <Grid Grid.Column="1" x:Name="InnerGrid">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
               
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
        </Grid>
        <Grid Grid.Column="3" x:Name="RightGrid" Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
             
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
        </Grid>
        <Grid Grid.Row="0" Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                
            </Grid.RowDefinitions>
            <Label Content="14" Grid.Row="0" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="13" Grid.Row="1" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="12" Grid.Row="2" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="11" Grid.Row="3" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="10" Grid.Row="4" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="9" Grid.Row="5" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="8" Grid.Row="6" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="7" Grid.Row="7" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="6" Grid.Row="8" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="5" Grid.Row="9" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="4" Grid.Row="10" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="3" Grid.Row="11" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="2" Grid.Row="12" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="1" Grid.Row="13" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        </Grid>
        <Grid Grid.Row="2" Grid.Column="4">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Label Content="14" Grid.Row="0" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="13" Grid.Row="1" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="12" Grid.Row="2" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="11" Grid.Row="3" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="10" Grid.Row="4" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="9" Grid.Row="5" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="8" Grid.Row="6" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="7" Grid.Row="7" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="6" Grid.Row="8" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="5" Grid.Row="9" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="4" Grid.Row="10" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="3" Grid.Row="11" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="2" Grid.Row="12" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
            <Label Content="1" Grid.Row="13" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>

        </Grid>
    </Grid>
</UserControl>
