using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows.Input;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 录制回放配置管理器
    /// </summary>
    public class RecordReplayConfigManager
    {
        private static RecordReplayConfigManager _instance;
        private static readonly object _lock = new object();
        private RecordReplayConfig _config;
        private readonly string _configFilePath;

        private RecordReplayConfigManager()
        {
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "RecordReplayConfig.json");
            LoadConfig();
        }

        public static RecordReplayConfigManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new RecordReplayConfigManager();
                        }
                    }
                }
                return _instance;
            }
        }

        public RecordReplayConfig Config => _config;

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    _config = JsonSerializer.Deserialize<RecordReplayConfig>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    Logger.Instance.InfoLog($"录制回放配置加载成功: {_configFilePath}");
                }
                else
                {
                    _config = CreateDefaultConfig();
                    SaveConfig();
                    Logger.Instance.InfoLog("使用默认录制回放配置并保存到文件");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"加载录制回放配置失败: {ex.Message}", ex);
                _config = CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                string directory = Path.GetDirectoryName(_configFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(_configFilePath, json);
                Logger.Instance.InfoLog("录制回放配置保存成功");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"保存录制回放配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private RecordReplayConfig CreateDefaultConfig()
        {
            return new RecordReplayConfig
            {
                HotKeys = new HotKeysConfig
                {
                    StartRecording = new HotKeyConfig
                    {
                        Key = "S",
                        Modifiers = new List<string> { "Ctrl", "Shift" },
                        Description = "开始录制"
                    },
                    StopRecording = new HotKeyConfig
                    {
                        Key = "G",
                        Modifiers = new List<string> { "Ctrl", "Shift" },
                        Description = "停止录制"
                    },
                    StartPlayback = new HotKeyConfig
                    {
                        Key = "P",
                        Modifiers = new List<string> { "Ctrl", "Shift" },
                        Description = "开始回放"
                    },
                    StopPlayback = new HotKeyConfig
                    {
                        Key = "OemPipe",
                        Modifiers = new List<string> { "Ctrl", "Shift" },
                        Description = "停止回放 (Ctrl+Shift+\\)"
                    }
                },
                Recording = new RecordingConfig
                {
                    EnableMouseRecording = true,
                    EnableKeyboardRecording = true,
                    RecordMouseMovement = true,
                    AutoSave = true,
                    MaxRecordingDurationMinutes = 30
                },
                Playback = new PlaybackConfig
                {
                    DefaultSpeed = 1.0,
                    EnableMouseMovement = true,
                    EnableMouseClicks = true,
                    EnableKeyboard = true,
                    StartDelay = 1000
                },
                Storage = new StorageConfig
                {
                    StorageDirectory = "RecordReplay",
                    EnableCompression = true,
                    MaxFileCount = 50,
                    AutoCleanupDays = 30,
                    FileNameFormat = "Recording_{0:yyyyMMdd_HHmmss}"
                }
            };
        }

        /// <summary>
        /// 检查键盘事件是否匹配热键配置
        /// </summary>
        public bool IsHotKeyMatch(KeyEventArgs e, HotKeyConfig hotKeyConfig)
        {
            try
            {
                // 检查主键
                if (!Enum.TryParse<Key>(hotKeyConfig.Key, out Key configKey))
                {
                    return false;
                }

                if (e.Key != configKey)
                {
                    return false;
                }

                // 检查修饰键
                bool ctrlRequired = hotKeyConfig.Modifiers.Contains("Ctrl");
                bool shiftRequired = hotKeyConfig.Modifiers.Contains("Shift");
                bool altRequired = hotKeyConfig.Modifiers.Contains("Alt");

                bool ctrlPressed = (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control;
                bool shiftPressed = (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift;
                bool altPressed = (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt;

                return ctrlRequired == ctrlPressed && 
                       shiftRequired == shiftPressed && 
                       altRequired == altPressed;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"检查热键匹配失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取热键显示文本
        /// </summary>
        public string GetHotKeyDisplayText(HotKeyConfig hotKeyConfig)
        {
            try
            {
                var parts = new List<string>();
                
                if (hotKeyConfig.Modifiers.Contains("Ctrl"))
                    parts.Add("Ctrl");
                if (hotKeyConfig.Modifiers.Contains("Shift"))
                    parts.Add("Shift");
                if (hotKeyConfig.Modifiers.Contains("Alt"))
                    parts.Add("Alt");
                
                parts.Add(hotKeyConfig.Key);
                
                return string.Join("+", parts);
            }
            catch
            {
                return hotKeyConfig.Description ?? "未知热键";
            }
        }
    }

    #region 配置数据模型

    public class RecordReplayConfig
    {
        public HotKeysConfig HotKeys { get; set; } = new HotKeysConfig();
        public RecordingConfig Recording { get; set; } = new RecordingConfig();
        public PlaybackConfig Playback { get; set; } = new PlaybackConfig();
        public StorageConfig Storage { get; set; } = new StorageConfig();
    }

    public class HotKeysConfig
    {
        public HotKeyConfig StartRecording { get; set; } = new HotKeyConfig();
        public HotKeyConfig StopRecording { get; set; } = new HotKeyConfig();
        public HotKeyConfig StartPlayback { get; set; } = new HotKeyConfig();
        public HotKeyConfig StopPlayback { get; set; } = new HotKeyConfig();
    }

    public class HotKeyConfig
    {
        public string Key { get; set; } = "";
        public List<string> Modifiers { get; set; } = new List<string>();
        public string Description { get; set; } = "";
    }

    public class RecordingConfig
    {
        public bool EnableMouseRecording { get; set; } = true;
        public bool EnableKeyboardRecording { get; set; } = true;
        public bool RecordMouseMovement { get; set; } = true;
        public bool AutoSave { get; set; } = true;
        public int MaxRecordingDurationMinutes { get; set; } = 30;
    }

    public class PlaybackConfig
    {
        public double DefaultSpeed { get; set; } = 1.0;
        public bool EnableMouseMovement { get; set; } = true;
        public bool EnableMouseClicks { get; set; } = true;
        public bool EnableKeyboard { get; set; } = true;
        public int StartDelay { get; set; } = 1000;
    }

    public class StorageConfig
    {
        public string StorageDirectory { get; set; } = "RecordReplay";
        public bool EnableCompression { get; set; } = true;
        public int MaxFileCount { get; set; } = 50;
        public int AutoCleanupDays { get; set; } = 30;
        public string FileNameFormat { get; set; } = "Recording_{0:yyyyMMdd_HHmmss}";
    }

    #endregion
}
