# ChangePortState功能实现说明

## 🎯 **功能概述**

实现了用户右击shelf，在弹出菜单中选择"Change Port State"时，不仅通过`FinClientHelper.WriteTagValue`更新PLC状态，还能同步更新数据库和内存中的出入口状态，并实时刷新页面显示。

## 🔧 **实现架构**

### 数据流程图
```
用户右击Shelf → 弹出菜单 → 选择Change Port State
    ↓
ChangePortState_Click事件处理
    ↓
1. 调用FinClientHelper.WriteTagValue更新PLC
    ↓
2. UpdatePortStateInDatabaseAndMemory
    ├── UpdateDatabasePortState (更新数据库)
    ├── UpdateMemoryPortState (更新内存)
    ├── UpdateUIPortState (更新界面)
    └── RefreshTransferLocationLists (刷新下拉框数据源)
```

## 📊 **核心组件**

### 1. **主要修改的文件**
- `userControls/ShelfContextMenu.xaml.cs` - 主要实现逻辑
- `Conveyor/frmConvryorMain.xaml.cs` - 添加辅助方法

### 2. **涉及的数据结构**
- **数据库**: `tp_path_point` 表的 `is_enter` 和 `is_exit` 字段
- **内存**: `_objectContents` 中的 `PointInfo` 和 `ShelfViewModel`
- **监控**: `Conveyors` 中的 `IsEntry` 和 `IsExit` 属性
- **UI数据源**: `TransferExcuteViewModel` 中的 `SourceList` 和 `DestList`

## 🚀 **核心功能实现**

### 1. **ChangePortState_Click方法增强**

**原来的实现**:
```csharp
private void ChangePortState_Click(object sender, RoutedEventArgs e)
{
    // 只调用FinClientHelper.WriteTagValue
    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Enter");
}
```

**增强后的实现**:
```csharp
private async void ChangePortState_Click(object sender, RoutedEventArgs e)
{
    // 1. 调用FinClientHelper.WriteTagValue
    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Enter");
    
    // 2. 🔄 更新数据库和内存状态
    await UpdatePortStateInDatabaseAndMemory(targetPointID, true, false, conveyorMain);
}
```

### 2. **三层数据同步机制**

#### **🗄️ 数据库层 - UpdateDatabasePortState**
```csharp
private async Task UpdateDatabasePortState(int pointID, bool isEntry, bool isExit)
{
    var dbHelper = new DatabaseHelper<DBEntity.tp_path_point>(connectionString);
    string updateSql = $"is_enter = {(isEntry ? 1 : 0)}, is_exit = {(isExit ? 1 : 0)}";
    string whereCondition = $"point_id = {pointID}";
    await dbHelper.UpdateAsync(updateSql, whereCondition);
}
```

#### **💾 内存层 - UpdateMemoryPortState**
```csharp
private void UpdateMemoryPortState(int pointID, bool isEntry, bool isExit, frmConvryorMain conveyorMain)
{
    // 更新_objectContents中的PointInfo
    var objectContent = conveyorMain.GetObjectContentByPointID(pointID.ToString());
    objectContent.PointInfo.is_enter = isEntry;
    objectContent.PointInfo.is_exit = isExit;
    
    // 更新Conveyors中的IsEntry和IsExit
    var conveyor = conveyorMain.GetConveyorByPointID(pointID.ToString());
    conveyor.IsEntry = isEntry;
    conveyor.IsExit = isExit;
}
```

#### **🎨 界面层 - UpdateUIPortState**
```csharp
private void UpdateUIPortState(int pointID, bool isEntry, bool isExit, frmConvryorMain conveyorMain)
{
    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
    {
        objectContent.ShelfViewModel.IsEntry = isEntry;
        objectContent.ShelfViewModel.IsExit = isExit;
        
        // 强制刷新相关UI属性
        objectContent.ShelfViewModel.NotifyPropertyChanged(nameof(IsEntry));
        objectContent.ShelfViewModel.NotifyPropertyChanged(nameof(IsExit));
        objectContent.ShelfViewModel.NotifyPropertyChanged(nameof(EntryExitText));
        objectContent.ShelfViewModel.NotifyPropertyChanged(nameof(HasEntryExitMark));
        objectContent.ShelfViewModel.NotifyPropertyChanged(nameof(EntryExitBrush));
    }), DispatcherPriority.Background);
}
```

### 3. **下拉框数据源同步机制**

#### **🔄 TransferExcuteViewModel数据源刷新**
```csharp
/// <summary>
/// 刷新出入口状态列表（当数据库中的出入口状态发生变化时调用）
/// </summary>
public void RefreshPortStates()
{
    LoadPortStatesFromDatabase();
}

private void LoadPortStatesFromDatabase()
{
    Task.Factory.StartNew(() => {
        DatabaseHelper<tp_path_point> databaseHelper = new DatabaseHelper<tp_path_point>(connectionString);
        var pointList = databaseHelper.QueryAsync($"is_exit=1 or is_enter=1").Result;

        var newSourceList = new Dictionary<int, string>();
        var newDestList = new Dictionary<int, string>();

        foreach (var item in pointList)
        {
            if (item.is_enter)
                newSourceList[item.point_id] = item.disp_row + "," + item.disp_column;
            if (item.is_exit)
                newDestList[item.point_id] = item.disp_row + "," + item.disp_column;
        }

        // 在UI线程中更新属性
        Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
        {
            SourceList = newSourceList;
            DestList = newDestList;
        }));
    });
}
```

#### **📋 下拉框数据绑定**
在`frmAction.xaml`中，Source Location和Dest Location下拉框绑定到：
```xml
<ComboBox ItemsSource="{Binding SourceList}"
          SelectedValue="{Binding SourceLocation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
          DisplayMemberPath="Value" SelectedValuePath="Key"/>

<ComboBox ItemsSource="{Binding DestList}"
          SelectedValue="{Binding DestLocation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
          DisplayMemberPath="Value" SelectedValuePath="Key"/>
```

#### **🔗 数据源刷新调用**
```csharp
/// <summary>
/// 刷新Transfer页面的Source Location和Dest Location下拉框数据源
/// </summary>
private void RefreshTransferLocationLists()
{
    // 通过GlobalData访问TransferExcuteViewModel实例并刷新数据源
    GlobalData.frmActionViewModel.transferExcuteViewModel.RefreshPortStates();

    Proj.Log.Logger.Instance.OperationLog("[数据源刷新] Transfer页面的Source/Dest Location下拉框数据源已刷新");
}
```

### 4. **辅助方法实现**

在`frmConvryorMain.xaml.cs`中添加了两个关键的辅助方法：

```csharp
/// <summary>
/// 根据PointID获取ObjectContent
/// </summary>
public ObjectContent GetObjectContentByPointID(string pointID)
{
    return _objectContents.FirstOrDefault(x =>
        string.Compare(x.PointInfo.point_id.ToString(), pointID, true) == 0);
}

/// <summary>
/// 根据PointID获取Conveyor
/// </summary>
public IConveyor GetConveyorByPointID(string pointID)
{
    return Conveyors.FirstOrDefault(x =>
        string.Compare(x.PointID, pointID, true) == 0);
}
```

## 🎯 **状态切换逻辑**

### 入口 ↔ 出口切换
```csharp
if (currentVm.IsExit) // 当前是出口 → 切换为入口
{
    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Enter");
    await UpdatePortStateInDatabaseAndMemory(targetPointID, true, false, conveyorMain);
}
else if (currentVm.IsEntry) // 当前是入口 → 切换为出口
{
    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Exit");
    await UpdatePortStateInDatabaseAndMemory(targetPointID, false, true, conveyorMain);
}
```

## 📊 **UI显示效果**

### ShelfViewModel中的相关属性
```csharp
public string EntryExitText
{
    get
    {
        if (IsEntry && IsExit) return "IN/OUT";
        else if (IsEntry) return "IN";
        else if (IsExit) return "OUT";
        return string.Empty;
    }
}

public Brush EntryExitBrush
{
    get
    {
        if (IsEntry && IsExit) return Brushes.Purple;
        else if (IsEntry) return Brushes.Blue;
        else if (IsExit) return Brushes.Orange;
        return Brushes.Transparent;
    }
}
```

## 🔒 **错误处理机制**

### 1. **异常捕获**
```csharp
try
{
    await UpdatePortStateInDatabaseAndMemory(targetPointID, isEntry, isExit, conveyorMain);
}
catch (Exception ex)
{
    MessageBox.Show($"切换入口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
    Proj.Log.Logger.Instance.ExceptionLog($"切换入口失败: {ex.Message}\n{ex.StackTrace}");
}
```

### 2. **详细日志记录**
- 数据库更新日志
- 内存更新日志  
- UI更新日志
- 异常日志

## 📋 **测试验证**

### 1. **功能测试**
- 右击入口点位 → 选择Change Port State → 验证切换为出口
- 右击出口点位 → 选择Change Port State → 验证切换为入口
- 验证页面显示的入口/出口标记更新

### 2. **数据一致性测试**
- 验证数据库中`tp_path_point`表的`is_enter`和`is_exit`字段更新
- 验证内存中`_objectContents`和`Conveyors`的状态同步
- 验证UI显示与数据状态一致

### 3. **异常测试**
- 数据库连接异常
- UI线程异常
- PLC通信异常

## 🎉 **功能优势**

### 1. **数据一致性**
- 确保PLC、数据库、内存、UI、下拉框数据源五层数据完全同步
- 避免数据不一致导致的业务逻辑错误
- Transfer页面的Source/Dest Location下拉框实时反映最新的出入口配置

### 2. **实时性**
- 状态切换后立即更新所有相关数据
- UI实时反映最新的出入口状态
- 下拉框数据源即时刷新，无需重启应用

### 3. **用户体验**
- 用户切换出入口状态后，Transfer页面立即可用新的配置
- 避免用户困惑：为什么切换了状态但下拉框没有更新
- 提供完整的端到端功能体验

### 4. **可靠性**
- 完善的异常处理机制
- 详细的日志记录便于问题追踪
- 异步数据库操作避免UI阻塞

### 5. **可维护性**
- 清晰的分层架构
- 模块化的方法设计
- 详细的代码注释

## 🔮 **扩展建议**

### 1. **批量操作**
- 支持批量切换多个点位的出入口状态
- 提供批量操作的UI界面

### 2. **权限控制**
- 根据用户权限级别控制是否允许切换出入口
- 添加操作确认对话框

### 3. **历史记录**
- 记录出入口状态变更历史
- 提供状态变更审计功能

这个实现确保了用户操作后，系统的所有层面都能保持数据一致性，提供了完整可靠的出入口状态管理功能。

## 🔧 **编译错误修复**

### 问题描述
在实现过程中遇到了参数类型不匹配的编译错误，主要是因为`frmConvryorMain.Instance`可能返回`null`值。

### 解决方案
将所有相关方法的`conveyorMain`参数类型从：
```csharp
N2Purge.Conveyor.frmConvryorMain conveyorMain
```
修改为：
```csharp
N2Purge.Conveyor.frmConvryorMain? conveyorMain
```

### 修改的方法
- `UpdatePortStateInDatabaseAndMemory`
- `UpdateMemoryPortState`
- `UpdateUIPortState`

这样可以正确处理`Instance`属性可能返回`null`的情况，避免编译错误。

## 🎉 **功能总结**

现在当用户右击shelf选择"Change Port State"时，系统会：
1. ✅ 通过`FinClientHelper.WriteTagValue`更新PLC状态
2. ✅ 自动更新数据库中的`is_enter`和`is_exit`字段
3. ✅ 同步更新内存中`_objectContents`的出入口状态
4. ✅ 实时刷新页面上的出入口标记显示
5. ✅ **自动刷新Transfer页面的Source/Dest Location下拉框数据源**

### 🔄 **完整的数据同步流程**
```
用户操作 → PLC更新 → 数据库更新 → 内存更新 → UI更新 → 下拉框数据源刷新
```

这个实现确保了数据的完整一致性，用户操作后所有相关数据都会自动同步更新，包括Transfer页面的下拉框选项，提供了完整的端到端用户体验！
