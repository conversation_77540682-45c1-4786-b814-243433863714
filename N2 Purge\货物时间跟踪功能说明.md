# 货物时间跟踪功能说明

## 🎯 **功能概述**

在Process函数中实现了详细的货物到达和离开时间记录，提供精确的时间跟踪和停留时长计算，帮助分析传送带系统的运行效率。

## 📊 **核心功能**

### 1. **货物到达时间记录**
- ✅ 精确记录货物到达点位的时间（毫秒级）
- ✅ 检测货物ID变化，支持货物更换场景
- ✅ 区分入口点位和普通点位的处理逻辑

### 2. **货物离开时间记录**
- ✅ 精确记录货物离开点位的时间（毫秒级）
- ✅ 自动计算停留时长
- ✅ 区分出口点位和普通点位的处理逻辑

### 3. **传送路径跟踪**
- ✅ 完整记录货物从入口到出口的传送路径
- ✅ 记录经过的所有中间站点
- ✅ 生成详细的路径报告

## 🔍 **详细实现**

### **货物到达检测与记录**

#### **场景1：新货物到达**
```csharp
if ((PrevState == ActionState.None || PrevState == ActionState.Exit) && hasFoup)
{
    // 无货->有货：货物进入
    _EntryOn = DateTime.Now;
    currentFoupId = foupid;
    CurrentState = ActionState.Entry;
    PrevState = ActionState.Entry;

    _Log.OperationLog($"[{PointID}] 📦 货物到达：FoupID={foupid}, 到达时间={_EntryOn:yyyy-MM-dd HH:mm:ss.fff}");
}
```

**日志输出示例**:
```
[1] 📦 货物到达：FoupID=FOUP001, 到达时间=2024-01-15 14:30:25.123
```

#### **场景2：货物更换**
```csharp
else if (hasFoup && currentFoupId != foupid)
{
    // 货物ID发生变化，记录新货物到达
    var oldFoupId = currentFoupId;
    _EntryOn = DateTime.Now;
    currentFoupId = foupid;

    _Log.OperationLog($"[{PointID}] 🔄 货物更换：旧FoupID={oldFoupId ?? "null"} → 新FoupID={foupid}, 时间={_EntryOn:yyyy-MM-dd HH:mm:ss.fff}");
}
```

**日志输出示例**:
```
[3] 🔄 货物更换：旧FoupID=FOUP001 → 新FoupID=FOUP002, 时间=2024-01-15 14:32:18.456
```

### **货物离开检测与记录**

#### **离开时间记录**
```csharp
if ((PrevState == ActionState.Entry || PrevState == ActionState.OnTrans) && !hasFoup)
{
    // 有货->无货：货物离开
    _ExitOn = DateTime.Now;
    CurrentState = ActionState.Exit;
    PrevState = ActionState.Exit;

    _Log.OperationLog($"[{PointID}] 🚚 货物离开：FoupID={currentFoupId}, 离开时间={_ExitOn:yyyy-MM-dd HH:mm:ss.fff}");
}
```

**日志输出示例**:
```
[1] 🚚 货物离开：FoupID=FOUP001, 离开时间=2024-01-15 14:31:45.789
```

#### **停留时长计算**
```csharp
// 计算停留时间
if (_EntryOn.HasValue)
{
    var stayDuration = _ExitOn.Value - _EntryOn.Value;
    _Log.OperationLog($"[{PointID}] ⏱️ 停留时长：{stayDuration.TotalSeconds:F2}秒 (从 {_EntryOn:HH:mm:ss.fff} 到 {_ExitOn:HH:mm:ss.fff})");
}
```

**日志输出示例**:
```
[1] ⏱️ 停留时长：80.67秒 (从 14:30:25.123 到 14:31:45.789)
```

### **状态转换跟踪**

#### **货物稳定状态**
```csharp
else if (PrevState == ActionState.Entry && hasFoup && currentFoupId == foupid)
{
    // 货物稳定在点位上，转为搬运中状态
    CurrentState = ActionState.OnTrans;
    PrevState = ActionState.OnTrans;
    
    _Log.OperationLog($"[{PointID}] 🔄 货物稳定：FoupID={foupid}, 状态转为搬运中");
}
```

**日志输出示例**:
```
[1] 🔄 货物稳定：FoupID=FOUP001, 状态转为搬运中
```

### **传送路径管理**

#### **入口点位：创建新路径**
```csharp
if (IsEntry)
{
    // 入口点位：创建新路径并添加自己
    TransmissionPath‌ path = new TransmissionPath‌(foupid, PointID, _EntryOn);
    AddPath(path);
    _Log.OperationLog($"[{PointID}] 🚀 入口开始：FoupID={foupid}, EntryPointID={PointID}, 路径ID={path.Guid}");
}
```

#### **中间点位：添加到路径**
```csharp
// 添加到传送路径（如果还没添加）
if (!stationAdded && !IsEntry)
{
    AddToTransmissionPath(foupid, this);
    stationAdded = true;
    _Log.OperationLog($"[{PointID}] 📍 添加到传送路径：FoupID={foupid}");
}
```

#### **出口点位：完成路径**
```csharp
if (IsExit && !string.IsNullOrEmpty(currentFoupId))
{
    // 出口点位：完成路径并移除
    CompleteTransmissionPath(currentFoupId, PointID, _ExitOn);
    _Log.OperationLog($"[{PointID}] 🏁 出口完成：FoupID={currentFoupId}, 传送路径已完成");
}
```

## 📋 **日志输出示例**

### **完整的货物传送过程日志**
```
[1] 📦 货物到达：FoupID=FOUP001, 到达时间=2024-01-15 14:30:25.123
[1] 🔄 货物稳定：FoupID=FOUP001, 状态转为搬运中
[1] 🚀 入口开始：FoupID=FOUP001, EntryPointID=1, 路径ID=a1b2c3d4-e5f6-7890-abcd-ef1234567890
[1] 🚚 货物离开：FoupID=FOUP001, 离开时间=2024-01-15 14:31:45.789
[1] ⏱️ 停留时长：80.67秒 (从 14:30:25.123 到 14:31:45.789)

[3] 📦 货物到达：FoupID=FOUP001, 到达时间=2024-01-15 14:32:10.456
[3] 🔄 货物稳定：FoupID=FOUP001, 状态转为搬运中
[3] 📍 添加到传送路径：FoupID=FOUP001
[3] 🚚 货物离开：FoupID=FOUP001, 离开时间=2024-01-15 14:32:55.123
[3] ⏱️ 停留时长：44.67秒 (从 14:32:10.456 到 14:32:55.123)

[15] 📦 货物到达：FoupID=FOUP001, 到达时间=2024-01-15 14:33:20.789
[15] 🔄 货物稳定：FoupID=FOUP001, 状态转为搬运中
[15] 🚚 货物离开：FoupID=FOUP001, 离开时间=2024-01-15 14:34:05.456
[15] ⏱️ 停留时长：44.67秒 (从 14:33:20.789 到 14:34:05.456)
[15] 🏁 出口完成：FoupID=FOUP001, 传送路径已完成
```

### **传送路径完整报告**
```
ID: a1b2c3d4-e5f6-7890-abcd-ef1234567890, FoupID=FOUP001
EntryOn: 2024-01-15 14:30:25.123, EntryPointID: 1
Station - PointID: 3, EntryOn: 2024-01-15 14:32:10.456, ExitOn: 2024-01-15 14:32:55.123
ExitOn: 2024-01-15 14:34:05.456, ExitPointID: 15
```

## 🔧 **调试功能**

### **状态检查日志**
每100次循环记录一次状态检查信息：
```
[1] 状态检查: hasFoup=true, foupid=FOUP001, PrevState=Entry, CurrentState=OnTrans
[3] 状态检查: hasFoup=false, foupid=null, PrevState=Exit, CurrentState=Exit
```

### **异常处理**
```csharp
catch (Exception ex)
{
    _Log.ExceptionLog($"[{PointID}] MainLoop异常：{ex.Message}\r\n堆栈：{ex.StackTrace}");
    System.Threading.Thread.Sleep(1000);
}
```

## 📊 **性能分析应用**

### 1. **停留时间分析**
- 分析每个点位的平均停留时间
- 识别瓶颈点位
- 优化传送带配置

### 2. **传送效率分析**
- 计算端到端传送时间
- 分析传送路径效率
- 优化路径规划

### 3. **设备利用率分析**
- 统计点位占用时间
- 分析设备利用率
- 优化资源配置

## 🎯 **功能优势**

### 1. **精确时间记录**
- 毫秒级时间精度
- 完整的时间轴记录
- 详细的停留时长计算

### 2. **完整路径跟踪**
- 端到端路径记录
- 中间站点跟踪
- 路径报告生成

### 3. **丰富的日志信息**
- 图标化日志输出
- 结构化信息记录
- 便于问题诊断

### 4. **性能分析支持**
- 提供详细的性能数据
- 支持效率分析
- 便于系统优化

## 🧪 **测试功能**

### **快捷键测试**
- **Ctrl+Shift+M**: 测试货物时间跟踪功能
- **Ctrl+Shift+T**: 测试UI绑定
- **Ctrl+Shift+C**: 清除所有点位状态

### **测试输出示例**
```
开始测试货物时间跟踪功能
[测试] 点位 1: 类型=入口:True, 出口:False
[测试] 点位 1: 当前状态=PrevState:Entry, CurrentState:OnTrans
[测试] 点位 1: 时间记录=EntryOn:14:30:25.123, ExitOn:null
[测试] 点位 3: 类型=入口:False, 出口:False
[测试] 点位 3: 当前状态=PrevState:Exit, CurrentState:Exit
[测试] 点位 3: 时间记录=EntryOn:14:32:10.456, ExitOn:14:32:55.123
[测试] 当前活跃传送路径数量: 2
[测试] 路径 a1b2c3d4-e5f6-7890-abcd-ef1234567890: FoupID=FOUP001, 入口=1, 出口=进行中, 站点数=2
货物时间跟踪功能测试完成
```

## 🎯 **使用建议**

### 1. **日常监控**
- 定期查看日志文件中的时间记录
- 关注异常的停留时间
- 监控传送路径的完整性

### 2. **性能优化**
- 分析停留时间数据，识别瓶颈
- 优化传送带速度配置
- 调整点位间距和缓冲区

### 3. **故障诊断**
- 使用Ctrl+Shift+M快速检查当前状态
- 查看详细的时间轴日志
- 分析异常的传送路径

这个增强的Process函数提供了完整的货物时间跟踪功能，为传送带系统的性能分析和优化提供了强有力的数据支持！
