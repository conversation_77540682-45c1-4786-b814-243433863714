<ContextMenu x:Class="N2Purge.userControls.ShelfContextMenu"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d">
    <MenuItem Header="Shelf Info" Click="ShelfInfo_Click"/>
    <MenuItem Header="Transfer Carrier" Click="TransferCarrier_Click"/>
    <MenuItem Header="Install Carrier" Click="InstallCarrier_Click"/>
    <MenuItem Header="Remove Carrier" Click="RemoveCarrier_Click"/>
    <MenuItem Header="Scan" Click="Scan_Click"/>
    <MenuItem Header="Set Exit" Click="SetExit_Click" x:Name="menuSetExit"/>
    <MenuItem Header="Change Port State" Click="ChangePortState_Click" x:Name="menuChangePortState"/>
    <MenuItem Header="Enable" Click="Enable_Click"/>
    <MenuItem Header="Disable" Click="Disable_Click"/>
    <MenuItem Header="Clear Reserved" Click="ClearReserved_Click"/>
    <MenuItem Header="Clear Carrier" Click="ClearCarrier_Click"/>
    <MenuItem Header="Comment" Click="Comment_Click"/>
</ContextMenu>