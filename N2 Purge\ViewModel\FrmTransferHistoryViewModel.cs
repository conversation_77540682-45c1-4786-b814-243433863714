using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// TransferHistory Vm
    /// </summary>
    public class FrmTransferHistoryViewModel : VmPropertyChange
    {
        private DateTime _startTime;
        private DateTime _endTime;
        private string _cmdID;
        private string _carrierID;
        private string _cmdSource;
        private string _cmdState;
        public DateTime CmdTime { get; set; }
     
        public int _Priority { get; set; }
        public string _CmdType { get; set; }
        public string _StrLocation { get; set; }
        public string _DestLocation { get; set; }
        public int _CalcPriority { get; set; }
        public string _TransferType { get; set; }
        public string _CraneName { get; set; }
        public DateTime _CraneStartTime { get; set; }
        public DateTime _CraneEndTime { get; set; }
        public string _CmdState { get; set; }
        public string _DelayReason { get; set; }
        public string _Comment { get; set; }
        private ObservableCollection<TransferHistoryModel> _dataList;

        public DateTime StartTime
        {
            get => _startTime;
            set { _startTime = value; OnPropertyChanged("StartTime"); }
        }

        public DateTime EndTime
        {
            get => _endTime;
            set { _endTime = value; OnPropertyChanged("EndTime"); }
        }

        public string CmdID
        {
            get => _cmdID;
            set { _cmdID = value; OnPropertyChanged("CmdID"); }
        }

        public string CarrierID
        {
            get => _carrierID;
            set { _carrierID = value; OnPropertyChanged("CarrierID"); }
        }
        public int Priority
        {
            get => _Priority;
            set { _Priority = value; OnPropertyChanged("Priority"); }
        }

        public string CmdType
        {
            get => _CmdType;
            set { _CmdType = value; OnPropertyChanged("CmdType"); }
        }

        public string StrLocation
        {
            get => _StrLocation;
            set { _StrLocation = value; OnPropertyChanged("StrLocation"); }
        }

        public string DestLocation
        {
            get => _DestLocation;
            set { _DestLocation = value; OnPropertyChanged("DestLocation"); }
        }
        public int CalcPriority
        {
            get => _CalcPriority;
            set { _CalcPriority = value; OnPropertyChanged("CalcPriority"); }
        }
        public string TransferType
        {
            get => _TransferType;
            set { _TransferType = value; OnPropertyChanged("TransferType"); }
        }
        public string CraneName
        {
            get => _CraneName;
            set { _CraneName = value; OnPropertyChanged("CraneName"); }
        }
        public DateTime CraneStartTime
        {
            get => _CraneStartTime;
            set { _CraneStartTime = value; OnPropertyChanged("CraneStartTime"); }
        }
        public DateTime CraneEndTime
        {
            get => _CraneEndTime;
            set { _CraneEndTime = value; OnPropertyChanged("CraneEndTime"); }
        }
        public string CmdState
        {
            get => _CmdState;
            set { _CmdState = value; OnPropertyChanged("CmdState"); }
        }
        public string DelayReason
        {
            get => _DelayReason;
            set { _DelayReason = value; OnPropertyChanged("DelayReason"); }
        }
        public string Comment
        {
            get => _Comment;
            set { _Comment = value; OnPropertyChanged("Comment"); }
        }
        public ObservableCollection<TransferHistoryModel> DataList
        {
            get => _dataList;
            set { _dataList = value; OnPropertyChanged("DataList"); }
        }

        public FrmTransferHistoryViewModel()
        {
            StartTime = DateTime.Now.AddDays(-1);
            EndTime = DateTime.Now;
            DataList = new ObservableCollection<TransferHistoryModel>();
        }

       
    }

    public class TransferHistoryModel
    {
        public DateTime CmdTime { get; set; }
        public string CmdID { get; set; }
        public string CarrierID { get; set; }
        public string CmdSource { get; set; }
        public int Priority { get; set; }
        public string CmdType { get; set; }
        public string StrLocation { get; set; }
        public string DestLocation { get; set; }
        public int CalcPriority { get; set; }
        public string TransferType { get; set; }
        public string CraneName { get; set; }
        public DateTime CraneStartTime { get; set; }
        public DateTime CraneEndTime { get; set; }
        public string CmdState { get; set; }
        public string DelayReason { get; set; }
        public string Comment { get; set; }
    }
}