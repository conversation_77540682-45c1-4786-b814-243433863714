using N2Purge.ViewModel;
using N2Purge.userControls;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PlcTools;

namespace N2Purge.Conveyor
{
    /// <summary>
    /// frmCVIO.xaml 的交互逻辑
    /// </summary>
    public partial class frmCVIO : UserControl
    {
        private Dictionary<string, ObservableCollection<IOViewModel>> iOViewModels = new Dictionary<string, ObservableCollection<IOViewModel>>();
        private DispatcherTimer timer;
        private string currentCVKey = "CV1"; // 默认显示CV1的数据
        private PlcHelper plcHelper;
        
        public frmCVIO()
        {
            InitializeComponent();
            
            // 初始化PlcHelper并加载PLC点位配置
            InitializePlcHelper();
            
            LoadConfigFromJson();
            storageinfo.DataContext = GlobalData.gbfrmInfoViewModel;
            GlobalData.ShelfLeftDbClick += GlobalData_ShelfLeftDbClick;
            
            // 设置定时器更新IO状态
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1); // 每秒更新一次
            timer.Tick += Timer_Tick;
            timer.Start();
        }
        
        private async void InitializePlcHelper()
        {
            try
            {
                plcHelper = new PlcHelper();
                
                // 加载PLC配置文件
                string configDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                string plcConfigPath = System.IO.Path.Combine(configDir, "plc_config.json");
                if (File.Exists(plcConfigPath))
                {
                    plcHelper.LoadPlcConfig(plcConfigPath);
                }
                else
                {
                    // 如果PLC配置文件不存在，可以创建一个默认的配置
                    string defaultConfig = @"{
  ""Name"": ""STKPLC"",
  ""IP"": ""*************"",
  ""PORT"": ""9600""
}";
                    File.WriteAllText(plcConfigPath, defaultConfig);
                    plcHelper.LoadPlcConfig(plcConfigPath);
                }
                
                // 连接PLC设备
                PLCResult connectResult = plcHelper.ConnectEquipment();
                if (!connectResult.code)
                {
                    Console.WriteLine($"连接PLC设备失败: {connectResult.msg}");
                }
                
                // 加载PLC点位配置
                PLCResult loadResult = await plcHelper.LoadPLCData();
                if (!loadResult.code)
                {
                    Console.WriteLine($"加载PLC点位配置失败: {loadResult.msg}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化PLC Helper时出错: {ex.Message}");
            }
        }

        private void GlobalData_ShelfLeftDbClick(string arg1, ShelfViewModel model)
        {
            storageinfo.GlobalData_ShelfLeftDbClick(arg1, model);
            
            // 根据arg1参数切换DataGrid的数据源
            if (iOViewModels.ContainsKey(arg1))
            {
                currentCVKey = arg1;
                dgv.ItemsSource = iOViewModels[arg1];
            }
        }

        private void LoadConfigFromJson()
        {
            try
            {
                // 确保Config目录存在
                string configDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                // 从JSON文件加载配置
                string configPath = System.IO.Path.Combine(configDir, "cvio_config.json");
                
                if (File.Exists(configPath))
                {
                    string jsonContent = File.ReadAllText(configPath);
                    var cvioConfig = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(jsonContent);
                    
                    if (cvioConfig != null)
                    {
                        // 清空现有数据
                        iOViewModels.Clear();
                        
                        // 遍历配置中的每个CV
                        foreach (var kvp in cvioConfig)
                        {
                            string cvKey = kvp.Key;
                            var ioList = new ObservableCollection<IOViewModel>();
                            
                            // 遍历每个CV的IO点
                            foreach (var ioItem in kvp.Value)
                            {
                                ioList.Add(new IOViewModel
                                {
                                    CVNmae = ioItem["CVNmae"],
                                    CVIoNmae = ioItem["CVIoNmae"],
                                    CVIoValue = ioItem["CVIoValue"],
                                    PLCTagName = ioItem.ContainsKey("PLCTagName") ? ioItem["PLCTagName"] : string.Empty
                                });
                            }
                            
                            // 将IO点列表添加到字典中
                            iOViewModels[cvKey] = ioList;
                        }
                        
                        // 默认显示第一个CV的数据
                        if (iOViewModels.Count > 0)
                        {
                            currentCVKey = iOViewModels.Keys.First();
                            dgv.ItemsSource = iOViewModels[currentCVKey];
                        }
                    }
                }
                else
                {
                    MessageBox.Show($"配置文件不存在: {configPath}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    // 创建默认数据
                    CreateDefaultData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                // 创建默认数据
                CreateDefaultData();
            }
        }
        
        private void CreateDefaultData()
        {
            // 创建默认数据
            var cv1List = new ObservableCollection<IOViewModel>();
            cv1List.Add(new IOViewModel { CVNmae = "CV1", CVIoNmae = "Ready", CVIoValue = "0", PLCTagName = "CV1_Ready" });
            cv1List.Add(new IOViewModel { CVNmae = "CV1", CVIoNmae = "Busy", CVIoValue = "0", PLCTagName = "CV1_Busy" });
            cv1List.Add(new IOViewModel { CVNmae = "CV1", CVIoNmae = "Error", CVIoValue = "0", PLCTagName = "CV1_Error" });
            
            var cv2List = new ObservableCollection<IOViewModel>();
            cv2List.Add(new IOViewModel { CVNmae = "CV2", CVIoNmae = "Ready", CVIoValue = "0", PLCTagName = "CV2_Ready" });
            cv2List.Add(new IOViewModel { CVNmae = "CV2", CVIoNmae = "Busy", CVIoValue = "0", PLCTagName = "CV2_Busy" });
            cv2List.Add(new IOViewModel { CVNmae = "CV2", CVIoNmae = "Error", CVIoValue = "0", PLCTagName = "CV2_Error" });
            
            var cv3List = new ObservableCollection<IOViewModel>();
            cv3List.Add(new IOViewModel { CVNmae = "CV3", CVIoNmae = "Ready", CVIoValue = "0", PLCTagName = "CV3_Ready" });
            cv3List.Add(new IOViewModel { CVNmae = "CV3", CVIoNmae = "Busy", CVIoValue = "0", PLCTagName = "CV3_Busy" });
            cv3List.Add(new IOViewModel { CVNmae = "CV3", CVIoNmae = "Error", CVIoValue = "0", PLCTagName = "CV3_Error" });
            
            // 将数据添加到字典中
            iOViewModels["CV1"] = cv1List;
            iOViewModels["CV2"] = cv2List;
            iOViewModels["CV3"] = cv3List;
            
            // 默认显示CV1的数据
            currentCVKey = "CV1";
            dgv.ItemsSource = iOViewModels[currentCVKey];
            
            // 保存默认配置到JSON文件
            SaveConfigToJson();
        }
        
        private void Timer_Tick(object sender, EventArgs e)
        {
            // 只更新当前显示的数据源
            if (iOViewModels.ContainsKey(currentCVKey))
            {
                var items = iOViewModels[currentCVKey];
                if (items != null)
                {
                    bool dataChanged = false;
                    
                    foreach (var item in items)
                    {
                        // 如果有配置PLC标签名称，则从PLC读取数据
                        if (!string.IsNullOrEmpty(item.PLCTagName))
                        {
                            try
                            {
                                // 从PLC读取数据
                                PLCResult result = plcHelper.PLCRead(item.PLCTagName);
                                
                                if (result.code) // 读取成功
                                {
                                    // 将PLC读取的值转换为字符串并更新到IOViewModel
                                    string newValue = result.data?.ToString() ?? "0";
                                    
                                    // 如果是布尔值，转换为0或1
                                    if (result.data is bool boolValue)
                                    {
                                        newValue = boolValue ? "1" : "0";
                                    }
                                    
                                    // 如果值发生变化，则更新
                                    if (item.CVIoValue != newValue)
                                    {
                                        item.CVIoValue = newValue;
                                        dataChanged = true;
                                    }
                                }
                                else
                                {
                                    // 读取失败，记录错误信息
                                    Console.WriteLine($"读取PLC点位失败: {item.PLCTagName}, 错误: {result.msg}");
                                }
                            }
                            catch (Exception ex)
                            {
                                // 捕获并记录异常
                                Console.WriteLine($"读取PLC点位异常: {item.PLCTagName}, 异常: {ex.Message}");
                            }
                        }
                    }
                    
                    // 如果数据有变化，保存到JSON文件
                    if (dataChanged)
                    {
                        SaveConfigToJson();
                    }
                }
            }
        }
        
        private void SaveConfigToJson()
        {
            try
            {
                // 准备要保存的数据
                var configData = new Dictionary<string, List<Dictionary<string, string>>>();
                
                foreach (var kvp in iOViewModels)
                {
                    string cvKey = kvp.Key;
                    var ioItems = new List<Dictionary<string, string>>();
                    
                    foreach (var ioViewModel in kvp.Value)
                    {
                        var ioItem = new Dictionary<string, string>
                        {
                            { "CVNmae", ioViewModel.CVNmae },
                            { "CVIoNmae", ioViewModel.CVIoNmae },
                            { "CVIoValue", ioViewModel.CVIoValue },
                            { "PLCTagName", ioViewModel.PLCTagName ?? string.Empty }
                        };
                        
                        ioItems.Add(ioItem);
                    }
                    
                    configData[cvKey] = ioItems;
                }
                
                // 将数据序列化为JSON
                string jsonContent = JsonConvert.SerializeObject(configData, Formatting.Indented);
                
                // 确保Config目录存在
                string configDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                // 保存到文件
                string configPath = System.IO.Path.Combine(configDir, "cvio_config.json");
                File.WriteAllText(configPath, jsonContent);
            }
            catch (Exception ex)
            {
                // 在实际应用中，可能需要记录日志而不是显示消息框
                // MessageBox.Show($"保存配置文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Console.WriteLine($"保存配置文件时出错: {ex.Message}");
            }
        }
    }
}
