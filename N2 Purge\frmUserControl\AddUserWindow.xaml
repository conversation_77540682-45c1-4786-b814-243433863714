<Window x:Class="N2Purge.frmUserControl.AddUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="添加用户" Height="300" Width="400" WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Label Grid.Row="0" Grid.Column="0" Content="用户名：" Margin="5"/>
        <TextBox x:Name="txtUsername" Grid.Row="0" Grid.Column="1" Margin="5"/>

        <Label Grid.Row="1" Grid.Column="0" Content="密码：" Margin="5"/>
        <PasswordBox x:Name="txtPassword" Grid.Row="1" Grid.Column="1" Margin="5"/>

        <Label Grid.Row="2" Grid.Column="0" Content="用户等级：" Margin="5"/>
        <ComboBox x:Name="cmbUserLevel" Grid.Row="2" Grid.Column="1" Margin="5">
            <ComboBoxItem Content="1"/>
            <ComboBoxItem Content="2"/>
            <ComboBoxItem Content="3"/>
        </ComboBox>

        <StackPanel Grid.Row="3" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="btnConfirm" Content="确认" Width="80" Height="30" Margin="5" Click="btnConfirm_Click"/>
            <Button x:Name="btnCancel" Content="取消" Width="80" Height="30" Margin="5" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>