﻿using N2Purge.userControls;
using N2Purge.ViewModel;
using N2Purge.userControls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.IO;
using N2Purge.CcnfigClass;
using System.Text.Json.Serialization;
using System.Text.Json;
using N2Purge.ViewModel;
using System.ComponentModel;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmMain.xaml 的交互逻辑
    /// </summary>
    public partial class frmMain : UserControl
    {
        public frmMain()
        {
            InitializeComponent();
            if (DesignerProperties.GetIsInDesignMode(this))
            {
                return;
            }
            //GlobalData.gbcranevm.CranePosition = 100;
            //GlobalData.gbcranevm.FillColor = Brushes.Yellow;
            //GlobalData.gbcranevm.IsVisible = true;
            //this.DataContext = GlobalData.gbcranevm;

        }
        public void loadShelfConfig(string path,Grid grid, bool left)
        {
           
            string json = File.ReadAllText(path);
            var shelfConfigs = JsonSerializer.Deserialize<List<ShelfConfig>>(json);
       
            foreach (var config in shelfConfigs)
            {
                Shelf shelf = new Shelf();
                shelf.ShelfleftClicked += Shelf_ShelfleftClicked;
                shelf.HorizontalAlignment = HorizontalAlignment.Stretch;
                shelf.VerticalAlignment = VerticalAlignment.Stretch;
                shelf.Margin = new Thickness(5,5,5,5); // 设置控件的间隙
                grid.Children.Add(shelf);

                ShelfViewModel viewModel = new ShelfViewModel
                {
                    ShelfLocation = config.ShelfLocation,
                    ShelfColor = config.ShelfColor,
                    IsPurge = config.IsPurge
                };
                SingleShelfInfoViewModel singleShelfInfoViewModel = new SingleShelfInfoViewModel();
                if (!GlobalData.gbshelfinfovm.ContainsKey(viewModel.ShelfLocation))
                {
                    GlobalData.gbshelfinfovm.Add(viewModel.ShelfLocation, singleShelfInfoViewModel);
                }
              
                shelf.DataContext = viewModel;
                if (left)
                {
                    Grid.SetRow(shelf, config.Row);
                    Grid.SetColumn(shelf, config.Column);
                    if (!GlobalData.gbshelfvm.ContainsKey(viewModel.ShelfLocation))
                    {
                        GlobalData.gbshelfvm.Add(viewModel.ShelfLocation, viewModel);
                    }
                   
                }
                else
                {
                    Grid.SetRow(shelf,  config.Row );
                    Grid.SetColumn(shelf, config.Column);

                    if (!GlobalData.gbshelfvm.ContainsKey(viewModel.ShelfLocation))
                    {
                        GlobalData.gbshelfvm.Add(viewModel.ShelfLocation, viewModel);
                    }
                }
                // 设置shelf所在的行和列
            
            }

        }

        private void Shelf_ShelfleftClicked(object? sender, ShelfEventArgs e)
        {
            GlobalData.ControlleftClick(e.ShelfLocation);
        }

        public void loadPortConfig(Grid gridleft,Grid gridright)
        {
            string path2 = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\portConfig.json");
            string json1 = File.ReadAllText(path2);
            var portConfigs = JsonSerializer.Deserialize<List<PortConfig>>(json1);
            foreach (var config in portConfigs)
            {
                Port port = new Port();
                port.HorizontalAlignment = HorizontalAlignment.Stretch;
                port.VerticalAlignment = VerticalAlignment.Stretch;
                if (config.IsPurge)
                {

                    switch (config.PortLocation)
                    {
                        case "30101":
                            port.Margin = new Thickness(10, 3, 70, 3); // 设置控件的间隙
                            break;
                        case "30102":
                            port.Margin = new Thickness(10, 3, 70, 3); // 设置控件的间隙
                            break;
                        case "30103":
                            port.Margin = new Thickness(3); // 设置控件的间隙
                            break;
                        case "40101":
                            port.Margin = new Thickness(10, 3, 70, 3); // 设置控件的间隙
                            break;
                        case "40102":
                            port.Margin = new Thickness(70, 3, 10, 3); // 设置控件的间隙
                            break;
                        case "40103":
                            port.Margin = new Thickness(10, 3, 70, 3); // 设置控件的间隙
                            break;
                        case "40104":
                            port.Margin = new Thickness(70, 3, 10, 3); // 设置控件的间隙
                            break;
                    }

                    gridleft.Children.Add(port);

                    // 设置port所在的行、列及跨度

                }
                else
                {
                    port.Margin = new Thickness(3); // 设置控件的间隙
                    gridright.Children.Add(port);
                }
                Grid.SetRow(port, config.Row);
                Grid.SetColumn(port, config.Column);
                Grid.SetRowSpan(port, config.RowSpan);
                Grid.SetColumnSpan(port, config.ColumnSpan);
            }

        }
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            string path = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\shelfConfig.json");
            var innerGrid = this.FindName("InnerGrid") as Grid;
            loadShelfConfig(path,innerGrid,true);
            string path1 = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\RightshelfConfig.json");
            var rightGrid = this.FindName("RightGrid") as Grid;
            loadShelfConfig(path1, rightGrid,false);
            Portnew portnew=new Portnew();
            innerGrid.Children.Add(portnew);
            Grid.SetColumn(portnew,3);
            Grid.SetColumnSpan(portnew,2); 
            Grid.SetRow(portnew, 13);

            // loadPortConfig(innerGrid, rightGrid);


        }
        public void OnMessage()
        {
            //GlobalData.gbsignalhelper.On<ShelfViewModel>("", HandleReceivedShelfMessage);
        }
        public void HandleReceivedShelfMessage(ShelfViewModel str)
        {
            if (str!=null)
            {

            }
        }
    }
}
