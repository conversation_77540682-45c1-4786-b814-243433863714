using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// Remove Vm
    /// </summary>
    public class RemoveViewModel : VmPropertyChange
    {
        private string? cstId;
        private string? location;

        public string CstID
        {
            get { return cstId; }
            set
            {
                if (cstId != value)
                {
                    cstId = value;
                    OnPropertyChanged(nameof(CstID));
                }
            }
        }

        public string Location
        {
            get { return location; }
            set
            {
                if (location != value)
                {
                    location = value;
                    OnPropertyChanged(nameof(Location));
                }
            }
        }

     
    }
}