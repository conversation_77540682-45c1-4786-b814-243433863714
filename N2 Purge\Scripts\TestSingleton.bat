@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    N2 Purge 单例模式测试工具
echo ========================================
echo.

set "APP_PATH=%~dp0..\bin\Debug\N2Purge.exe"

:: 检查应用程序文件
if not exist "%APP_PATH%" (
    echo [错误] 找不到应用程序文件: %APP_PATH%
    echo.
    echo 请确保：
    echo 1. N2Purge.exe 文件存在于 bin\Debug 目录中
    echo 2. 或者修改此脚本中的 APP_PATH 变量
    echo.
    pause
    exit /b 1
)

echo [信息] 应用程序文件: %APP_PATH%
echo.

echo 测试说明：
echo 1. 此工具将启动两个N2Purge实例
echo 2. 如果单例模式正常工作，第二个实例应该自动退出
echo 3. 如果看到两个实例都在运行，说明单例模式有问题
echo.

set /p confirm=是否开始测试？(Y/N): 
if /i "%confirm%" neq "Y" (
    echo 测试已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo 开始测试...
echo ========================================
echo.

:: 启动第一个实例
echo [步骤 1] 启动第一个实例...
start "N2Purge-Instance1" "%APP_PATH%"
echo [信息] 第一个实例已启动，等待5秒让其完全加载...
timeout /t 5 /nobreak >nul

:: 检查第一个实例是否成功启动
for /f %%i in ('tasklist /fi "imagename eq N2Purge.exe" 2^>nul ^| find /c "N2Purge.exe"') do set first_count=%%i
echo [检查] 第一个实例启动后，运行中的实例数量: %first_count%

:: 启动第二个实例
echo [步骤 2] 启动第二个实例...
start "N2Purge-Instance2" "%APP_PATH%"
echo [信息] 第二个实例已启动，等待10秒观察单例检查结果...
timeout /t 10 /nobreak >nul

:: 检查运行中的实例数量
echo [步骤 3] 检查运行中的实例数量...
for /f %%i in ('tasklist /fi "imagename eq N2Purge.exe" 2^>nul ^| find /c "N2Purge.exe"') do set instance_count=%%i

echo.
echo ========================================
echo 测试结果
echo ========================================
echo.

if %instance_count% equ 1 (
    echo ✅ 测试通过！
    echo [结果] 检测到 %instance_count% 个N2Purge实例正在运行
    echo [说明] 单例模式正常工作，第二个实例已自动退出
) else if %instance_count% equ 0 (
    echo ❓ 未检测到运行中的实例
    echo [结果] 可能所有实例都已退出，或者启动失败
    echo [建议] 请手动启动程序检查是否有其他问题
) else (
    echo ❌ 测试失败！
    echo [结果] 检测到 %instance_count% 个N2Purge实例正在运行
    echo [说明] 单例模式未生效，多个实例同时运行
    echo.
    echo 可能的原因：
    echo 1. Mutex创建失败
    echo 2. 权限问题
    echo 3. 代码逻辑错误
    echo 4. 异常处理导致默认允许运行
    echo.
    echo 建议操作：
    echo 1. 检查应用程序日志文件
    echo 2. 以管理员身份运行测试
    echo 3. 检查防病毒软件是否阻止Mutex创建
)

echo.
echo 当前运行的N2Purge进程：
tasklist /fi "imagename eq N2Purge.exe" 2>nul | findstr "N2Purge.exe"

echo.
set /p cleanup=是否终止所有N2Purge进程？(Y/N): 
if /i "%cleanup%" equ "Y" (
    echo [清理] 正在终止所有N2Purge进程...
    taskkill /f /im N2Purge.exe >nul 2>&1
    if %errorlevel% equ 0 (
        echo [成功] 所有N2Purge进程已终止
    ) else (
        echo [信息] 未找到运行中的N2Purge进程
    )
)

echo.
echo 测试完成！
pause
