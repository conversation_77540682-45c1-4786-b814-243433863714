using System.Windows;
using System.Windows.Controls;
using System.ComponentModel;
using N2Purge.ViewModel;
using Org.BouncyCastle.Asn1.X509;
using System.Net.NetworkInformation;

namespace N2Purge.userControls
{
    public class IOStatusViewModel : VmPropertyChange
    {
        public string? IoName;
        public string? IoAddress;
        private int _status;
        private string _displayText;
        private System.Windows.Media.SolidColorBrush _backgroundColor;
        
        public int Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    UpdateBackgroundColor(); // 更新背景色
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        public string DisplayText
        {
            get => _displayText;
            set
            {
                if (_displayText != value)
                {
                    _displayText = value;
                    OnPropertyChanged(nameof(DisplayText));
                }
            }
        }
        
        public System.Windows.Media.SolidColorBrush BackgroundColor
        {
            get => _backgroundColor;
            private set
            {
                if (_backgroundColor != value)
                {
                    _backgroundColor = value;
                    OnPropertyChanged(nameof(BackgroundColor));
                }
            }
        }
        
        private void UpdateBackgroundColor()
        {
            BackgroundColor = Status switch
            {
                0 => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),      // 默认状态
                1 => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green),     // 成功状态
                2 => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red),       // 错误状态
                3 => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Yellow),    // 警告状态
                _ => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray)       // 未知状态
            };
        }
        
        public IOStatusViewModel()
        {
            _status = 0;
            _displayText = string.Empty;
            _backgroundColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray);
        }
    }
    public partial class CmdStatus : UserControl
    {
        public CmdStatus()
        {
            InitializeComponent();
            
            // 注意：不要在这里设置DataContext，因为我们使用的是IOStatusViewModel
            // 而不是CmdStatusViewModel
        }
        
        // 我们不再需要这个方法，因为DataContext会通过绑定自动设置
        // 如果将来需要手动设置ViewModel，可以添加一个类似的方法

     

       
    }
}