﻿<UserControl x:Class="N2Purge.Conveyor.frmConveyor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.Conveyor"
             xmlns:xtrl="clr-namespace:N2Purge.silan"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="300"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <Border Grid.Column="1" Grid.Row="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="60"/>

                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <xtrl:Shelf x:Name="S40105" Margin="0" Grid.Column="0" Grid.Row="0" />
                <xtrl:Shelf  x:Name="S4010501" Margin="0" Grid.Column="0" Grid.Row="1" />
                <xtrl:Shelf  x:Name="S4010502" Margin="0" Grid.Column="0" Grid.Row="2" />
                <xtrl:Shelf  x:Name="S4010503" Margin="0,0,0,0" Grid.Column="0" Grid.Row="3" />
                <xtrl:Shelf  x:Name="S4010504" Margin="0,0" Grid.Column="0" Grid.Row="4" />
                <xtrl:Shelf  x:Name="S4010505" Margin="0,0" Grid.Column="1" Grid.Row="4" />
            </Grid>
        </Border>
        <Border Grid.Column="2" Grid.Row="1">
            
            <Grid Height="250">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                <Label Content="Cassette ID" Margin="4" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                <Label Content="Source Location" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                <Label Content="Dest Location" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                <Label Content="Priority" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                <TextBox Margin="4" x:Name="CarrierId" Text="{Binding CarrierId}" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="black" VerticalContentAlignment="Center"   TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                <TextBox Margin="4" x:Name="SourceLocation" Text="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                <TextBox Margin="4" x:Name="DestLocation" Text="{Binding DestLocation}" Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                <TextBox Margin="4" x:Name="Priority" Text="{Binding Priority}" Background="Transparent" Grid.Column="3" Grid.Row="3" TextBlock.Foreground="Black" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                <Button x:Name="btnExcute" Content="Excute" Click="btnExcute_Click" Grid.Column="0" Grid.Row="4" Grid.ColumnSpan="2" Background="Snow" Margin=" 3"/>
                <TextBlock x:Name="lblTransferResult" Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="5" Margin="2" Background="LightYellow"/>
            </Grid>
            
        </Border>
    </Grid>
</UserControl>
