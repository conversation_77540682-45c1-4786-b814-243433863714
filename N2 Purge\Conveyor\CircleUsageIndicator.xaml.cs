using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.Conveyor
{
    /// <summary>
    /// CircleUsageIndicator.xaml 的交互逻辑
    /// </summary>
    public partial class CircleUsageIndicator : UserControl
    {
        #region 依赖属性定义

        // 占用率属性 (0-100)
        public static readonly DependencyProperty UsagePercentageProperty =
            DependencyProperty.Register(
                "UsagePercentage",
                typeof(double),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(0.0, OnUsagePercentageChanged));

        // 圆环厚度属性
        public static readonly DependencyProperty RingThicknessProperty =
            DependencyProperty.Register(
                "RingThickness",
                typeof(double),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(10.0, OnRingThicknessChanged));

        // 背景圆环颜色
        public static readonly DependencyProperty BackgroundRingBrushProperty =
            DependencyProperty.Register(
                "BackgroundRingBrush",
                typeof(Brush),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(new SolidColorBrush(Colors.LightGray)));

        // 进度圆环颜色
        public static readonly DependencyProperty ProgressRingBrushProperty =
            DependencyProperty.Register(
                "ProgressRingBrush",
                typeof(Brush),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(new SolidColorBrush(Colors.LimeGreen)));

        // 文本前景色
        public static readonly DependencyProperty TextForegroundProperty =
            DependencyProperty.Register(
                "TextForeground",
                typeof(Brush),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(new SolidColorBrush(Colors.Black)));

        // 标题文本
        public static readonly DependencyProperty CaptionProperty =
            DependencyProperty.Register(
                "Caption",
                typeof(string),
                typeof(CircleUsageIndicator),
                new PropertyMetadata("使用率"));

        // 标题文本颜色
        public static readonly DependencyProperty CaptionForegroundProperty =
            DependencyProperty.Register(
                "CaptionForeground",
                typeof(Brush),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(new SolidColorBrush(Colors.Gray)));

        // 文本颜色属性
        public static readonly DependencyProperty TextColorProperty =
            DependencyProperty.Register(
                "TextColor",
                typeof(Brush),
                typeof(CircleUsageIndicator),
                new PropertyMetadata(new SolidColorBrush(Colors.Black)));

        // 文本颜色属性已在上面定义

        #endregion

        #region 属性访问器

        public double UsagePercentage
        {
            get => (double)GetValue(UsagePercentageProperty);
            set => SetValue(UsagePercentageProperty, Math.Clamp(value, 0, 100));
        }

        public double RingThickness
        {
            get => (double)GetValue(RingThicknessProperty);
            set => SetValue(RingThicknessProperty, value);
        }

        public Brush BackgroundRingBrush
        {
            get => (Brush)GetValue(BackgroundRingBrushProperty);
            set => SetValue(BackgroundRingBrushProperty, value);
        }

        public Brush ProgressRingBrush
        {
            get => (Brush)GetValue(ProgressRingBrushProperty);
            set => SetValue(ProgressRingBrushProperty, value);
        }

        public Brush TextForeground
        {
            get => (Brush)GetValue(TextForegroundProperty);
            set => SetValue(TextForegroundProperty, value);
        }

        public string Caption
        {
            get => (string)GetValue(CaptionProperty);
            set => SetValue(CaptionProperty, value);
        }

        public Brush CaptionForeground
        {
            get => (Brush)GetValue(CaptionForegroundProperty);
            set => SetValue(CaptionForegroundProperty, value);
        }

        public Brush TextColor
        {
            get => (Brush)GetValue(TextColorProperty);
            set => SetValue(TextColorProperty, value);
        }

        // TextColor属性访问器已在上面定义

        #endregion

        private double _centerX = 100;
        private double _centerY = 100;
        private double _radius = 100;
        private Point _startPoint;

        public CircleUsageIndicator()
        {
            InitializeComponent();
            UpdateControlDimensions();
            UpdateArcEndPoint();
        }

        private void UserControl_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            UpdateControlDimensions();
            UpdateArcEndPoint();
        }

        private void UpdateControlDimensions()
        {
            _centerX = ActualWidth / 2;
            _centerY = ActualHeight / 2;
            _radius = Math.Min(_centerX, _centerY) - RingThickness;

            if (_radius < 0) _radius = 0;
        }

        // 当占用率变化时更新圆弧
        private static void OnUsagePercentageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CircleUsageIndicator indicator)
            {
                indicator.UpdateArcEndPoint();
                indicator.UpdateProgressColor();
            }
        }

        // 更新圆弧终点坐标
        private void UpdateArcEndPoint()
        {
            if (_radius <= 0 || ActualWidth <= 0 || ActualHeight <= 0)
                return;

            // 计算起始角度和结束角度（以度为单位）
            double startAngle = -90; // 从顶部开始
            double endAngle = UsagePercentage / 100 * 360 - 90; // 根据百分比计算结束角度

            // 转换为弧度
            double startRadians = startAngle * Math.PI / 180;
            double endRadians = endAngle * Math.PI / 180;

            // 计算起点和终点坐标
            double startX = _centerX + _radius * Math.Cos(startRadians);
            double startY = _centerY + _radius * Math.Sin(startRadians);
            double endX = _centerX + _radius * Math.Cos(endRadians);
            double endY = _centerY + _radius * Math.Sin(endRadians);

            // 创建路径几何
            PathGeometry pathGeometry = new PathGeometry();
            PathFigure pathFigure = new PathFigure();
            pathFigure.StartPoint = new Point(startX, startY);

            // 创建圆弧段
            ArcSegment arcSegment = new ArcSegment();
            arcSegment.Point = new Point(endX, endY);
            arcSegment.Size = new Size(_radius, _radius);
            arcSegment.IsLargeArc = UsagePercentage > 50;
            arcSegment.SweepDirection = SweepDirection.Clockwise;

            pathFigure.Segments.Add(arcSegment);
            pathGeometry.Figures.Add(pathFigure);

            // 设置路径数据
            ProgressPath.Data = pathGeometry;
        }

        // 当圆环厚度变化时更新圆弧
        private static void OnRingThicknessChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CircleUsageIndicator indicator)
            {
                indicator.UpdateControlDimensions();
                indicator.UpdateArcEndPoint();
            }
        }

        // 根据占用率更新进度条颜色
        private void UpdateProgressColor()
        {
            if (UsagePercentage < 30)
            {
                ProgressRingBrush = new SolidColorBrush(Colors.LimeGreen);
            }
            else if (UsagePercentage < 70)
            {
                ProgressRingBrush = new SolidColorBrush(Colors.Orange);
            }
            else
            {
                ProgressRingBrush = new SolidColorBrush(Colors.Red);
            }
        }
    }
}
