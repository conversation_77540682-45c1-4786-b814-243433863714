﻿using DBEntity;
using N2Purge.userControls;
using N2Purge.ViewModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using PlcTools;

namespace N2Purge.silan
{
    /// <summary>
    /// frmIoMap.xaml 的交互逻辑
    /// </summary>
    public partial class frmIoMap : UserControl
    {
        private bool IsLoad = false;
        private Dictionary<string, ObservableCollection<IOViewModel>> iOViewModels = new Dictionary<string, ObservableCollection<IOViewModel>>();
        private DispatcherTimer timer;
        private string currentCVKey = "CV1"; // 默认显示CV1的数据
        private PlcHelper plcHelper;

        public frmIoMap()
        {
            InitializeComponent();
            if (IsLoad) return;
            
            // 初始化PlcHelper并加载PLC点位配置
            InitializePlcHelper();
            
            // 加载CV配置
            LoadConfigFromJson();
            
            // 初始化OHT数据
            InitializeOHTData();
            
            // 设置定时器更新IO状态
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1); // 每秒更新一次
            timer.Tick += Timer_Tick;
            timer.Start();
            
            // 默认选择第一个OHT
            if (cmbOHT.Items.Count > 0)
            {
                cmbOHT.SelectedIndex = 0;
            }
            
            IsLoad = true;
        }
        
        private async void InitializePlcHelper()
        {
            try
            {
                plcHelper = new PlcHelper();
                
                // 加载PLC配置文件
                string configDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                string plcConfigPath = System.IO.Path.Combine(configDir, "plc_config.json");
                if (File.Exists(plcConfigPath))
                {
                    plcHelper.LoadPlcConfig(plcConfigPath);
                }
                else
                {
                    // 如果PLC配置文件不存在，可以创建一个默认的配置
                    string defaultConfig = @"{
  ""Name"": ""STKPLC"",
  ""IP"": ""*************"",
  ""PORT"": ""9600""
}";
                    File.WriteAllText(plcConfigPath, defaultConfig);
                    plcHelper.LoadPlcConfig(plcConfigPath);
                }
                
                // 连接PLC设备
                PLCResult connectResult = plcHelper.ConnectEquipment();
                if (!connectResult.code)
                {
                    Console.WriteLine($"连接PLC设备失败: {connectResult.msg}");
                }
                
                // 加载PLC点位配置
                PLCResult loadResult = await plcHelper.LoadPLCData();
                if (!loadResult.code)
                {
                    Console.WriteLine($"加载PLC点位配置失败: {loadResult.msg}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化PLC Helper时出错: {ex.Message}");
            }
        }
        
        private void LoadConfigFromJson()
        {
            try
            {
                // 确保Config目录存在
                string configDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                // 从JSON文件加载配置
                string configPath = System.IO.Path.Combine(configDir, "cvio_config.json");
                
                if (File.Exists(configPath))
                {
                    string jsonContent = File.ReadAllText(configPath);
                    var cvioConfig = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(jsonContent);
                    
                    if (cvioConfig != null)
                    {
                        // 清空现有数据
                        iOViewModels.Clear();
                        
                        // 遍历配置中的每个CV
                        foreach (var kvp in cvioConfig)
                        {
                            string cvKey = kvp.Key;
                            var ioList = new ObservableCollection<IOViewModel>();
                            
                            // 遍历每个CV的IO点
                            foreach (var ioItem in kvp.Value)
                            {
                                ioList.Add(new IOViewModel
                                {
                                    CVNmae = ioItem["CVNmae"],
                                    CVIoNmae = ioItem["CVIoNmae"],
                                    CVIoValue = ioItem["CVIoValue"],
                                    PLCTagName = ioItem.ContainsKey("PLCTagName") ? ioItem["PLCTagName"] : string.Empty
                                });
                            }
                            
                            // 将IO点列表添加到字典中
                            iOViewModels[cvKey] = ioList;
                        }
                        
                        // 创建一个包含所有CV数据的列表
                        var allIOList = new ObservableCollection<IOViewModel>();
                        foreach (var kvp in iOViewModels)
                        {
                            foreach (var ioViewModel in kvp.Value)
                            {
                                allIOList.Add(ioViewModel);
                            }
                        }
                        
                        // 显示所有数据
                        dgv.ItemsSource = allIOList;
                    }
                }
                else
                {
                    // 如果配置文件不存在，创建默认配置
                    CreateDefaultCVConfig(configPath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CreateDefaultCVConfig(string configPath)
        {
            try
            {
                // 创建默认的CV配置
                var defaultConfig = new Dictionary<string, List<Dictionary<string, string>>>
                {
                    ["CV1"] = new List<Dictionary<string, string>>
                    {
                        new Dictionary<string, string> { ["CVNmae"] = "CV1", ["CVIoNmae"] = "Ready", ["CVIoValue"] = "0", ["PLCTagName"] = "CV1.Ready" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV1", ["CVIoNmae"] = "Busy", ["CVIoValue"] = "0", ["PLCTagName"] = "CV1.Busy" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV1", ["CVIoNmae"] = "Error", ["CVIoValue"] = "0", ["PLCTagName"] = "CV1.Error" }
                    },
                    ["CV2"] = new List<Dictionary<string, string>>
                    {
                        new Dictionary<string, string> { ["CVNmae"] = "CV2", ["CVIoNmae"] = "Ready", ["CVIoValue"] = "0", ["PLCTagName"] = "CV2.Ready" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV2", ["CVIoNmae"] = "Busy", ["CVIoValue"] = "0", ["PLCTagName"] = "CV2.Busy" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV2", ["CVIoNmae"] = "Error", ["CVIoValue"] = "0", ["PLCTagName"] = "CV2.Error" }
                    },
                    ["CV3"] = new List<Dictionary<string, string>>
                    {
                        new Dictionary<string, string> { ["CVNmae"] = "CV3", ["CVIoNmae"] = "Ready", ["CVIoValue"] = "0", ["PLCTagName"] = "CV3.Ready" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV3", ["CVIoNmae"] = "Busy", ["CVIoValue"] = "0", ["PLCTagName"] = "CV3.Busy" },
                        new Dictionary<string, string> { ["CVNmae"] = "CV3", ["CVIoNmae"] = "Error", ["CVIoValue"] = "0", ["PLCTagName"] = "CV3.Error" }
                    }
                };
                
                // 将默认配置保存到文件
                string jsonContent = JsonConvert.SerializeObject(defaultConfig, Formatting.Indented);
                File.WriteAllText(configPath, jsonContent);
                
                // 加载默认配置到内存
                iOViewModels.Clear();
                
                foreach (var kvp in defaultConfig)
                {
                    string cvKey = kvp.Key;
                    var ioList = new ObservableCollection<IOViewModel>();
                    
                    foreach (var ioItem in kvp.Value)
                    {
                        ioList.Add(new IOViewModel
                        {
                            CVNmae = ioItem["CVNmae"],
                            CVIoNmae = ioItem["CVIoNmae"],
                            CVIoValue = ioItem["CVIoValue"],
                            PLCTagName = ioItem.ContainsKey("PLCTagName") ? ioItem["PLCTagName"] : string.Empty
                        });
                    }
                    
                    iOViewModels[cvKey] = ioList;
                }
                
                // 创建一个包含所有CV数据的列表
                var allIOList = new ObservableCollection<IOViewModel>();
                foreach (var kvp in iOViewModels)
                {
                    foreach (var ioViewModel in kvp.Value)
                    {
                        allIOList.Add(ioViewModel);
                    }
                }
                
                // 显示所有数据
                dgv.ItemsSource = allIOList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建默认配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void InitializeOHTData()
        {
            try
            {
                // 确保GlobalData.gbPortCmdstatusVm已初始化
                if (GlobalData.gbPortCmdstatusVm == null)
                {
                    GlobalData.gbPortCmdstatusVm = new Dictionary<string, Dictionary<string, IOStatusViewModel>>();
                }
                
                // 为OHT1-OHT4创建数据源
                string[] ohtNames = { "OHT1", "OHT2", "OHT3", "OHT4" };
                string[] statusNames = { "Ready", "Alarm", "Fault", "SafetyOk", "ServoEnable", "Standstill", "CSTStorageLP", 
                                        "CSTPresenceLP", "CSTStorageBoard", "CSTPresenceBoard", "CSTStorageOP", "CSTPresenceOP", 
                                        "BordAtLP", "BordAtOP", "TypeChangePermit", "Warning", "PortDisable", "RFIDBypass", 
                                        "LC1Mutting", "BuzzerSilence", "BoardAtSafePosition", "AlarmReset", "SafetyReset", 
                                        "ServoOn", "HandoffCompleteAck", "PortDisable", "RFIDBaypass", "LC1Mutting" };
                
                foreach (string ohtName in ohtNames)
                {
                    if (!GlobalData.gbPortCmdstatusVm.ContainsKey(ohtName))
                    {
                        var statusDict = new Dictionary<string, IOStatusViewModel>();
                        
                        foreach (string statusName in statusNames)
                        {
                            statusDict[statusName] = new IOStatusViewModel
                            {
                                IoName = statusName,
                                IoAddress = $"{ohtName}.{statusName}",
                                Status = 0, // 默认为灰色状态
                                DisplayText = statusName
                            };
                        }
                        
                        GlobalData.gbPortCmdstatusVm[ohtName] = statusDict;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化OHT数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void Timer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 更新CV数据源
                UpdateCVData();
                
                // 更新OHT数据源
                UpdateOHTData();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"定时器更新数据时出错: {ex.Message}");
            }
        }
        
        private void UpdateCVData()
        {
            // 更新所有CV数据源
            foreach (var kvp in iOViewModels)
            {
                var items = kvp.Value;
                if (items != null)
                {
                    foreach (var item in items)
                    {
                        // 如果有配置PLC标签名称，则从PLC读取数据
                        if (!string.IsNullOrEmpty(item.PLCTagName))
                        {
                            try
                            {
                                // 从PLC读取数据
                                PLCResult result = plcHelper.PLCRead(item.PLCTagName);
                                
                                if (result.code) // 读取成功
                                {
                                    // 将PLC读取的值转换为字符串并更新到IOViewModel
                                    string newValue = result.data?.ToString() ?? "0";
                                    
                                    // 如果是布尔值，转换为0或1
                                    if (result.data is bool boolValue)
                                    {
                                        newValue = boolValue ? "1" : "0";
                                    }
                                    
                                    // 如果值发生变化，则更新
                                    if (item.CVIoValue != newValue)
                                    {
                                        item.CVIoValue = newValue;
                                    }
                                }
                                else
                                {
                                    // 读取失败，记录错误信息
                                    Console.WriteLine($"读取PLC点位失败: {item.PLCTagName}, 错误: {result.msg}");
                                }
                            }
                            catch (Exception ex)
                            {
                                // 捕获并记录异常
                                Console.WriteLine($"读取PLC点位异常: {item.PLCTagName}, 异常: {ex.Message}");
                            }
                        }
                    }
                }
            }
        }
        
        private void UpdateOHTData()
        {
            // 更新所有OHT数据源
            foreach (var ohtKvp in GlobalData.gbPortCmdstatusVm)
            {
                string ohtName = ohtKvp.Key;
                var statusDict = ohtKvp.Value;
                
                if (statusDict != null)
                {
                    foreach (var statusKvp in statusDict)
                    {
                        string statusName = statusKvp.Key;
                        IOStatusViewModel statusViewModel = statusKvp.Value;
                        
                        // 模拟从PLC读取数据（实际应用中应替换为真实的PLC读取逻辑）
                        try
                        {
                            string plcTagName = $"{ohtName}.{statusName}";
                            
                            // 如果plcHelper已初始化，尝试从PLC读取数据
                            if (plcHelper != null)
                            {
                                PLCResult result = plcHelper.PLCRead(plcTagName);
                                
                                if (result.code) // 读取成功
                                {
                                    // 将PLC读取的值转换为整数并更新到IOStatusViewModel
                                    int newStatus = 0;
                                    
                                    if (result.data is bool boolValue)
                                    {
                                        newStatus = boolValue ? 1 : 0; // 1表示绿色（成功状态）
                                    }
                                    else if (result.data != null)
                                    {
                                        if (int.TryParse(result.data.ToString(), out int intValue))
                                        {
                                            newStatus = intValue > 0 ? 1 : 0;
                                        }
                                    }
                                    
                                    // 更新状态
                                    statusViewModel.Status = newStatus;
                                }
                            }
                            else
                            {
                                // 如果plcHelper未初始化，使用随机状态进行测试
                                Random random = new Random();
                                statusViewModel.Status = random.Next(0, 2); // 随机生成0或1
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"更新OHT状态时出错: {ohtName}.{statusName}, 异常: {ex.Message}");
                        }
                    }
                }
            }
        }
    

        private void cmbOHT_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!string.IsNullOrEmpty(cmbOHT.Text))
            {
                string txt = "";
                if (cmbOHT.SelectedIndex == 0) { txt = "OHT1"; }
                if (cmbOHT.SelectedIndex == 1) { txt = "OHT2"; }
                if (cmbOHT.SelectedIndex == 2) { txt = "OHT3"; }
                if (cmbOHT.SelectedIndex == 3) { txt = "OHT4"; }
                
                // 检查GlobalData.gbPortCmdstatusVm中是否包含所选的OHT
                if (GlobalData.gbPortCmdstatusVm.ContainsKey(txt))
                {
                    // 获取所选OHT的状态字典
                    var statusDict = GlobalData.gbPortCmdstatusVm[txt];
                    
                    // 遍历状态字典中的每个项目
                    foreach (var item in statusDict)
                    {
                        CmdStatus cmd = null;
                        string name = "";
                        
                        // 首先尝试在OHTbd1中查找控件
                        name = "OHT" + item.Value.DisplayText;
                        cmd = OHTbd1.FindName(name) as CmdStatus;
                        
                     
                        // 如果找到了控件，则设置其DataContext
                        if (cmd != null)
                        {
                            cmd.DataContext = item.Value;
                        }
                        else
                        {
                            Console.WriteLine($"未找到控件: {name}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"GlobalData.gbPortCmdstatusVm中不包含键: {txt}");
                    // 如果数据源中没有对应的OHT数据，可以初始化一个空的数据集
                    GlobalData.gbPortCmdstatusVm[txt] = new Dictionary<string, IOStatusViewModel>();
                }
            }
        }
    }
}
