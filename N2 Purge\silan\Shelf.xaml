﻿<UserControl x:Class="N2Purge.silan.Shelf"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="60" MouseDoubleClick="UserControl_MouseDoubleClick"  MouseLeftButtonDown="UserControl_MouseLeftButtonDown">
    <UserControl.ContextMenu>
        <local:ShelfContextMenu DataContext="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource Self}}"/>
    </UserControl.ContextMenu>
    <Border BorderBrush="{Binding BorderBrush}" BorderThickness="{Binding BorderThickness}" >
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="0"/>
            </Grid.RowDefinitions>

            <!-- 点位ID显示 -->
            <TextBlock Grid.Row="0"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center"
                       Text="{Binding ShelfLocation}"
                       FontSize="8"
                       FontWeight="Bold"
                       Foreground="Black"
                       Background="White"
                       Margin="1"/>

            <!-- 状态显示区域 -->
            <Grid Grid.Row="1">
                <Rectangle Fill="{Binding ShelfColorBrush}"
                           Stroke="Black"
                           StrokeThickness="1"/>

                <!-- 货物ID显示 -->
                <TextBlock VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Text="{Binding FoupDisplayText}"
                           FontSize="9"
                           FontWeight="Bold"
                           Foreground="Red"
                           Background="Transparent"
                           TextWrapping="Wrap"
                           TextAlignment="Center"
                           Margin="2">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <!-- 只有在有货物ID时才显示 -->
                                <DataTrigger Binding="{Binding FoupDisplayText}" Value="">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding FoupDisplayText}" Value="{x:Null}">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </Grid>

            <!-- 状态文本显示 -->
            <TextBlock Grid.Row="2"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center"
                       Text="{Binding StatusText}"
                       FontSize="6"
                       FontWeight="Bold"
                       Foreground="Black"
                       Background="White"
                       Margin="1"/>

            <!-- 入口出口标记 -->
            <Border Grid.Row="0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="{Binding EntryExitBrush}"
                    CornerRadius="2"
                    Margin="1"
                    Padding="2,0">
                <Border.Style>
                    <Style TargetType="Border">
                        <Style.Triggers>
                            <!-- 只有在有入口出口标记时才显示 -->
                            <DataTrigger Binding="{Binding HasEntryExitMark}" Value="False">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>
                <TextBlock Text="{Binding EntryExitText}"
                           FontSize="6"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>

            <!-- 选择指示器 -->
            <Border Grid.Row="0" Grid.RowSpan="3"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Background="Blue"
                    CornerRadius="1"
                    Width="4"
                    Height="4"
                    Margin="2">
                <Border.Style>
                    <Style TargetType="Border">
                        <Style.Triggers>
                            <!-- 只有在选中时才显示 -->
                            <DataTrigger Binding="{Binding IsSelected}" Value="False">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>
            </Border>
            <!-- 左上角IO信号指示灯 -->
            <!--<Ellipse Width="8" Height="8" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="2,2,0,0">
                <Ellipse.Style>
                    <Style TargetType="Ellipse">
                        <Setter Property="Fill" Value="Gray"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IoSignal1}" Value="True">
                                <Setter Property="Fill" Value="Red"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Ellipse.Style>
            </Ellipse>

            --><!-- 右上角IO信号指示灯 --><!--
            <Ellipse Width="8" Height="8" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,2,2,0">
                <Ellipse.Style>
                    <Style TargetType="Ellipse">
                        <Setter Property="Fill" Value="Gray"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IoSignal2}" Value="True">
                                <Setter Property="Fill" Value="Green"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Ellipse.Style>
            </Ellipse>-->
        </Grid>
    </Border>
</UserControl>
