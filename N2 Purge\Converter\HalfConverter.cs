using System;
using System.Globalization;
using System.Windows.Data;

namespace N2Purge.Converter
{
    /// <summary>
    /// 将值除以2的转换器
    /// </summary>
    public class HalfConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return doubleValue / 2;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return doubleValue * 2;
            }
            return 0;
        }
    }
}