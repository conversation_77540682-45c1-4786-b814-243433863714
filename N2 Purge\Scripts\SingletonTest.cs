using System;
using System.Threading;
using System.Diagnostics;

namespace SingletonTest
{
    class Program
    {
        private static Mutex? _mutex;
        private static readonly string _mutexName = "Global\\N2Purge_SingleInstance_2024";

        static void Main(string[] args)
        {
            Console.WriteLine("========================================");
            Console.WriteLine("    N2 Purge 单例测试程序");
            Console.WriteLine("========================================");
            Console.WriteLine();

            Console.WriteLine($"进程ID: {Process.GetCurrentProcess().Id}");
            Console.WriteLine($"Mutex名称: {_mutexName}");
            Console.WriteLine();

            bool isMainInstance = CheckSingleInstance();
            
            Console.WriteLine($"单例检查结果: {(isMainInstance ? "主实例" : "非主实例")}");
            Console.WriteLine();

            if (isMainInstance)
            {
                Console.WriteLine("✅ 当前为主实例，程序将保持运行");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                
                // 释放Mutex
                ReleaseMutex();
            }
            else
            {
                Console.WriteLine("❌ 检测到其他实例正在运行，程序将退出");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        static bool CheckSingleInstance()
        {
            try
            {
                Console.WriteLine("[检查] 开始创建Mutex...");
                
                // 尝试创建命名Mutex
                bool createdNew;
                _mutex = new Mutex(true, _mutexName, out createdNew);
                
                Console.WriteLine($"[检查] Mutex创建结果: createdNew={createdNew}");

                if (createdNew)
                {
                    Console.WriteLine("[检查] ✅ 成功创建新Mutex，当前为主实例");
                    return true;
                }
                else
                {
                    Console.WriteLine("[检查] ⚠️ Mutex已存在，尝试获取所有权...");
                    
                    // 尝试获取Mutex的所有权（等待3秒）
                    bool acquired = _mutex.WaitOne(3000, false);
                    Console.WriteLine($"[检查] Mutex获取结果: {acquired}");

                    if (acquired)
                    {
                        Console.WriteLine("[检查] ✅ 成功获取Mutex所有权，当前为主实例");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine("[检查] ❌ 无法获取Mutex所有权，其他实例正在运行");
                        
                        // 释放Mutex资源
                        _mutex?.Close();
                        _mutex?.Dispose();
                        _mutex = null;
                        
                        return false;
                    }
                }
            }
            catch (AbandonedMutexException ex)
            {
                Console.WriteLine($"[检查] ⚠️ 检测到遗弃的Mutex: {ex.Message}");
                Console.WriteLine("[检查] 前一个实例可能异常退出，当前实例成为主实例");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[检查] ❌ 检查单例时发生异常: {ex.Message}");
                Console.WriteLine("[检查] 异常情况下默认允许运行");
                return true;
            }
        }

        static void ReleaseMutex()
        {
            try
            {
                if (_mutex != null)
                {
                    Console.WriteLine("[清理] 释放Mutex资源...");
                    _mutex.ReleaseMutex();
                    _mutex.Close();
                    _mutex.Dispose();
                    _mutex = null;
                    Console.WriteLine("[清理] Mutex资源已释放");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[清理] 释放Mutex时发生异常: {ex.Message}");
            }
        }
    }
}
