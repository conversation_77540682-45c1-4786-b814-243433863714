﻿<UserControl x:Class="N2Purge.frmUserControl.frmSetting"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             xmlns:converter="clr-namespace:N2Purge.Converter"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converter:InverseBoolConverter x:Key="InverseBoolConverter"/>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <StackPanel Margin="10" Grid.Row="0">
            <!-- Online/Offline 设置 -->
            <GroupBox Header="Online/Offline" Margin="0,5" Background="AliceBlue">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <RadioButton GroupName="OnlineOffline" Content="Offline" x:Name="rboff"  IsChecked="{Binding IsOnline, Converter={StaticResource InverseBoolConverter}}" Margin="0,0,20,0" Checked="RadioButton_Checked"/>
                        <RadioButton GroupName="OnlineOffline" Content="Online" x:Name="rbon"  IsChecked="{Binding IsOnline}"/>
                        <Button x:Name="btnconfirm" Content="确认" Margin="50,0,0,0"  Width="60" Background="Snow" Click="btnconfirm_Click"/>
                    </StackPanel>
                   
                </Grid>
            </GroupBox>

            <!-- Local/Remote 设置 -->
            <GroupBox Header="Local/Remote" Margin="0,5" Background="AliceBlue">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <!-- Local/Remote 设置 -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <RadioButton GroupName="LocalRemote" Content="OnlineLocal" x:Name="rbloc" IsChecked="True" Margin="0,0,20,0"/>
                        <RadioButton GroupName="LocalRemote" Content="OnlineRemote" x:Name="rbremote"/>
                        <Button x:Name="btnconfirm1" Click="btnconfirm1_Click" Content="确认" Margin="50,0,0,0"  Width="60" Background="Snow"/>
                    </StackPanel>
                    
                   
                </Grid>
            </GroupBox>
            <!-- SC Run/Pause 设置 -->
            <GroupBox Header="SC Run/Pause" Margin="0,5" Background="AliceBlue">
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <RadioButton GroupName="RunPause" Content="Auto" IsChecked="True" Margin="0,0,20,0" x:Name="IsAuto"/>
                    <RadioButton GroupName="RunPause" Content="Paused" x:Name="IsPause"/>
                    <Button x:Name="btnconfirm2" Click="btnconfirm2_Click" Content="确认" Margin="50,0,0,0"  Width="60" Background="Snow"/>
                </StackPanel>
            </GroupBox>
            <!-- SC Mode 设置 -->
            <GroupBox Header="Sc Mode" Margin="0,5" Background="AliceBlue">
                <Grid>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <RadioButton GroupName="Mode" Content="Normal" IsChecked="True" x:Name="IsNormal" Margin="0,0,20,0"/>
                            <RadioButton GroupName="Mode" Content="Maintenance" x:Name="IsMaint" Margin="0,0,20,0"/>
                        </StackPanel>
                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5">
                            <RadioButton GroupName="Mode" Content="Test" x:Name="IsTest" Margin="0,0,20,0"/>
                            <RadioButton GroupName="Mode" Content="Simulation" x:Name="IsSimulation"/>
                            <Button x:Name="btnconfirm3" Click="btnconfirm3_Click" Content="确认" Margin="50,0,0,0"  Width="60" Background="Snow"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </GroupBox>
          
        </StackPanel>
        <GroupBox Header="Cycle Test" Margin="0,5" Background="AliceBlue" Grid.Row="1">
            <local:FrmLoopRun/>
        </GroupBox>
    </Grid>
</UserControl>
