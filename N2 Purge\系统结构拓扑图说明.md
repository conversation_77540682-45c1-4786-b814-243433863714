# N2 Purge 系统结构拓扑图说明

## 📋 **概述**

本文档详细说明了N2 Purge智能传送带控制系统的完整架构拓扑，包括系统分层结构、模块依赖关系、数据流向以及各组件之间的交互关系。

## 🏗️ **系统分层架构**

### **1. 外部系统层 (External Systems)**
```
🔴 PLC设备 - 工业控制器，负责硬件设备控制
🔴 MySQL数据库 - 数据持久化存储，stkc数据库
🔴 文件系统 - 配置文件和日志文件存储
```

### **2. 基础设施层 (Infrastructure Layer)**
```
🟢 PlcTools - PLC通信工具，设备连接和数据交换
🟢 DBEntity - 数据实体层，数据库表映射和ORM
🟢 Proj.Log - 日志系统，操作记录和异常跟踪
🟢 CSVTools - CSV处理工具，数据导出功能
🟢 WCFClient - WCF通信客户端，服务间通信
🟢 EmulatorClient - 模拟器客户端，设备仿真
🟢 Proj.RecordReplay - 录制回放引擎，自动化操作
```

### **3. 数据访问层 (Data Access Layer)**
```
🔵 DbHelper - 数据库操作助手，CRUD封装
🔵 PlcHelper - PLC通信助手，设备数据读写
🔵 CsvHelper - CSV导出助手，数据格式化
🔵 GlobalData - 全局数据管理，应用状态维护
🔵 UserManagement - 用户管理，权限控制
```

### **4. 业务逻辑层 (Business Logic Layer)**
```
🟡 传送带控制 - MainLoop/Process，核心业务逻辑
🟡 传送逻辑 - 路径管理，货物流转控制
🟡 报警系统 - 异常检测与处理
🟡 录制回放服务 - 操作自动化
🟡 数据同步服务 - 状态管理和数据一致性
```

### **5. 视图模型层 (ViewModel Layer)**
```
🟣 ShelfViewModel - 货架视图模型，MVVM数据绑定
🟣 TransferViewModel - 传送视图模型，传送操作界面
🟣 IOViewModel - IO视图模型，设备状态监控
🟣 CraneViewModel - 起重机视图模型，设备控制
🟣 StatisticsViewModel - 统计视图模型，数据分析
```

### **6. 表示层 (Presentation Layer)**
```
🔵 MainWindow - 主窗口，应用程序入口
🔵 传送带界面 - frmConvryorMain, frmConveyor, frmCVIO
🔵 管理界面 - 用户管理、报警管理、历史记录、设置
🔵 用户控件 - Shelf, Port, Crane, ShelfContextMenu
```

## 🔄 **核心数据流**

### **1. PLC数据流**
```
PLC设备 → PLC读取 → MainLoop → Process函数 → 状态机 → PLC写入 → PLC设备
```
- **实时监控**: 150ms间隔读取PLC数据
- **状态检测**: 货物到达/离开检测
- **控制指令**: 设备状态控制和参数设置

### **2. 数据库数据流**
```
实体层 → 数据库读取 → MySQL数据库 → 数据库写入 → 实体层
```
- **数据持久化**: 货物信息、传送记录、用户数据
- **配置管理**: 点位配置、系统参数
- **历史记录**: 操作日志、报警记录

### **3. UI数据流**
```
用户输入 → UI事件处理 → ViewModel更新 → UI刷新 → 用户输入
```
- **事件驱动**: 鼠标点击、键盘输入、右键菜单
- **数据绑定**: MVVM模式，自动UI更新
- **实时反馈**: 状态变化即时显示

### **4. 业务逻辑数据流**
```
Process → 状态机 → 传送路径 → 时间跟踪 → 数据库写入
```
- **状态管理**: Entry → OnTrans → Exit状态转换
- **路径跟踪**: 完整的货物传送路径记录
- **时间记录**: 精确的到达/离开时间

### **5. 录制回放数据流**
```
用户输入 → 全局钩子 → 录制服务 → JSON文件 → 回放服务 → UI事件
```
- **操作捕获**: 系统级事件监听
- **序列存储**: JSON格式操作记录
- **自动回放**: 精确的操作重现

## 🔗 **关键交互关系**

### **1. 出入口状态同步**
```
UI事件 --[ChangePortState]--> PLC写入 --[状态同步]--> 数据库写入 --[数据源刷新]--> ViewModel更新
```

### **2. 货物状态更新**
```
PLC读取 → Process函数 → 状态机 → ViewModel更新 → UI刷新
```

### **3. 用户权限控制**
```
用户登录 → UserManagement → 权限验证 → 功能访问控制
```

## 📊 **模块依赖关系**

### **核心依赖模块**
- **DBEntity**: 数据库映射和实体定义
- **PlcTools**: PLC设备通信核心
- **Proj.Log**: 系统日志记录
- **Proj.RecordReplay**: 自动化操作引擎

### **辅助工具模块**
- **CSVTools**: 数据导出功能
- **WCFClient**: 服务间通信
- **EmulatorClient**: 设备仿真测试

### **第三方依赖**
- **Xceed.Wpf.Toolkit**: WPF扩展控件
- **HslCommunication**: 工业通信库
- **MySQL.Data**: 数据库连接器
- **.NET 8.0**: 运行时框架

## 🎯 **架构优势**

### **1. 分层清晰**
- 职责分离，模块化设计
- 易于维护和扩展
- 降低模块间耦合度

### **2. 数据流向明确**
- 单向数据流，避免循环依赖
- 状态管理集中化
- 数据一致性保证

### **3. 可扩展性强**
- 插件化架构设计
- 接口驱动开发
- 支持功能模块独立部署

### **4. 可测试性好**
- 依赖注入支持
- 模块独立测试
- 自动化测试集成

## 🔧 **技术特色**

### **1. 创新的录制回放功能**
- 全局系统钩子技术
- JSON格式操作序列存储
- 精确的时间控制回放

### **2. 实时性能优化**
- 状态变化检查机制
- 精确的属性通知
- 异步UI更新策略

### **3. 完整的数据同步**
- 五层数据一致性保证
- 实时状态同步机制
- 自动数据源刷新

### **4. 强大的监控能力**
- 毫秒级时间跟踪
- 完整的传送路径记录
- 详细的性能分析数据

## 📈 **系统价值**

### **技术价值**
- 现代化架构设计
- 高性能实时处理
- 完善的错误处理机制
- 优秀的用户体验

### **业务价值**
- 提高生产效率
- 降低运营成本
- 增强系统可靠性
- 支持智能化决策

这个系统架构为工业4.0时代的智能制造提供了强有力的技术支撑，具有良好的可扩展性和可维护性。
