﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 定义展开/折叠按钮的样式 -->
    
    <Style x:Key="ExpandCollapseToggleStyle" TargetType="ToggleButton">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border Background="Transparent"  x:Name="ToggleButtonBorder"
                            Width="20"
                            Height="20"
                            Padding="5">
                        <Path x:Name="ExpandPath"
                              Fill="#666666"
                              Data="M0,0 L8,0 L4,4 Z"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              RenderTransformOrigin="0.5,0.5">
                            <Path.RenderTransform>
                                <RotateTransform Angle="0"/>
                            </Path.RenderTransform>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 展开状态 -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ExpandPath" Property="Data" Value="M0,0 L8,0 L4,4 Z"/>
                        </Trigger>
                        <!-- 折叠状态 -->
                        <Trigger Property="IsChecked" Value="False">
                            <Setter TargetName="ExpandPath" Property="Data" Value="M0,0 L0,8 L4,4 Z"/>
                        </Trigger>
                        <!-- 鼠标悬停效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ExpandPath" Property="Fill" Value="#333333"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding Items.Count, RelativeSource={RelativeSource AncestorType=TreeViewItem}}"
                                     Value="0">
                            <Setter TargetName="ToggleButtonBorder" Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 定义TreeViewItem样式 -->
    <Style TargetType="TreeViewItem">
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="10,5,0,5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TreeViewItem">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 展开/折叠按钮 -->
                        <ToggleButton x:Name="Expander"
                                      Grid.Column="0"
                                      Style="{StaticResource ExpandCollapseToggleStyle}"
                                      IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                      ClickMode="Press"
                                      Width="20"
                                      Height="20"
                                      Margin="5,0"/>

                        <!-- 节点内容 -->
                        <Border x:Name="Bd"
                                Grid.Column="1"
                                Background="Transparent"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter ContentSource="Header"
                                              VerticalAlignment="Center"/>
                        </Border>

                        <!-- 子节点容器 -->
                        <ItemsPresenter x:Name="ItemsHost"
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Margin="15,0,0,0"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <!-- 选中状态 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Bd" Property="Background" Value="#E0E0E0"/>
                        </Trigger>
                        <Trigger Property="IsExpanded" Value="True">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsExpanded" Value="False">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>