﻿using N2Purge.Services;
using Proj.Log;
using System.Configuration;
using System.Data;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Windows;
using System.Windows.Threading;
using System.Diagnostics;
using System.IO;

namespace N2Purge
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private static readonly ManualResetEvent _resetEvent = new ManualResetEvent(false);
        private static UdpClient? _udpListener;
        private static readonly string _appIdentifier = "N2Purge_v1.0";
        private static readonly int _detectionPort = 50001;  // 统一使用一个端口进行检测
        private static bool _isMainInstance = false;
        private static bool _udpListenerReady = false;  // UDP监听器就绪标志
        private SingletonManager? _singletonManager;

        /// <summary>
        /// 应用程序启动事件 - 最早的检查点
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            // 在任何UI创建之前进行单例检查
            PerformSingletonCheck();

            // 如果通过单例检查，继续正常启动
            base.OnStartup(e);
        }

        public App()
        {
            // 构造函数中只做最基本的初始化
            GlobalExceptionHandler.Initialize();
        }

        /// <summary>
        /// 执行单例检查
        /// </summary>
        private void PerformSingletonCheck()
        {
            // 初始化日志
            try
            {
                Logger.Instance.OperationLog("N2Purge应用程序启动，开始单例检查");
            }
            catch
            {
                // 如果日志初始化失败，继续执行
            }

            // 使用简单单例管理器进行检查
            bool isMainInstance = SimpleSingletonManager.CheckSingleInstance();

            if (!isMainInstance)
            {
                Logger.Instance?.OperationLog("检测到其他N2Purge实例正在运行，当前实例退出");

                // 显示提示信息
                try
                {
                    MessageBox.Show("检测到N2Purge已在运行中！\n\n当前实例将自动退出。",
                                  "程序已运行",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Information);
                }
                catch
                {
                    // 如果MessageBox显示失败，直接退出
                }

                // 立即退出应用程序
                this.Shutdown(0);
                return;
            }

            Logger.Instance?.OperationLog("单例检查通过，当前实例为主实例");

            // 检查并设置自动计划任务
            CheckAndSetupAutoTask();

            // 网络实例检测（作为额外保险）
            CheckAndSetMainInstance();

#if DEBUG
            // Debug模式下等待成为主实例（支持备用模式）
            Logger.Instance?.OperationLog("Debug模式：等待成为主实例（支持备用模式）");
            _resetEvent.WaitOne(); // 无限等待直到成为主实例
#else
            // Release模式下等待成为主实例（支持备用模式）
            Logger.Instance?.OperationLog("等待成为主实例（支持备用模式）");
            _resetEvent.WaitOne(); // 无限等待直到成为主实例
#endif

            Logger.Instance?.OperationLog("所有检查通过，应用程序正常启动");
        }

        #region 自动计划任务管理
        /// <summary>
        /// 检查并设置自动计划任务
        /// </summary>
        private static void CheckAndSetupAutoTask()
        {
            try
            {
                Logger.Instance?.OperationLog("[启动检查] 开始检查自动计划任务");

                // 改为同步执行，确保能看到完整的日志输出
                try
                {
                    Logger.Instance?.OperationLog("[启动检查] 调用AutoTaskManager.CheckAndSetupAutoTask()");
                    bool result = AutoTaskManager.CheckAndSetupAutoTask();
                    Logger.Instance?.OperationLog($"[启动检查] AutoTaskManager.CheckAndSetupAutoTask() 返回结果: {result}");

                    if (result)
                    {
                        Logger.Instance?.OperationLog("[启动检查] ✅ 自动计划任务检查完成 - 任务已存在或创建成功");
                    }
                    else
                    {
                        Logger.Instance?.OperationLog("[启动检查] ❌ 自动计划任务设置失败");
                        Logger.Instance?.OperationLog("[启动检查] 程序将正常运行，用户可通过AutoTask界面手动创建计划任务");

                        // 检查是否是权限问题
                        bool isAdmin = AutoTaskManager.IsAdministrator();
                        Logger.Instance?.OperationLog($"[启动检查] 当前管理员权限状态: {isAdmin}");

                        if (!isAdmin)
                        {
                            Logger.Instance?.OperationLog("[启动检查] 💡 提示：以管理员身份运行程序可自动创建计划任务");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance?.ExceptionLog($"[启动检查] 自动计划任务处理异常: {ex.Message}");
                    Logger.Instance?.ExceptionLog($"[启动检查] 异常堆栈: {ex.StackTrace}");
                    Logger.Instance?.OperationLog("[启动检查] 程序将正常运行，不影响主要功能");
                }

                Logger.Instance?.OperationLog("[启动检查] 自动计划任务检查流程完成");
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"[启动检查] 启动自动计划任务检查失败: {ex.Message}");
                Logger.Instance?.ExceptionLog($"[启动检查] 外层异常堆栈: {ex.StackTrace}");
            }
        }
        #endregion

        #region 网络实例检测
        /// <summary>
        /// 检查并设置主实例
        /// </summary>
        private static void CheckAndSetMainInstance()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    // 启动UDP监听器
                    StartUdpListener();

                    // 等待UDP监听器完全启动
                    Logger.Instance?.OperationLog("⏳ 等待UDP监听器完全启动...");
                    int waitCount = 0;
                    while (!_udpListenerReady && waitCount < 50) // 最多等待5秒
                    {
                        Thread.Sleep(100);
                        waitCount++;
                    }

                    if (_udpListenerReady)
                    {
                        Logger.Instance?.OperationLog("✅ UDP监听器已就绪，开始网络检测");
                    }
                    else
                    {
                        Logger.Instance?.ExceptionLog("⚠️ UDP监听器启动超时，继续进行网络检测");
                    }

                    // 发送广播检测其他实例
                    bool hasOtherInstance = BroadcastDetection();

                    _isMainInstance = !hasOtherInstance;

                    Logger.Instance?.OperationLog($"网络检测完成，是否为主实例: {_isMainInstance}");

                    if (hasOtherInstance)
                    {
                        Logger.Instance?.OperationLog("🔄 检测到网络中已有主实例，当前程序进入备用模式，持续监控...");
                        Logger.Instance?.OperationLog("💡 提示：程序将在后台等待，当主实例退出时自动接管");

                        // 进入备用模式，持续监控主实例状态
                        StartStandbyMode();
                    }
                    else
                    {
                        Logger.Instance?.OperationLog("✅ 当前程序成为主实例");
                        _resetEvent.Set();
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance?.ExceptionLog($"网络检测异常: {ex.Message}");
                    // 异常情况下默认为主实例
                    _isMainInstance = true;
                    _resetEvent.Set();
                }
            });
        }

        /// <summary>
        /// 启动UDP监听器
        /// </summary>
        private static void StartUdpListener()
        {
            try
            {
                _udpListener = new UdpClient(_detectionPort);
                Logger.Instance?.OperationLog($"🎧 UDP监听器启动成功，监听端口: {_detectionPort}");

                Task.Factory.StartNew(() =>
                {
                    var allLocalIPs = GetAllLocalIPAddresses();
                    var primaryLocalIP = GetLocalIPAddress();
                    Logger.Instance?.OperationLog($"🎧 UDP监听器开始监听，本机所有IP: {string.Join(", ", allLocalIPs)}");

                    // 标记监听器已就绪
                    _udpListenerReady = true;
                    Logger.Instance?.OperationLog("🎧 UDP监听器已就绪，可以接收消息");

                    while (_udpListener != null)
                    {
                        try
                        {
                            var remoteEP = new IPEndPoint(IPAddress.Any, 0);
                            var data = _udpListener.Receive(ref remoteEP);
                            var message = Encoding.UTF8.GetString(data);

                            Logger.Instance?.OperationLog($"🎧 收到UDP消息: '{message}' 来自 {remoteEP.Address}:{remoteEP.Port}");

                            // 如果收到的是检测消息，回复确认
                            if (message.StartsWith(_appIdentifier))
                            {
                                // 确定用于回复的本机IP（选择与发送方在同一网段的IP）
                                string replyIP = GetBestReplyIP(remoteEP.Address, allLocalIPs);
                                var responseMessage = Encoding.UTF8.GetBytes($"{_appIdentifier}:{replyIP}");

                                // 回复到发送方的接收端口
                                var replyEndPoint = new IPEndPoint(remoteEP.Address, _detectionPort + 1);
                                _udpListener.Send(responseMessage, responseMessage.Length, replyEndPoint);
                                Logger.Instance?.OperationLog($"📤 回复实例检测请求: {remoteEP.Address} -> 端口 {_detectionPort + 1}");
                                Logger.Instance?.OperationLog($"📤 回复内容: {Encoding.UTF8.GetString(responseMessage)} (使用IP: {replyIP})");
                            }
                            else
                            {
                                Logger.Instance?.OperationLog($"🎧 忽略非检测消息: {message}");
                            }
                        }
                        catch (ObjectDisposedException)
                        {
                            // UDP客户端已释放，退出循环
                            Logger.Instance?.OperationLog("🎧 UDP监听器已释放，退出监听循环");
                            break;
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance?.ExceptionLog($"🎧 UDP监听异常: {ex.Message}");
                        }
                    }

                    Logger.Instance?.OperationLog("🎧 UDP监听器线程结束");
                }, TaskCreationOptions.LongRunning);
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"启动UDP监听器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 广播检测其他实例 - 支持多网卡
        /// </summary>
        private static bool BroadcastDetection()
        {
            UdpClient? receiveClient = null;
            var broadcastClients = new List<UdpClient>();

            try
            {
                // 设置接收客户端 - 使用不同的端口避免冲突
                receiveClient = new UdpClient(_detectionPort + 1);  // 50002端口用于接收回复
                receiveClient.Client.ReceiveTimeout = 3000; // 3秒超时

                var localIPs = GetAllLocalIPAddresses();
                var primaryLocalIP = GetLocalIPAddress();

                if (localIPs.Count == 0)
                {
                    Logger.Instance?.ExceptionLog("未找到可用的网络接口");
                    return false;
                }

                // 开始多网卡广播检测
                Logger.Instance?.OperationLog("🔍 开始多网卡广播检测...");

                // 为每个网卡创建广播客户端并发送广播
                foreach (var localIP in localIPs)
                {
                    try
                    {
                        var broadcastClient = new UdpClient();
                        broadcastClient.EnableBroadcast = true;
                        broadcastClients.Add(broadcastClient);

                        // 绑定到特定的本地IP
                        broadcastClient.Client.Bind(new IPEndPoint(localIP, 0));

                        // 计算该网段的广播地址
                        var broadcastAddress = GetNetworkBroadcastAddress(localIP);
                        var broadcastEndPoint = new IPEndPoint(broadcastAddress, _detectionPort);  // 发送到检测端口

                        var message = Encoding.UTF8.GetBytes($"{_appIdentifier}:{localIP}");

                        // 发送广播
                        broadcastClient.Send(message, message.Length, broadcastEndPoint);
                        Logger.Instance?.OperationLog($"发送实例检测广播: {localIP} -> {broadcastAddress}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance?.ExceptionLog($"在网卡 {localIP} 上发送广播失败: {ex.Message}");
                    }
                }

                // 等待回复 - 可能收到多个回复
                var receivedReplies = new List<string>();
                var startTime = DateTime.Now;

                try
                {
                    while ((DateTime.Now - startTime).TotalMilliseconds < 3000) // 3秒内收集所有回复
                    {
                        try
                        {
                            var remoteEP = new IPEndPoint(IPAddress.Any, 0);
                            var data = receiveClient.Receive(ref remoteEP);
                            var reply = Encoding.UTF8.GetString(data);

                            Logger.Instance?.OperationLog($"收到回复: {reply} (来自 {remoteEP.Address}:{remoteEP.Port})");
                            receivedReplies.Add($"{reply}|{remoteEP.Address}");
                        }
                        catch (SocketException ex) when (ex.SocketErrorCode == SocketError.TimedOut)
                        {
                            // 超时，跳出循环
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance?.ExceptionLog($"接收回复时异常: {ex.Message}");
                }

                Logger.Instance?.OperationLog($"总共收到 {receivedReplies.Count} 个回复");

                // 分析收到的回复
                foreach (var replyInfo in receivedReplies)
                {
                    var parts = replyInfo.Split('|');
                    if (parts.Length != 2) continue;

                    var reply = parts[0];
                    var senderIP = parts[1];

                    Logger.Instance?.OperationLog($"分析回复: {reply} 来自 {senderIP}");

                    // 检查回复是否来自其他实例（不是当前机器的任何IP）
                    bool isFromOtherInstance = reply.StartsWith(_appIdentifier);
                    foreach (var localIP in localIPs)
                    {
                        if (reply.Contains(localIP.ToString()) || senderIP == localIP.ToString())
                        {
                            isFromOtherInstance = false;
                            Logger.Instance?.OperationLog($"忽略来自本机的回复: {localIP}");
                            break;
                        }
                    }

                    if (isFromOtherInstance)
                    {
                        Logger.Instance?.OperationLog($"✅ 确认检测到其他实例: {reply} (来自 {senderIP})");
                        return true;
                    }
                }

                Logger.Instance?.OperationLog("❌ 未检测到其他实例");

                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"广播检测异常: {ex.Message}");
                return false;
            }
            finally
            {
                // 清理所有广播客户端
                foreach (var client in broadcastClients)
                {
                    try { client?.Close(); } catch { }
                }
                receiveClient?.Close();
            }
        }

        /// <summary>
        /// 获取网络广播地址（简化版本，假设常见的子网掩码）
        /// </summary>
        private static IPAddress GetNetworkBroadcastAddress(IPAddress localIP)
        {
            try
            {
                byte[] ipBytes = localIP.GetAddressBytes();

                // 根据IP地址类别确定广播地址
                if (ipBytes[0] == 192 && ipBytes[1] == 168)
                {
                    // 192.168.x.x 网段，假设 /24 子网
                    return new IPAddress(new byte[] { ipBytes[0], ipBytes[1], ipBytes[2], 255 });
                }
                else if (ipBytes[0] == 172 && ipBytes[1] >= 16 && ipBytes[1] <= 31)
                {
                    // 172.16.x.x - 172.31.x.x 网段，假设 /24 子网
                    return new IPAddress(new byte[] { ipBytes[0], ipBytes[1], ipBytes[2], 255 });
                }
                else if (ipBytes[0] == 10)
                {
                    // 10.x.x.x 网段，假设 /24 子网
                    return new IPAddress(new byte[] { ipBytes[0], ipBytes[1], ipBytes[2], 255 });
                }
                else
                {
                    // 其他情况，使用全局广播
                    return IPAddress.Broadcast;
                }
            }
            catch
            {
                // 异常情况下使用全局广播
                return IPAddress.Broadcast;
            }
        }

        /// <summary>
        /// 选择最佳的回复IP地址（与发送方在同一网段的IP）
        /// </summary>
        private static string GetBestReplyIP(IPAddress senderIP, List<IPAddress> localIPs)
        {
            try
            {
                byte[] senderBytes = senderIP.GetAddressBytes();

                // 寻找与发送方在同一网段的本机IP
                foreach (var localIP in localIPs)
                {
                    byte[] localBytes = localIP.GetAddressBytes();

                    // 检查是否在同一个C类网段 (前3个字节相同)
                    if (senderBytes[0] == localBytes[0] &&
                        senderBytes[1] == localBytes[1] &&
                        senderBytes[2] == localBytes[2])
                    {
                        Logger.Instance?.OperationLog($"🎯 选择同网段IP回复: {localIP} (发送方: {senderIP})");
                        return localIP.ToString();
                    }
                }

                // 如果没有找到同网段的IP，使用第一个可用的IP
                if (localIPs.Count > 0)
                {
                    Logger.Instance?.OperationLog($"🎯 使用默认IP回复: {localIPs[0]} (发送方: {senderIP})");
                    return localIPs[0].ToString();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"选择回复IP时异常: {ex.Message}");
            }

            // 最后的备选方案
            return GetLocalIPAddress();
        }

        /// <summary>
        /// 启动备用模式 - 持续监控主实例状态
        /// </summary>
        private static void StartStandbyMode()
        {
            Task.Factory.StartNew(() =>
            {
                Logger.Instance?.OperationLog("🔄 备用模式启动，每30秒检测一次主实例状态");

                while (!_isMainInstance)
                {
                    try
                    {
                        // 等待30秒后再次检测
                        Thread.Sleep(30000);

                        Logger.Instance?.OperationLog("🔍 备用模式：检测主实例状态...");

                        // 重新检测是否有其他实例
                        bool hasOtherInstance = BroadcastDetection();

                        if (!hasOtherInstance)
                        {
                            // 没有检测到其他实例，升级为主实例
                            _isMainInstance = true;
                            Logger.Instance?.OperationLog("🎉 主实例消失，当前程序升级为主实例！");

                            // 通知主线程可以继续启动
                            _resetEvent.Set();
                            break;
                        }
                        else
                        {
                            Logger.Instance?.OperationLog("⏳ 主实例仍在运行，继续等待...");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance?.ExceptionLog($"备用模式检测异常: {ex.Message}");
                        // 异常情况下等待一段时间后继续
                        Thread.Sleep(10000);
                    }
                }

                Logger.Instance?.OperationLog("✅ 备用模式结束，程序正常启动");
            }, TaskCreationOptions.LongRunning);
        }
        #endregion

        #region 工具方法
        /// <summary>
        /// 获取本地IP地址
        /// </summary>
        private static string GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"获取本地IP地址失败: {ex.Message}");
            }
            return "127.0.0.1";
        }

        /// <summary>
        /// 获取所有本地IP地址
        /// </summary>
        private static List<IPAddress> GetAllLocalIPAddresses()
        {
            var ipList = new List<IPAddress>();
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        ipList.Add(ip);
                    }
                }
                Logger.Instance?.OperationLog($"发现本地IP地址: {string.Join(", ", ipList)}");
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"获取所有本地IP地址失败: {ex.Message}");
            }
            return ipList;
        }

        /// <summary>
        /// 计算网段广播地址
        /// </summary>
        private static IPAddress GetBroadcastAddress(IPAddress address, IPAddress subnetMask)
        {
            byte[] ipAdressBytes = address.GetAddressBytes();
            byte[] subnetMaskBytes = subnetMask.GetAddressBytes();

            if (ipAdressBytes.Length != subnetMaskBytes.Length)
                throw new ArgumentException("IP地址和子网掩码长度不匹配");

            byte[] broadcastAddress = new byte[ipAdressBytes.Length];
            for (int i = 0; i < broadcastAddress.Length; i++)
            {
                broadcastAddress[i] = (byte)(ipAdressBytes[i] | (subnetMaskBytes[i] ^ 255));
            }
            return new IPAddress(broadcastAddress);
        }

        /// <summary>
        /// 应用程序退出时清理资源
        /// </summary>
        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // 清理网络资源
                _udpListener?.Close();
                _udpListener?.Dispose();

                // 清理简单单例管理器
                SimpleSingletonManager.Release();

                Logger.Instance?.OperationLog("N2Purge应用程序正常退出，所有资源已清理");
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"应用程序退出时清理资源异常: {ex.Message}");
            }

            base.OnExit(e);
        }
        #endregion
    }
}
