@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置变量
set "TASK_NAME=N2PurgeAutoStart"
set "APP_NAME=N2Purge"
set "CURRENT_DIR=%~dp0"
set "APP_PATH=%CURRENT_DIR%..\bin\Debug\N2Purge.exe"
set "STARTUP_DELAY=30"

:: 显示标题
echo ========================================
echo    N2 Purge 自启动设置工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 此脚本需要管理员权限运行
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo [信息] 管理员权限检查通过
echo.

:: 检查应用程序文件是否存在
if not exist "%APP_PATH%" (
    echo [错误] 找不到应用程序文件: %APP_PATH%
    echo 请确保N2Purge.exe文件存在于正确位置
    echo.
    pause
    exit /b 1
)

echo [信息] 应用程序文件检查通过: %APP_PATH%
echo.

:: 显示菜单
:MENU
echo 请选择操作:
echo 1. 设置开机自启动 (推荐)
echo 2. 移除开机自启动
echo 3. 查看当前状态
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto REMOVE
if "%choice%"=="3" goto STATUS
if "%choice%"=="4" goto EXIT
echo [错误] 无效选择，请重新输入
echo.
goto MENU

:SETUP
echo.
echo [信息] 正在设置开机自启动...

:: 删除已存在的任务
schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1

:: 创建临时XML文件
set "TEMP_XML=%TEMP%\%TASK_NAME%.xml"
call :CREATE_XML "%TEMP_XML%" "%APP_PATH%" "%STARTUP_DELAY%"

:: 创建计划任务
schtasks /create /tn "%TASK_NAME%" /xml "%TEMP_XML%" >nul 2>&1
if %errorLevel% equ 0 (
    echo [成功] 开机自启动设置完成
    echo - 任务名称: %TASK_NAME%
    echo - 启动延迟: %STARTUP_DELAY% 秒
    echo - 应用程序: %APP_PATH%
) else (
    echo [错误] 设置开机自启动失败
)

:: 清理临时文件
if exist "%TEMP_XML%" del "%TEMP_XML%" >nul 2>&1

echo.
pause
goto MENU

:REMOVE
echo.
echo [信息] 正在移除开机自启动...

schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1
if %errorLevel% equ 0 (
    echo [成功] 开机自启动已移除
) else (
    echo [信息] 未找到自启动任务或已经移除
)

echo.
pause
goto MENU

:STATUS
echo.
echo [信息] 检查当前状态...

schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo [状态] 开机自启动: 已启用
    echo.
    echo 任务详细信息:
    schtasks /query /tn "%TASK_NAME%" /fo LIST /v | findstr /C:"任务名" /C:"状态" /C:"下次运行时间" /C:"上次运行时间"
) else (
    echo [状态] 开机自启动: 未启用
)

echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用 N2 Purge 自启动设置工具
echo.
exit /b 0

:: 创建XML配置文件的函数
:CREATE_XML
set "XML_FILE=%~1"
set "EXE_PATH=%~2"
set "DELAY=%~3"
set "WORK_DIR=%~dp2"

(
echo ^<?xml version="1.0" encoding="UTF-16"?^>
echo ^<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task"^>
echo   ^<RegistrationInfo^>
echo     ^<Date^>%date%T%time%^</Date^>
echo     ^<Author^>%USERNAME%^</Author^>
echo     ^<Description^>N2 Purge 智能传送带控制系统自动启动任务^</Description^>
echo   ^</RegistrationInfo^>
echo   ^<Triggers^>
echo     ^<BootTrigger^>
echo       ^<Enabled^>true^</Enabled^>
echo       ^<Delay^>PT%DELAY%S^</Delay^>
echo     ^</BootTrigger^>
echo     ^<LogonTrigger^>
echo       ^<Enabled^>true^</Enabled^>
echo       ^<Delay^>PT%DELAY%S^</Delay^>
echo     ^</LogonTrigger^>
echo   ^</Triggers^>
echo   ^<Principals^>
echo     ^<Principal id="Author"^>
echo       ^<UserId^>%USERDOMAIN%\%USERNAME%^</UserId^>
echo       ^<LogonType^>InteractiveToken^</LogonType^>
echo       ^<RunLevel^>HighestAvailable^</RunLevel^>
echo     ^</Principal^>
echo   ^</Principals^>
echo   ^<Settings^>
echo     ^<MultipleInstancesPolicy^>IgnoreNew^</MultipleInstancesPolicy^>
echo     ^<DisallowStartIfOnBatteries^>false^</DisallowStartIfOnBatteries^>
echo     ^<StopIfGoingOnBatteries^>false^</StopIfGoingOnBatteries^>
echo     ^<AllowHardTerminate^>true^</AllowHardTerminate^>
echo     ^<StartWhenAvailable^>true^</StartWhenAvailable^>
echo     ^<RunOnlyIfNetworkAvailable^>false^</RunOnlyIfNetworkAvailable^>
echo     ^<AllowStartOnDemand^>true^</AllowStartOnDemand^>
echo     ^<Enabled^>true^</Enabled^>
echo     ^<Hidden^>false^</Hidden^>
echo     ^<RunOnlyIfIdle^>false^</RunOnlyIfIdle^>
echo     ^<WakeToRun^>false^</WakeToRun^>
echo     ^<ExecutionTimeLimit^>PT0S^</ExecutionTimeLimit^>
echo     ^<Priority^>7^</Priority^>
echo     ^<RestartOnFailure^>
echo       ^<Interval^>PT1M^</Interval^>
echo       ^<Count^>3^</Count^>
echo     ^</RestartOnFailure^>
echo   ^</Settings^>
echo   ^<Actions Context="Author"^>
echo     ^<Exec^>
echo       ^<Command^>%EXE_PATH%^</Command^>
echo       ^<WorkingDirectory^>%WORK_DIR%^</WorkingDirectory^>
echo     ^</Exec^>
echo   ^</Actions^>
echo ^</Task^>
) > "%XML_FILE%"

goto :eof
