﻿using DBEntity;
using log4net;
using N2Purge.userControls;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmPioChart.xaml 的交互逻辑
    /// </summary>
    public partial class frmPioChart : UserControl
    {
        public frmPioChart()
        {
            try
            {
                InitializeComponent();
                dtStart.Value = DateTime.Now.AddMinutes(-5);
                listParams = new List<string>();
                listParams.Add("P_L_REQ");
                listParams.Add("P_U_REQ");
                listParams.Add("P_Ready");
                listParams.Add("P_HO_AVBL");
                listParams.Add("P_ES");
                //listParams.Add("P_Abnormal");
                //listParams.Add("P_InterLock");
                //listParams.Add("P_CSTContain");
                listParams.Add("A_VALID");
                listParams.Add("A_CS_0");
                listParams.Add("A_TR_REQ");
                listParams.Add("A_Busy");
                listParams.Add("A_COMPT");
                listParams.Add("A_CONT");
                listParams.Add("A_GO");
                LoadPortInfo();
               // Task.Run(() => { StartSignalSimulation(); });
            }
            catch (Exception ex) {
                Proj.Log.Logger.Instance.ExceptionLog("FrmCommonConfig_Load: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
          
        }
        private async void LoadPortInfo()
        {
            var portList =await GlobalData.dbHelper.tpport.GetAllTpPortAsync();
            foreach (TpPort port in portList)
            {
                cmbPort.Items.Add(port.Name);
            }
        }
        private async void StartSignalSimulation()
        {
            int a = 0;
            while (true)
            {

                Random rd = new Random();
                a = rd.Next(0, 2);
                //  Console.WriteLine(a);
                bool[] signals = new bool[12];
                for (int i = 0; i < 12; i++)
                {
                    if (a == 0)
                    {
                        signals[i] = true;

                    }
                    else
                    {
                        signals[i] = false;

                    }
                }
                // A_GO 信号设为高电平
                piochart.UpdateSignals(signals);
                Thread.Sleep(1000);
            }

        }
        private List<string> listParams;
        private bool[] currentPIO = new bool[12];  // 添加成员变量存储当前PIO状态
        public void SetPIOValue(string ioName, int value)
        {
            // 根据IO名称设置对应位置的值
            int index = GetIOIndex(ioName);
            if (index >= 0 && index < currentPIO.Length)
            {
                //value != 0 是一个比较表达式：
               // -当 value 不等于 0 时，结果为 true
              //  - 当 value 等于 0 时，结果为 false
                currentPIO[index] = value != 0;
            }
        }

        private int GetIOIndex(string ioName)
        {
            int index = -1;
            // 根据实际IO名称返回对应的索引
            switch (ioName)
            {
              
                case "A_GO":
                    index= 0;
                break;
                case "A_CONT":
                index = 1;
                break;
                case "A_COMPT":
                index = 2;
                break;
                case "A_Busy":
                index = 3;
                break;
                case "A_TR_REQ":
                index = 4;
                break;
            case "A_CS_0":
                index = 5;
                break;
            case "A_VALID":
                index = 6;
                break;
            case "P_ES":
                index = 7;
                break;
            case "P_NO_AVEL":
                index = 8;
                break;
            case "P_Ready":
                index = 9;
                break;
            case "P_U_REQ":
                index = 10;
                break;
            case "P_L_REQ":
                index = 11;
                break;
              
            }
            return index;
        }
        private async void btnQuery_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cmbPort.Text == "")
                {
                    MessageBox.Show("请选择一个Port");
                    return;
                }
                if (dtStart.Value==null)
                {
                    MessageBox.Show("请选择时间");
                    return;
                }
                DateTime startTime =(DateTime)dtStart.Value;
                DateTime endTime = startTime.AddSeconds(60);
                piochart.ClearChart();
                //var oldPioList = await GlobalData.dbHelper.thpiodb.GetPioHistoryByTimeRangeAsync(startTime.AddMinutes(-61), startTime);
                //oldPioList.Where(x => x.Port_Name == cmbPort.Text).ToList();
                //foreach (string pioName in listParams)
                //{
                //    try
                //    {
                //        PioHistory oldThPIO = oldPioList.Last(x => x.IO_Name == pioName);
                //        if (oldThPIO != null)
                //        {
                //            SetPIOValue(pioName, (int)oldThPIO.Io_Value);
                //        }
                //    }
                //    catch (Exception ex)
                //    {
                //        Proj.Log.Logger.Instance.ExceptionLog("FrmPIOChart.cs Error: " + ex.Message
                //            + ", pioName: " + pioName + ", Stack: " + ex.StackTrace);
                //    }
                //}
                //piochart.UpdateSignals(currentPIO);
                var pioList= await GlobalData.dbHelper.thpiodb.GetPioHistoryByTimeRangeAsync(startTime, endTime);
                pioList.Where(x => x.Port_Name == cmbPort.Text).ToList();
                for (int i = 0; i < 60; i++)
                {
                    Array.Clear(currentPIO, 0, currentPIO.Length);
                    DateTime currentStartTime = startTime.AddSeconds(i);
                    DateTime currentEndTime = startTime.AddSeconds(i + 1);
                    foreach (PioHistory currentThPIO in pioList)
                    {
                        if (currentThPIO.Time > currentStartTime && currentThPIO.Time <= currentEndTime)
                        {
                            SetPIOValue(currentThPIO.IO_Name, (int)currentThPIO.Io_Value);
                        }
                    }
                    piochart.UpdateSignals(currentPIO);
                }
            }
            catch (Exception ex)
            {

                Proj.Log.Logger.Instance.ExceptionLog("btnQuery_Click: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
         
        }
    }
}
