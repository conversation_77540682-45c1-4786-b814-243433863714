using N2Purge.ViewModel;
using System;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    public class TpalarmViewModel : VmPropertyChange
    {
        private int _alarmCode;
        private string _alarmUnit;
        private DateTime _startTime;
        private DateTime _lastTime;
        private string _comment;

        public int AlarmCode
        {
            get { return _alarmCode; }
            set
            {
                if (_alarmCode != value)
                {
                    _alarmCode = value;
                    OnPropertyChanged(nameof(AlarmCode));
                }
            }
        }

        public string AlarmUnit
        {
            get { return _alarmUnit; }
            set
            {
                if (_alarmUnit != value)
                {
                    _alarmUnit = value;
                    OnPropertyChanged(nameof(AlarmUnit));
                }
            }
        }

        public DateTime StartTime
        {
            get { return _startTime; }
            set
            {
                if (_startTime != value)
                {
                    _startTime = value;
                    OnPropertyChanged(nameof(StartTime));
                }
            }
        }

        public DateTime LastTime
        {
            get { return _lastTime; }
            set
            {
                if (_lastTime != value)
                {
                    _lastTime = value;
                    OnPropertyChanged(nameof(LastTime));
                }
            }
        }

        public string Comment
        {
            get { return _comment; }
            set
            {
                if (_comment != value)
                {
                    _comment = value;
                    OnPropertyChanged(nameof(Comment));
                }
            }
        }

     
    }
}