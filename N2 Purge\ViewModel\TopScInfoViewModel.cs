﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.ViewModel
{
    //Top模型
    public class TopScInfoViewModel:VmPropertyChange
    {
        public string? _Alarm;
        public string? Alarm
        {
            get { return _Alarm; }
            set
            {
                if (_Alarm != value)
                {
                    _Alarm = value;
                    OnPropertyChanged(nameof(Alarm));
                }
            }
        }
        public int? _Server;

        public int? Server
        {
            get { return _Server; }
            set
            {
                if (_Server != value)
                {
                    _Server = value;
                    OnPropertyChanged(nameof(Server));
                }
            }
        }
        public string? _HSMSState;
        public string? HSMSState
        {
            get { return _HSMSState; }
            set
            {
                if (_HSMSState != value)
                {
                    _HSMSState = value;
                    OnPropertyChanged(nameof(HSMSState));
                }
            }
        }
        public string? _MCS;

        public string? MCS
        {
            get { return _MCS; }
            set
            {
                if (_MCS != value)
                {
                    _MCS = value;
                    OnPropertyChanged(nameof(MCS));
                }
            }
        }
        public string? _ControlState;
        public string? ControlState
        {
            get { return _ControlState; }
            set
            {
                if (_ControlState != value)
                {
                    _ControlState = value;
                    OnPropertyChanged(nameof(ControlState));
                }
            }
        }
        public string? _ScState;

        public string? ScState
        {
            get { return _ScState; }
            set
            {
                if (_ScState != value)
                {
                    _ScState = value;
                    OnPropertyChanged(nameof(ScState));
                }
            }
        }
        public string? _ScMode;
        public string? ScMode
        {
            get { return _ScMode; }
            set
            {
                if (_ScMode != value)
                {
                    _ScMode = value;
                    OnPropertyChanged(nameof(ScMode));
                }
            }
        }
        public string? _MachineState;

        public string? MachineState
        {
            get { return _MachineState; }
            set
            {
                if (_MachineState != value)
                {
                    _MachineState = value;
                    OnPropertyChanged(nameof(MachineState));
                }
            }
        }
        public int? _TotlaShelf;

        public int? TotlaShelf
        {
            get { return _TotlaShelf; }
            set
            {
                if (_TotlaShelf != value)
                {
                    _TotlaShelf = value;
                    OnPropertyChanged(nameof(TotlaShelf));
                }
            }
        }
        public int? _EmptyShelf;
        public int? EmptyShelf
        {
            get { return _EmptyShelf; }
            set
            {
                if (_EmptyShelf != value)
                {
                    _EmptyShelf = value;
                    OnPropertyChanged(nameof(EmptyShelf));
                }
            }
        }
        public int? _Cassette;

        public int? Cassette
        {
            get { return _Cassette; }
            set
            {
                if (_Cassette != value)
                {
                    _Cassette = value;
                    OnPropertyChanged(nameof(Cassette));
                }
            }
        }
        public int? _CommandCnt;

        public int? CommandCnt
        {
            get { return _CommandCnt; }
            set
            {
                if (_CommandCnt != value)
                {
                    _CommandCnt = value;
                    OnPropertyChanged(nameof(CommandCnt));
                }
            }
        }
    }
}
