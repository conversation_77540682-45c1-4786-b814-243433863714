using System;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    public class ThEQPViewModel : VmPropertyChange
    {
        private int _id;
        public int Id
        {
            get { return _id; }
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        private int _eventId;
        public int EventId
        {
            get { return _eventId; }
            set
            {
                _eventId = value;
                OnPropertyChanged(nameof(EventId));
            }
        }

        private string? _eventName;
        public string? Event_Name
        {
            get { return _eventName; }
            set
            {
                _eventName = value;
                OnPropertyChanged(nameof(Event_Name));
            }
        }

        private string? _text;
        public string? Text
        {
            get { return _text; }
            set
            {
                _text = value;
                OnPropertyChanged(nameof(Text));
            }
        }

        private DateTime _time;
        public DateTime Time
        {
            get { return _time; }
            set
            {
                _time = value;
                OnPropertyChanged(nameof(Time));
            }
        }

        private string? _unit;
        public string? Unit
        {
            get { return _unit; }
            set
            {
                _unit = value;
                OnPropertyChanged(nameof(Unit));
            }
        }
    }
}