﻿<UserControl x:Class="N2Purge.frmUserControl.frmAlarmHistory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             xmlns:xctr="http://schemas.xceed.com/wpf/xaml/toolkit" xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
        
            <RowDefinition Height="0"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Label  Content="开始时间" Grid.Column="0" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <xctr:DateTimePicker x:Name="tmstart" Grid.Column="1" Background="White" Width="200" Margin="0" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="结束时间" Grid.Column="2" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <xctr:DateTimePicker x:Name="tmend" Grid.Column="3" Background="White" Width="200" Margin="0" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center" Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="报警编码" Grid.Column="4" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBlock  Width="100" Background="White" Grid.Column="5" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Label  Content="事件名称" Grid.Column="6" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBlock  Width="100" Background="White" Grid.Column="7" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Button x:Name="btnSearch" Content="查询" Grid.Column="8" Margin="2" Background="Snow" Click="btnSearch_Click"/>
                <Button x:Name="btnExport" Content="导出" Grid.Column="9" Margin="2" Background="Snow" Click="btnExport_Click"/>
            </Grid>
        </Border>
  
        <DataGrid Grid.Row="2" x:Name="dgalarm" IsReadOnly="True">
            <DataGrid.Columns>
                <DataGridTextColumn Header="AlarmCode" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="AlarmUnit" Binding="{Binding Unit}" Width="100"/>
                <DataGridTextColumn Header="StartTime" Binding="{Binding Start_Time, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="200"/>
                <DataGridTextColumn Header="ClearTime" Binding="{Binding Clear_Time, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="200"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="*"/>
            </DataGrid.Columns>
        </DataGrid>

        <lvc:CartesianChart Grid.Row="3" x:Name="chartAlarms" Height="300" Margin="10">
            <lvc:CartesianChart.AxisX>
                <lvc:Axis Title="时间" />
            </lvc:CartesianChart.AxisX>
            <lvc:CartesianChart.AxisY>
                <lvc:Axis Title="报警数量" />
            </lvc:CartesianChart.AxisY>
        </lvc:CartesianChart>
    </Grid>
</UserControl>
