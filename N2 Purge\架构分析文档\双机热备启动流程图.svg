<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 1600">
  <defs>
    <style>
      .node { fill: #f9f9f9; stroke: #333; stroke-width: 2; }
      .start { fill: #e1f5fe; }
      .end { fill: #c8e6c9; }
      .exit { fill: #ffcdd2; }
      .standby { fill: #fff3e0; }
      .main { fill: #e8f5e8; }
      .decision { fill: #fff9c4; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .title { font-size: 14px; font-weight: bold; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" class="text title">N2 Purge 双机热备启动流程图</text>
  
  <!-- Start Node -->
  <rect x="550" y="60" width="100" height="40" rx="20" class="node start"/>
  <text x="600" y="85" class="text">程序启动</text>
  
  <!-- App Constructor -->
  <rect x="530" y="130" width="140" height="40" rx="5" class="node"/>
  <text x="600" y="155" class="text">App构造函数</text>
  
  <!-- Global Exception Handler -->
  <rect x="480" y="200" width="240" height="40" rx="5" class="node"/>
  <text x="600" y="225" class="text">GlobalExceptionHandler.Initialize</text>
  
  <!-- OnStartup Event -->
  <rect x="530" y="270" width="140" height="40" rx="5" class="node"/>
  <text x="600" y="295" class="text">OnStartup事件</text>
  
  <!-- PerformSingletonCheck -->
  <rect x="500" y="340" width="200" height="40" rx="5" class="node"/>
  <text x="600" y="365" class="text">PerformSingletonCheck</text>
  
  <!-- Initialize Log -->
  <rect x="520" y="410" width="160" height="40" rx="5" class="node"/>
  <text x="600" y="435" class="text">初始化日志系统</text>
  
  <!-- SimpleSingletonManager -->
  <rect x="450" y="480" width="300" height="40" rx="5" class="node"/>
  <text x="600" y="505" class="text">SimpleSingletonManager.CheckSingleInstance</text>
  
  <!-- Mutex Check Decision -->
  <polygon points="600,550 650,580 600,610 550,580" class="node decision"/>
  <text x="600" y="585" class="text">Mutex检查</text>
  
  <!-- Create New Mutex -->
  <rect x="350" y="650" width="120" height="40" rx="5" class="node"/>
  <text x="410" y="675" class="text">设为主实例</text>
  
  <!-- Mutex Exists -->
  <rect x="730" y="650" width="180" height="40" rx="5" class="node"/>
  <text x="820" y="675" class="text">尝试获取Mutex所有权</text>
  
  <!-- Acquire Success Decision -->
  <polygon points="820,720 870,750 820,780 770,750" class="node decision"/>
  <text x="820" y="755" class="text">获取成功?</text>
  
  <!-- Show Message -->
  <rect x="950" y="820" width="120" height="40" rx="5" class="node"/>
  <text x="1010" y="845" class="text">显示提示信息</text>
  
  <!-- Program Exit -->
  <rect x="950" y="890" width="120" height="40" rx="20" class="node exit"/>
  <text x="1010" y="915" class="text">程序退出</text>
  
  <!-- CheckAndSetupAutoTask -->
  <rect x="300" y="750" width="220" height="40" rx="5" class="node"/>
  <text x="410" y="775" class="text">CheckAndSetupAutoTask</text>
  
  <!-- Check Admin Rights -->
  <rect x="330" y="820" width="160" height="40" rx="5" class="node"/>
  <text x="410" y="845" class="text">检查管理员权限</text>
  
  <!-- Admin Decision -->
  <polygon points="410,890 460,920 410,950 360,920" class="node decision"/>
  <text x="410" y="925" class="text">是否管理员?</text>
  
  <!-- Create Task -->
  <rect x="200" y="990" width="120" height="40" rx="5" class="node"/>
  <text x="260" y="1015" class="text">创建计划任务</text>
  
  <!-- Skip Task -->
  <rect x="500" y="990" width="120" height="40" rx="5" class="node"/>
  <text x="560" y="1015" class="text">跳过任务创建</text>
  
  <!-- Generate XML -->
  <rect x="200" y="1060" width="120" height="40" rx="5" class="node"/>
  <text x="260" y="1085" class="text">生成XML配置</text>
  
  <!-- Execute schtasks -->
  <rect x="180" y="1130" width="160" height="40" rx="5" class="node"/>
  <text x="260" y="1155" class="text">执行schtasks命令</text>
  
  <!-- Create Success Decision -->
  <polygon points="260,1200 310,1230 260,1260 210,1230" class="node decision"/>
  <text x="260" y="1235" class="text">创建成功?</text>
  
  <!-- Task Complete -->
  <rect x="100" y="1300" width="120" height="40" rx="5" class="node end"/>
  <text x="160" y="1325" class="text">任务创建完成</text>
  
  <!-- Task Failed -->
  <rect x="350" y="1300" width="120" height="40" rx="5" class="node exit"/>
  <text x="410" y="1325" class="text">记录失败日志</text>
  
  <!-- CheckAndSetMainInstance -->
  <rect x="300" y="1400" width="220" height="40" rx="5" class="node"/>
  <text x="410" y="1425" class="text">CheckAndSetMainInstance</text>
  
  <!-- Start UDP Listener -->
  <rect x="330" y="1470" width="160" height="40" rx="5" class="node"/>
  <text x="410" y="1495" class="text">启动UDP监听器</text>
  
  <!-- Wait for Ready -->
  <rect x="330" y="1540" width="160" height="40" rx="5" class="node"/>
  <text x="410" y="1565" class="text">等待监听器就绪</text>
  
  <!-- Arrows -->
  <line x1="600" y1="100" x2="600" y2="130" class="arrow"/>
  <line x1="600" y1="170" x2="600" y2="200" class="arrow"/>
  <line x1="600" y1="240" x2="600" y2="270" class="arrow"/>
  <line x1="600" y1="310" x2="600" y2="340" class="arrow"/>
  <line x1="600" y1="380" x2="600" y2="410" class="arrow"/>
  <line x1="600" y1="450" x2="600" y2="480" class="arrow"/>
  <line x1="600" y1="520" x2="600" y2="550" class="arrow"/>
  
  <!-- Mutex Decision Arrows -->
  <line x1="550" y1="580" x2="470" y2="650" class="arrow"/>
  <text x="500" y="620" class="text" style="font-size: 10px;">创建新Mutex</text>
  
  <line x1="650" y1="580" x2="730" y2="650" class="arrow"/>
  <text x="700" y="620" class="text" style="font-size: 10px;">Mutex已存在</text>
  
  <line x1="820" y1="690" x2="820" y2="720" class="arrow"/>
  
  <!-- Acquire Decision Arrows -->
  <line x1="770" y1="750" x2="470" y2="670" class="arrow"/>
  <text x="620" y="710" class="text" style="font-size: 10px;">是</text>
  
  <line x1="870" y1="750" x2="950" y2="840" class="arrow"/>
  <text x="910" y="795" class="text" style="font-size: 10px;">否</text>
  
  <line x1="1010" y1="860" x2="1010" y2="890" class="arrow"/>
  
  <!-- Main Instance Flow -->
  <line x1="410" y1="690" x2="410" y2="750" class="arrow"/>
  <line x1="410" y1="790" x2="410" y2="820" class="arrow"/>
  <line x1="410" y1="860" x2="410" y2="890" class="arrow"/>
  
  <!-- Admin Decision Arrows -->
  <line x1="360" y1="920" x2="260" y2="990" class="arrow"/>
  <text x="310" y="955" class="text" style="font-size: 10px;">是</text>
  
  <line x1="460" y1="920" x2="560" y2="990" class="arrow"/>
  <text x="510" y="955" class="text" style="font-size: 10px;">否</text>
  
  <!-- Task Creation Flow -->
  <line x1="260" y1="1030" x2="260" y2="1060" class="arrow"/>
  <line x1="260" y1="1100" x2="260" y2="1130" class="arrow"/>
  <line x1="260" y1="1170" x2="260" y2="1200" class="arrow"/>
  
  <!-- Success/Failure Arrows -->
  <line x1="210" y1="1230" x2="160" y2="1300" class="arrow"/>
  <text x="185" y="1265" class="text" style="font-size: 10px;">是</text>
  
  <line x1="310" y1="1230" x2="410" y2="1300" class="arrow"/>
  <text x="360" y="1265" class="text" style="font-size: 10px;">否</text>
  
  <!-- Convergence to CheckAndSetMainInstance -->
  <line x1="560" y1="1030" x2="560" y2="1380" class="arrow"/>
  <line x1="560" y1="1380" x2="520" y2="1400" class="arrow"/>
  
  <line x1="160" y1="1340" x2="160" y2="1380" class="arrow"/>
  <line x1="160" y1="1380" x2="300" y2="1400" class="arrow"/>
  
  <line x1="410" y1="1340" x2="410" y2="1400" class="arrow"/>
  
  <!-- Final Flow -->
  <line x1="410" y1="1440" x2="410" y2="1470" class="arrow"/>
  <line x1="410" y1="1510" x2="410" y2="1540" class="arrow"/>
</svg>
