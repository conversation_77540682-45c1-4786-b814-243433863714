using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 全局异常处理器
    /// 处理应用程序中的未捕获异常，防止程序崩溃
    /// </summary>
    public static class GlobalExceptionHandler
    {
        private static bool _isInitialized = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// 初始化全局异常处理
        /// </summary>
        public static void Initialize()
        {
            lock (_lock)
            {
                if (_isInitialized)
                    return;

                try
                {
                    // 处理UI线程未捕获异常
                    Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;

                    // 处理非UI线程未捕获异常
                    AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

                    // 处理Task中的未捕获异常
                    TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                    _isInitialized = true;
                    Logger.Instance.InfoLog("全局异常处理器初始化成功");
                }
                catch (Exception ex)
                {
                    // 如果日志系统还未初始化，写入文件
                    WriteToEmergencyLog($"初始化全局异常处理器失败: {ex}");
                }
            }
        }

        /// <summary>
        /// 清理全局异常处理
        /// </summary>
        public static void Cleanup()
        {
            lock (_lock)
            {
                if (!_isInitialized)
                    return;

                try
                {
                    Application.Current.DispatcherUnhandledException -= OnDispatcherUnhandledException;
                    AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
                    TaskScheduler.UnobservedTaskException -= OnUnobservedTaskException;

                    _isInitialized = false;
                    Logger.Instance.InfoLog("全局异常处理器已清理");
                }
                catch (Exception ex)
                {
                    WriteToEmergencyLog($"清理全局异常处理器失败: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理UI线程未捕获异常
        /// </summary>
        private static void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.Exception;
                string errorMessage = $"UI线程未捕获异常: {exception.Message}";
                
                Logger.Instance.ExceptionLog(errorMessage, exception);
                
                // 显示用户友好的错误消息
                ShowUserFriendlyError("应用程序遇到错误", exception);
                
                // 标记异常已处理，防止程序崩溃
                e.Handled = true;
                
                Logger.Instance.InfoLog("UI线程异常已处理，程序继续运行");
            }
            catch (Exception handlerEx)
            {
                // 异常处理器本身出错，写入紧急日志
                WriteToEmergencyLog($"UI异常处理器失败: {handlerEx}, 原始异常: {e.Exception}");
                
                // 不设置 e.Handled = true，让程序崩溃以避免更严重的问题
            }
        }

        /// <summary>
        /// 处理非UI线程未捕获异常
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                string errorMessage = $"非UI线程未捕获异常 (IsTerminating: {e.IsTerminating}): {exception?.Message ?? e.ExceptionObject?.ToString()}";
                
                Logger.Instance.ExceptionLog(errorMessage, exception);
                
                if (exception != null)
                {
                    // 如果不是终止性异常，尝试显示错误消息
                    if (!e.IsTerminating)
                    {
                        Application.Current?.Dispatcher?.BeginInvoke(new Action(() =>
                        {
                            ShowUserFriendlyError("后台线程遇到错误", exception);
                        }));
                    }
                    else
                    {
                        // 终止性异常，尝试保存重要数据
                        SaveEmergencyData();
                    }
                }
                
                Logger.Instance.InfoLog($"非UI线程异常已记录 (IsTerminating: {e.IsTerminating})");
            }
            catch (Exception handlerEx)
            {
                WriteToEmergencyLog($"非UI异常处理器失败: {handlerEx}, 原始异常: {e.ExceptionObject}");
            }
        }

        /// <summary>
        /// 处理Task中的未观察异常
        /// </summary>
        private static void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                var exception = e.Exception;
                string errorMessage = $"Task未观察异常: {exception.Message}";
                
                Logger.Instance.ExceptionLog(errorMessage, exception);
                
                // 显示用户友好的错误消息
                Application.Current?.Dispatcher?.BeginInvoke(new Action(() =>
                {
                    // ShowUserFriendlyError("后台任务遇到错误", exception);
                }));
                
                // 标记异常已观察，防止程序崩溃
                e.SetObserved();
                
                Logger.Instance.InfoLog("Task异常已处理");
            }
            catch (Exception handlerEx)
            {
                WriteToEmergencyLog($"Task异常处理器失败: {handlerEx}, 原始异常: {e.Exception}");
            }
        }

        /// <summary>
        /// 显示用户友好的错误消息
        /// </summary>
        private static void ShowUserFriendlyError(string title, Exception exception)
        {
            try
            {
                string userMessage = GetUserFriendlyMessage(exception);
                string detailMessage = $"错误详情:\n{exception.Message}\n\n堆栈跟踪:\n{exception.StackTrace}";
                
                var result = MessageBox.Show(
                    $"{userMessage}\n\n是否查看详细错误信息？",
                    title,
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show(detailMessage, "详细错误信息", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                WriteToEmergencyLog($"显示错误消息失败: {ex}");
                
                // 最后的备用方案
                try
                {
                    MessageBox.Show("应用程序遇到错误，请查看日志文件获取详细信息。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                catch
                {
                    // 如果连MessageBox都无法显示，只能放弃
                }
            }
        }

        /// <summary>
        /// 获取用户友好的错误消息
        /// </summary>
        private static string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                FileNotFoundException => "找不到所需的文件，请检查文件是否存在。",
                DirectoryNotFoundException => "找不到指定的目录，请检查路径是否正确。",
                UnauthorizedAccessException => "没有足够的权限访问文件或目录。",
                OutOfMemoryException => "系统内存不足，请关闭其他应用程序后重试。",
                InvalidOperationException => "当前操作无效，请检查操作步骤。",
                ArgumentException => "参数错误，请检查输入的数据。",
                TimeoutException => "操作超时，请检查网络连接或稍后重试。",
                _ => "应用程序遇到未知错误，错误已记录到日志文件中。"
            };
        }

        /// <summary>
        /// 保存紧急数据
        /// </summary>
        private static void SaveEmergencyData()
        {
            try
            {
                Logger.Instance.InfoLog("程序即将终止，尝试保存紧急数据...");
                
                // 停止录制回放服务
                try
                {
                    if (GlobalData.recordReplayService?.IsRecording == true)
                    {
                        var task = GlobalData.recordReplayService.StopRecordingAsync(true);
                        task.Wait(TimeSpan.FromSeconds(5)); // 最多等待5秒
                        Logger.Instance.InfoLog("紧急停止录制完成");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog("紧急停止录制失败", ex);
                }
                
                // 可以在这里添加其他需要保存的数据
                
                Logger.Instance.InfoLog("紧急数据保存完成");
            }
            catch (Exception ex)
            {
                WriteToEmergencyLog($"保存紧急数据失败: {ex}");
            }
        }

        /// <summary>
        /// 写入紧急日志（当正常日志系统不可用时）
        /// </summary>
        private static void WriteToEmergencyLog(string message)
        {
            try
            {
                string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "emergency.log");
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // 如果连紧急日志都无法写入，只能放弃
            }
        }

        /// <summary>
        /// 手动处理异常（供其他代码调用）
        /// </summary>
        public static void HandleException(Exception exception, string context = "")
        {
            try
            {
                string errorMessage = string.IsNullOrEmpty(context) 
                    ? $"手动处理异常: {exception.Message}"
                    : $"手动处理异常 [{context}]: {exception.Message}";
                
                Logger.Instance.ExceptionLog(errorMessage, exception);
                
                // 在UI线程上显示错误消息
                if (Application.Current?.Dispatcher != null)
                {
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        ShowUserFriendlyError("操作遇到错误", exception);
                    }
                    else
                    {
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            ShowUserFriendlyError("操作遇到错误", exception);
                        }));
                    }
                }
            }
            catch (Exception handlerEx)
            {
                WriteToEmergencyLog($"手动异常处理失败: {handlerEx}, 原始异常: {exception}");
            }
        }
    }
}
