# N2 Purge 技术架构演示文档

---

## 📋 目录

1. [项目概述](#项目概述)
2. [技术栈](#技术栈)
3. [系统架构](#系统架构)
4. [核心模块](#核心模块)
5. [数据流程](#数据流程)
6. [设计模式](#设计模式)
7. [关键特性](#关键特性)
8. [部署架构](#部署架构)
9. [性能优化](#性能优化)
10. [未来规划](#未来规划)

---

## 🎯 项目概述

### N2 Purge 系统简介
- **项目名称**: N2 Purge 智能传送带控制系统
- **技术框架**: .NET 8.0 + WPF
- **应用领域**: 工业自动化、物料传送控制
- **核心功能**: 传送带监控、PLC通信、数据管理、用户权限控制

### 系统特点
- 🔄 **实时监控**: 传送带状态实时更新
- 🤖 **自动化控制**: PLC设备自动化通信
- 📊 **数据管理**: 完整的数据存储和查询
- 🎮 **录制回放**: 操作录制与自动回放
- 👥 **权限管理**: 多级用户权限控制

---

## 🛠️ 技术栈

### 前端技术
```
UI框架: WPF (Windows Presentation Foundation)
.NET版本: .NET 8.0
UI组件: Xceed.Wpf.Toolkit
图表组件: LiveCharts.Wpf, OxyPlot.Wpf
```

### 后端技术
```
开发语言: C# 12.0
数据库: MySQL 8.0
ORM: 自定义 DatabaseHelper
通信协议: PLC通信、WCF、SignalR
```

### 第三方组件
```
JSON处理: Newtonsoft.Json
日志系统: Proj.Log (自研)
PLC通信: PlcTools (自研)
录制回放: Proj.RecordReplay (自研)
CSV导出: CSVTools (自研)
```

---

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────┐
│                    N2 Purge 系统                        │
├─────────────────────────────────────────────────────────┤
│  表示层 (Presentation Layer)                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ MainWindow  │ │ Conveyor UI │ │ Management  │        │
│  │   主界面     │ │  传送带界面  │ │   管理界面   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ Conveyor    │ │ User Mgmt   │ │ Record      │        │
│  │ Controller  │ │ Service     │ │ Replay      │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  数据访问层 (Data Access Layer)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ DbHelper    │ │ PlcHelper   │ │ CsvHelper   │        │
│  │ 数据库访问   │ │ PLC通信     │ │ CSV导出     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure Layer)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ MySQL DB    │ │ PLC Device  │ │ File System │        │
│  │ 数据库      │ │ PLC设备     │ │ 文件系统    │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 模块依赖关系
```
N2Purge (主程序)
├── DBEntity (数据实体)
├── PlcTools (PLC通信)
├── Proj.Log (日志系统)
├── Proj.RecordReplay (录制回放)
├── CSVTools (CSV处理)
├── WCFClient (WCF通信)
└── EmulatorClient (模拟器客户端)
```

---

## 🧩 核心模块

### 1. 传送带控制模块 (Conveyor)
```csharp
核心类: frmConvryorMain
功能:
- 传送带状态监控
- 货物跟踪管理
- PLC数据读写
- UI实时更新
- 状态机控制
```

### 2. 数据管理模块 (Data Management)
```csharp
核心类: DbHelper, GlobalData
功能:
- MySQL数据库连接
- 数据CRUD操作
- 全局数据管理
- 配置文件管理
```

### 3. PLC通信模块 (PLC Communication)
```csharp
核心类: PlcHelper, PLCTools
功能:
- PLC设备连接
- 实时数据读取
- 指令发送
- 通信状态监控
```

### 4. 用户管理模块 (User Management)
```csharp
核心类: UserManagement
功能:
- 用户登录验证
- 权限级别控制
- 用户信息管理
- 操作权限检查
```

### 5. 录制回放模块 (Record & Replay)
```csharp
核心类: SimpleRecordReplayService
功能:
- 操作录制
- 自动回放
- 热键配置
- 文件管理
```

---

## 🔄 数据流程

### 传送带监控数据流
```
PLC设备 → PlcHelper → MainLoop → ShelfViewModel → UI更新
    ↓
数据库存储 ← DbHelper ← 业务逻辑处理
```

### 用户操作数据流
```
用户界面 → 事件处理 → 业务逻辑 → 数据验证 → 数据库操作
    ↓
日志记录 ← Proj.Log ← 操作结果
```

### 录制回放数据流
```
用户操作 → 全局钩子 → 录制服务 → JSON文件
    ↓
回放触发 → 文件读取 → 操作模拟 → 界面响应
```

---

## 🎨 设计模式

### 1. MVVM模式
```csharp
Model: 数据实体 (DBEntity)
View: XAML界面文件
ViewModel: ShelfViewModel, TransferViewModel等
```

### 2. 单例模式
```csharp
GlobalData: 全局数据管理
PLCTools.Instance: PLC工具实例
WCFClient.Instance: WCF客户端实例
```

### 3. 观察者模式
```csharp
INotifyPropertyChanged: 属性变更通知
事件驱动: ControlLeftClick, ShelfLeftDbClick
```

### 4. 工厂模式
```csharp
DatabaseHelper<T>: 泛型数据访问工厂
ObjectContent: 控件内容工厂
```

### 5. 策略模式
```csharp
不同的PLC通信策略
多种数据导出策略
```

---

## ⭐ 关键特性

### 1. 实时监控系统
- **MainLoop机制**: 后台线程持续监控
- **状态机管理**: Entry → OnTrans → Exit
- **异步UI更新**: 避免界面卡顿
- **异常处理**: 完善的错误恢复机制

### 2. 智能录制回放
- **全局钩子**: 捕获所有用户操作
- **JSON存储**: 结构化数据保存
- **可配置热键**: 灵活的快捷键设置
- **自动回放**: 精确的操作重现

### 3. 多级权限控制
```csharp
权限级别:
- Level 0: 游客 (只读)
- Level 1: 操作员 (基本操作)
- Level 2: 管理员 (用户管理)
- Level 3: 超级管理员 (完全控制)
```

### 4. 高性能数据处理
- **连接池管理**: 数据库连接优化
- **异步操作**: 非阻塞数据访问
- **缓存机制**: 热点数据缓存
- **批量处理**: 大量数据优化

---

## 🚀 部署架构

### 开发环境
```
IDE: Visual Studio 2022
.NET SDK: 8.0
数据库: MySQL 8.0
PLC模拟器: 自研模拟器
```

### 生产环境
```
操作系统: Windows 10/11
运行时: .NET 8.0 Runtime
数据库: MySQL 8.0 Server
PLC设备: 工业级PLC控制器
```

### 配置文件
```
App.config: 数据库连接字符串
PlcConfig.json: PLC设备配置
RecordReplayConfig.json: 录制回放配置
cvio_config.json: IO映射配置
```

---

## ⚡ 性能优化

### 1. UI性能优化
- **虚拟化**: 大量控件虚拟化显示
- **异步更新**: UI更新使用BeginInvoke
- **批量刷新**: 减少频繁的属性通知
- **内存管理**: 及时释放不用的资源

### 2. 数据库优化
- **索引优化**: 关键字段建立索引
- **连接池**: 复用数据库连接
- **批量操作**: 减少数据库往返次数
- **查询优化**: 使用高效的SQL语句

### 3. PLC通信优化
- **连接复用**: 保持PLC连接活跃
- **数据缓存**: 缓存频繁读取的数据
- **异常重试**: 自动重连机制
- **超时控制**: 避免长时间阻塞

---

## 🔮 未来规划

### 短期目标 (3-6个月)
- ✅ 完善录制回放功能
- ✅ 优化UI响应性能
- 🔄 增加更多PLC设备支持
- 🔄 完善异常处理机制

### 中期目标 (6-12个月)
- 📱 开发移动端监控应用
- 🌐 增加Web管理界面
- 📊 增强数据分析功能
- 🔒 加强安全性措施

### 长期目标 (1-2年)
- 🤖 集成AI智能诊断
- ☁️ 云端数据同步
- 🔗 IoT设备集成
- 📈 大数据分析平台

---

## 📞 联系信息

**开发团队**: N2 Purge Development Team  
**技术支持**: 提供7x24小时技术支持  
**文档更新**: 持续更新技术文档  
**版本发布**: 定期发布新版本和补丁  

---

## 📊 技术指标

### 系统性能指标
```
响应时间: < 100ms (UI操作)
数据处理: > 1000 records/second
并发用户: 支持50+并发用户
可用性: 99.9% uptime
内存占用: < 500MB (正常运行)
CPU占用: < 10% (空闲状态)
```

### 可靠性指标
```
MTBF: > 720小时 (平均故障间隔)
MTTR: < 30分钟 (平均修复时间)
数据完整性: 99.99%
备份恢复: < 5分钟
错误率: < 0.1%
```

---

## 🔧 开发工具链

### 开发环境
```
IDE: Visual Studio 2022 Community/Professional
版本控制: Git + GitHub/GitLab
包管理: NuGet Package Manager
构建工具: MSBuild
调试工具: Visual Studio Debugger
```

### 测试工具
```
单元测试: MSTest/NUnit
集成测试: 自定义测试框架
性能测试: PerfView
内存分析: JetBrains dotMemory
代码覆盖: OpenCover
```

### 部署工具
```
打包工具: ClickOnce/MSI
配置管理: App.config/JSON配置
日志分析: 自研日志系统
监控工具: 系统性能监控
```

---

## 📋 代码质量

### 编码规范
```
命名规范: Pascal/Camel Case
注释覆盖: > 80%
代码复用: 模块化设计
异常处理: 全面的try-catch
日志记录: 详细的操作日志
```

### 架构原则
```
单一职责: 每个类职责明确
开闭原则: 对扩展开放，对修改关闭
依赖倒置: 依赖抽象而非具体实现
接口隔离: 接口设计精简
里氏替换: 子类可替换父类
```

---

*本文档最后更新时间: 2024年8月*
*版本: v1.0*
*作者: N2 Purge Development Team*
