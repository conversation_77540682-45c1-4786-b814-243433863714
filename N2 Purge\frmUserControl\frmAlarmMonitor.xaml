﻿<UserControl x:Class="N2Purge.frmUserControl.frmAlarmMonitor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border BorderThickness="1" BorderBrush="Black">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="80"/>
                </Grid.ColumnDefinitions>
                <Label Grid.Column="0" Margin="2" Content="单元" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" TextBlock.FontSize="16" Background="AliceBlue"/>
                <ComboBox Margin="2" x:Name="cmb" Grid.Column="1"  Background="White" />
                <Button x:Name="btnclear" Content="清除" Grid.Column="2" Background="AliceBlue" Margin="2" Click="btnclear_Click"/>
                <Button x:Name="btnsolution" Content="解决方法" Grid.Column="3" Background="AliceBlue" Margin="2" Click="btnsolution_Click"/>
                
            </Grid>
        </Border>
        <DataGrid Grid.Row="1" x:Name="dgv" CanUserAddRows="False" BorderThickness="1" BorderBrush="Black" AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="AlarmCode" Binding="{Binding AlarmCode}" Width="1*"/>
                <DataGridTextColumn Header="AlarmUnit" Binding="{Binding AlarmUnit}" Width="1*"/>
                <DataGridTextColumn Header="StartTime" Binding="{Binding StartTime}" Width="1*"/>
                <DataGridTextColumn Header="LastTime" Binding="{Binding LastTime}" Width="1*"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
