﻿<UserControl x:Class="N2Purge.frmUserControl.frmPioChart"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl" xmlns:xctk="http://schemas.xceed.com/wpf/xaml/toolkit" xmlns:usercontrols="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <StackPanel Orientation="Horizontal" Margin="5">
            <TextBlock Text="Por" VerticalAlignment="Center" Margin="5,0"/>
            <ComboBox x:Name="cmbPort" Width="150" Margin="5,0">
                
            </ComboBox>
            <TextBlock Text="开始时间" VerticalAlignment="Center" Margin="20,0,5,0"/>
            <xctk:DateTimePicker x:Name="dtStart" Width="250"  Format="Custom" FormatString="yyyy-MM-dd HH:mm:ss"/>
            <Button Content="查询" x:Name="btnQuery" Width="60" Margin="20,0,5,0" Click="btnQuery_Click"/>
            <TextBlock Text="(可查询开始时间之后60秒数据)" VerticalAlignment="Center" Margin="5,0"/>
        </StackPanel>
        <usercontrols:PioChart x:Name="piochart" Grid.Row="1" Height="500"  />
    </Grid>
</UserControl>
