using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace N2Purge.Converter
{
    /// <summary>
    /// 将宽度、高度、厚度和角度转换为圆上的点坐标
    /// </summary>
    public class PointConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 4 || !(values[0] is double width) || !(values[1] is double height) || 
                !(values[2] is double thickness) || !(values[3] is double angle))
            {
                return new Point(0, 0);
            }

            // 计算中心点和半径
            double centerX = width / 2;
            double centerY = height / 2;
            double radius = Math.Min(centerX, centerY) - thickness;

            if (radius < 0) radius = 0;

            // 计算角度对应的弧度
            double radians = angle * Math.PI / 180;

            // 计算点坐标
            double x = centerX + radius * Math.Cos(radians);
            double y = centerY + radius * Math.Sin(radians);

            return new Point(x, y);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}