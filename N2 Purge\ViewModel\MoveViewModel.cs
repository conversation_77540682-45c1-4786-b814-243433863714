using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// Move Vm
    /// </summary>
    public class MoveViewModel : VmPropertyChange
    {
        private string sourceLocation;
        private string selectedSourceLocation;
        private string priority;

        public string SourceLocation
        {
            get { return sourceLocation; }
            set
            {
                if (sourceLocation != value)
                {
                    sourceLocation = value;
                    OnPropertyChanged(nameof(SourceLocation));
                }
            }
        }

        public string Priority
        {
            get { return priority; }
            set
            {
                if (priority != value)
                {
                    priority = value;
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

        public string SelectedSourceLocation
        {
            get { return selectedSourceLocation; }
            set
            {
                if (selectedSourceLocation != value)
                {
                    selectedSourceLocation = value;
                    OnPropertyChanged(nameof(SelectedSourceLocation));
                }
            }
        }
        
    }
}