using System.Windows;

namespace N2Purge.frmUserControl
{
    public partial class FrmUpdatePriority : Window
    {
        public string CommandId;
        public int Priority;

        public FrmUpdatePriority(string commandId)
        {
            InitializeComponent();
            CommandId = commandId;
            txtCommand.Text = commandId;
        }

        private void btnSubmit_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(txtPriority.Text))
            {
                MessageBox.Show("Please enter priority.");
                return;
            }

            if (!int.TryParse(txtPriority.Text, out int priority))
            {
                MessageBox.Show("Please enter integer for priority.");
                return;
            }

            Priority = priority;
            DialogResult = true;
            Close();
        }
    }
}