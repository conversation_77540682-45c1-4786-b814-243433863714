﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// Crane ViewModel
    /// </summary>
    public class CraneViewModel:VmPropertyChange
    {
        private double _cranePosition;
        public double CranePosition
        {
            get => _cranePosition;
            set
            {
                if (_cranePosition != value)
                {
                    _cranePosition = value;
                    OnPropertyChanged(nameof(CranePosition));
                }
            }
        }
        private Brush _fillColor;
        private bool _isVisible;
        private bool _isVisible2;
        private bool _isVisible3;
        private bool _isVisible4;

        //ForK uP？
        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged(nameof(IsVisible));
                }
            }
        }

        //Extern UP？
        public bool IsVisible2
        {
            get => _isVisible2;
            set
            {
                if (_isVisible2 != value)
                {
                    _isVisible2 = value;
                    OnPropertyChanged(nameof(IsVisible2));
                }
            }
        }
        //Fork Down
        public bool IsVisible3
        {
            get => _isVisible3;
            set
            {
                if (_isVisible3 != value)
                {
                    _isVisible3 = value;
                    OnPropertyChanged(nameof(IsVisible3));
                }
            }
        }
        //ExFork Down
        public bool IsVisible4
        {
            get => _isVisible4;
            set
            {
                if (_isVisible4 != value)
                {
                    _isVisible4 = value;
                    OnPropertyChanged(nameof(IsVisible4));
                }
            }
        }
        //Crane 填充色
        public Brush FillColor
        {
            get => _fillColor;
            set
            {
                if (_fillColor != value)
                {
                    _fillColor = value;
                    OnPropertyChanged(nameof(FillColor));
                }
            }
        }
        private double _craneWidth;
        //Crane UI实际宽度
        public double CraneWidth
        {
            get => _craneWidth;
            set
            {
                if (_craneWidth != value)
                {
                    _craneWidth = value;
                    OnPropertyChanged(nameof(CraneWidth));
                }
            }
        }

        private double _containerWidth;
        //Crane UI 运行的border实际宽带
        public double ContainerWidth
        {
            get => _containerWidth;
            set
            {
                if (_containerWidth != value)
                {
                    _containerWidth = value;
                    OnPropertyChanged(nameof(ContainerWidth));
                }
            }
        }

        private double _plcPosition;
        //PLC的位置
        public double PlcPosition
        {
            get => _plcPosition;
            set
            {
                if (_plcPosition != value)
                {
                    _plcPosition = value;
                    // 将PLC实际位置值(-5503.72到285.85)映射到UI显示范围(0到Container
                    double minPlc = -5503.72;
                    double maxPlc = 285.85;
                    double normalizedPosition = Math.Round((value - minPlc) / (maxPlc - minPlc), 4);
                    double pos = Math.Round(normalizedPosition * ContainerWidth, 2);
                   // Console.WriteLine(pos);
                    //if (pos< 361.01)
                    //{
                    //    pos = Math.Round(normalizedPosition * ContainerWidth, 2);
                    //}
                    //else
                    //{
                    //    pos = Math.Round(normalizedPosition * ContainerWidth, 2) - CraneWidth;
                    //}
                    CranePosition = pos;
                    OnPropertyChanged(nameof(PlcPosition));
                }
            }
        }
    }
}
