﻿<Window x:Class="N2Purge.frmUserControl.FrmTpAlarm"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="报警信息" Height="600" Width="1000" WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部红色标题区域 -->
        <Border Grid.Row="0" Background="Red">
            <Grid>
                <TextBlock x:Name="lblAlarm" Text="Alarm:" Foreground="White" FontSize="24" 
                         VerticalAlignment="Center" Margin="10,0,0,0"/>
                <Button Content="关闭" Width="80" Height="30" 
                        HorizontalAlignment="Right" Margin="0,0,10,0"
                        Click="BtnClose_Click"/>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1" x:Name="dgvAlarms" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False"
                  IsReadOnly="True">
            <DataGrid.Columns>
                <DataGridTextColumn Header="AlarmCode" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="AlarmUnit" Binding="{Binding Unit}" Width="100"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="*"/>
                <DataGridTextColumn Header="StartTime" Binding="{Binding Start_Time, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="150"/>
                <DataGridTextColumn Header="LastTime" Binding="{Binding Last_Time, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="150"/>
                <DataGridTextColumn Header="Count" Binding="{Binding Count}" Width="80"/>
            </DataGrid.Columns>
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Code}" Value="123">
                            <Setter Property="Background" Value="Red"/>
                            <Setter Property="Foreground" Value="White"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>
        </DataGrid>
    </Grid>
</Window>
