﻿using N2Purge.frmUserControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;
using N2Purge.silan;

namespace N2Purge
{
    public class FrmHelper
    {
        public static Dictionary<string, UserControl> CachedForm = new Dictionary<string, UserControl>();
        public static UserControl LastMenuForm = null;
        public static UserControl CurrentForm = null;
        public static UserControl MainForm = null;
        public static frmMainsilan frmMain
        {
            get
            {
                if (CachedForm.ContainsKey(typeof(frmMainsilan).Name))
                {
                    return (frmMainsilan)CachedForm[typeof(frmMainsilan).Name];
                }
                else
                {
                    return null;
                }
            }
        }
        private static object openFormLockObj = new object();
        //页面切换
        public static void Show<T>(ContentControl container, object relatedButton = null) where T : UserControl, new()
        {
            if (true)
            {

                lock (openFormLockObj)
                {
                    //if (CurrentForm != null && (CurrentForm.GetType().Name == typeof(frmDialog).Name || CurrentForm.GetType().Name == typeof(frmDLS).Name))
                    //{
                    //    (MainForm as frmMainCommon).AddMinDialogs(CurrentForm);
                    //}

                    if (CurrentForm != null)
                    {

                    }

                    if (!CachedForm.ContainsKey(typeof(T).Name))
                    {
                        UserControl temp = new T();
                        CachedForm.Add(typeof(T).Name, temp);
                    }
                    UserControl frm = CachedForm[typeof(T).Name];
                    if (typeof(T).Name == "frmUser")
                    {
                        frm.VerticalAlignment = VerticalAlignment.Center;
                        frm.HorizontalAlignment = HorizontalAlignment.Center;
                    }
                    else
                    {
                        frm.Margin = new Thickness(0);
                    }
                    container.Content = frm;
                    frm.Visibility = Visibility.Visible;
                    CurrentForm = frm;
                    LastMenuForm = CurrentForm;
                }
            }
        }
    }
}
