﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// Part Port VM LP/OP/BD
    /// </summary>
   public class PortViewModel:VmPropertyChange
    {
        public bool IsPurge = false;
        public bool _IsEnabled = true;
        public bool IsEnabled
        {
            get { return _IsEnabled; }
            set
            {
                if (_IsEnabled != value)
                {
                    _IsEnabled = value;
                    OnPropertyChanged(nameof(IsEnabled));
                }
            }
        }
        public string? _PortLocation;
        public string? PortLocation
        {
            get { return _PortLocation; }
            set
            {
                if (_PortLocation != value)
                {
                    _PortLocation = value;
                    OnPropertyChanged(nameof(PortLocation));
                }
            }
        }
        public int? _PortColor;

        public int? PortColor
        {
            get { return _PortColor; }
            set
            {
                if (_PortColor != value)
                {
                    _PortColor = value;
                    OnPropertyChanged(nameof(PortColor));
                }
            }
        }

    }
}
