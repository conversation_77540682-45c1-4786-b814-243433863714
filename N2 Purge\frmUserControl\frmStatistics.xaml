﻿<UserControl x:Class="N2Purge.frmUserControl.frmStatistics"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             xmlns:xctr="http://schemas.xceed.com/wpf/xaml/toolkit"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧面板 -->
        <GroupBox Header="MCBF / MCBA / MTBF / MTTR" Margin="5" Background="AliceBlue">
            <StackPanel Margin="5">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="250"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                    </Grid.RowDefinitions>

                    <TextBlock Text="开始时间" VerticalAlignment="Center"/>
                    <xctr:DateTimePicker x:Name="dtstart" Grid.Column="1" Margin="3" Background="White"/>

                    <TextBlock Text="结束时间" Grid.Row="1" VerticalAlignment="Center"/>
                    <xctr:DateTimePicker x:Name="dtend" Grid.Row="1" Margin="3" Grid.Column="1" Background="White"/>

                    <Button x:Name="btnSearch" Grid.Column="2" Grid.RowSpan="2" Content="查询" Width="60" Height="25" Margin="5" Background="Snow" Click="btnSearch_Click"/>
                </Grid>

                <Grid Margin="0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                        <RowDefinition Height="25"/>
                    </Grid.RowDefinitions>

                    <TextBlock Text="搬运次数:" VerticalAlignment="Center"/>
                    <TextBlock Grid.Column="1" Text="{Binding TransportCount}" VerticalAlignment="Center"/>

                    <TextBlock Text="Failure次数:" Grid.Row="1" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding FailureCount}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="1" Grid.Column="2" Text="(>30 min)" VerticalAlignment="Center"/>

                    <TextBlock Text="Alarm次数:" Grid.Row="2" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding AlarmCount}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="2" Grid.Column="2" Text="(>6 min)" VerticalAlignment="Center"/>

                    <TextBlock Text="查询时间(s):" Grid.Row="3" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding QueryTime}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="3" Grid.Column="2" Text="(hour)" VerticalAlignment="Center"/>

                    <TextBlock Text="报警持续时间(s):" Grid.Row="4" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding AlarmDuration}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="4" Grid.Column="2" Text="(hour)" VerticalAlignment="Center"/>

                    <TextBlock Text="MCBF:" Grid.Row="5" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding MCBF}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="5" Grid.Column="2" Text="(Mean cycles between failures)" VerticalAlignment="Center"/>

                    <TextBlock Text="MCBA:" Grid.Row="6" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding MCBA}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="6" Grid.Column="2" Text="(Mean cycles between alarms)" VerticalAlignment="Center"/>

                    <TextBlock Text="MTBF:" Grid.Row="7" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding MTBF}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="7" Grid.Column="2" Text="(Mean time(hour) between)" VerticalAlignment="Center"/>

                    <TextBlock Text="MTTR:" Grid.Row="8" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="8" Grid.Column="1" Text="{Binding MTTR}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="8" Grid.Column="2" Text="(Mean time(hour) to repair)" VerticalAlignment="Center"/>

                    <TextBlock Text="Up Time:" Grid.Row="9" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="9" Grid.Column="1" Text="{Binding UpTime}" VerticalAlignment="Center"/>
                </Grid>
            </StackPanel>
        </GroupBox>

        <!-- 右侧面板 -->
        <GroupBox Header="稼动率" Grid.Column="1" Margin="5" Background="AliceBlue">
            <StackPanel Margin="5">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="250"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Text="日期" VerticalAlignment="Center"/>
                    <xctr:DateTimePicker x:Name="dateRunRate" Grid.Column="1" Background="White"/>
                    <Button Grid.Column="2" Content="查询" Width="60" Height="25" Margin="5"/>
                </Grid>

                <TextBlock Text="稼动率:" Margin="0,10"/>
                <TextBlock Text="{Binding UtilizationRate}" Margin="20,0"/>
            </StackPanel>
        </GroupBox>
    </Grid>
</UserControl>
