using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Windows.Controls;

namespace N2Purge.userControls
{
    public partial class PioChart : UserControl
    {
        private PlotModel plotModel;
        private List<LineSeries> signalSeries;
        private const int MaxPoints = 60;  // 最大显示点数
        private double currentX = 0;
        

        public PioChart()
        {
            InitializeComponent();
            InitializePlot();
        }

        private void InitializePlot()
        {
            plotModel = new PlotModel { Background = OxyColors.Black };
            
            // 配置X轴
            var timeAxis = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Minimum = 0,
                Maximum = MaxPoints,
               TextColor = OxyColors.LightBlue,
               // AxislineColor = OxyColors.Yellow,
               // TicklineColor = OxyColors.LightBlue,
               // MajorGridlineStyle = LineStyle.Dot,
               // MajorGridlineColor = OxyColors.Yellow,
               // MinorGridlineStyle = LineStyle.Dot,
               // MinorGridlineColor = OxyColors.DarkGray,
                MajorStep = 5,
                MinorStep = 1
            };
            plotModel.Axes.Add(timeAxis);

           // 配置Y轴 - 每个信号占用固定高度
            var valueAxis = new LinearAxis
            {
                Position = AxisPosition.Left,
                Minimum = 0,
                Maximum = 12,
                IsAxisVisible = false,
                MajorGridlineStyle = LineStyle.None,
                MinorGridlineStyle = LineStyle.None,
                AxislineColor= OxyColors.Red,
                TicklineColor = OxyColors.Red,
                MajorGridlineColor = OxyColors.Red,
              
                MinorGridlineColor = OxyColors.Red,
            };
            plotModel.Axes.Add(valueAxis);

            signalSeries = new List<LineSeries>();
            for (int i = 0; i < 12; i++)
            {
                var series = new StairStepSeries  // 使用阶梯系列而不是普通线系列
                {
                    Color = i < 8 ? OxyColors.Red : OxyColors.Blue,
                    StrokeThickness = 1,
                    VerticalStrokeThickness = 1,
                    MarkerSize = 0
                };
                plotModel.Series.Add(series);
                signalSeries.Add(series);

                //为每个信号添加一条基准线
                //var baseline = new LineSeries
                //{
                //    Color = OxyColors.DarkGray,
                //    StrokeThickness = 1,
                //    LineStyle = LineStyle.Dot
                //};
                //baseline.Points.Add(new DataPoint(0, 11 - i));
                //baseline.Points.Add(new DataPoint(MaxPoints, 11 - i));
                //plotModel.Series.Add(baseline);
            }

            plotView.Model = plotModel;
        }
       


        public void UpdateSignals(bool[] values)
        {
            if (values == null || values.Length != signalSeries.Count)
                return;
            
            for (int i = 0; i < values.Length; i++)
            {
                var series = signalSeries[i] as StairStepSeries;
                double y = 12 - i;  // 从上到下排列信号

                // 添加新的数据点,- 当信号为false时，显示在基准线上
                // -当信号为true时，显示在基准线上方0.6个单位的位置
                series.Points.Add(new DataPoint(currentX, y + (values[i] ? 0.6 : 0)));
            
                // 保持固定显示点数
                if (series.Points.Count > MaxPoints)
                {
                    series.Points.RemoveAt(0);
                }
            }
            currentX++;
            if (currentX > MaxPoints)
            {
                //var timeAxis = plotModel.Axes[0] as LinearAxis;
                //timeAxis.Minimum = currentX - MaxPoints;
                //timeAxis.Maximum = currentX;
                currentX = 0;  // 重置为0
                foreach (var series in signalSeries)
                {
                    series.Points.Clear();  // 清空所有点
                }
            }
            
            plotModel.InvalidatePlot(true);
        }

        public void ClearChart()
        {
            foreach (var series in signalSeries)
            {
                series.Points.Clear();
            }
            currentX = 0;
            var timeAxis = plotModel.Axes[0] as LinearAxis;
            timeAxis.Minimum = 0;
            timeAxis.Maximum = MaxPoints;
            plotModel.InvalidatePlot(true);
        }
    }
}