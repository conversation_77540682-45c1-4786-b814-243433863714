﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR.Client;

namespace N2Purge.CommunicationHelper
{
    public class SignalirHelper
    {
        private HubConnection _connection;

        public SignalirHelper(string hubUrl)
        {
            _connection = new HubConnectionBuilder()
                .WithUrl(hubUrl)
                .Build();
        }

        public async Task ConnectAsync()
        {
            _connection.Closed += async (error) =>
            {
                await Task.Delay(new Random().Next(0, 5) * 1000);
                await ConnectAsync();
            };

            await _connection.StartAsync();
        }

        public async Task DisconnectAsync()
        {
            await _connection.StopAsync();
            await _connection.DisposeAsync();
        }

        public void On<T>(string methodName, Action<T> handler)
        {
            _connection.On(methodName, handler);
        }

        public async Task SendAsync(string methodName, object data)
        {
            await _connection.SendAsync(methodName, data);
        }
    }
}