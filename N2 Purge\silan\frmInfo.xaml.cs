﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBEntity;

namespace N2Purge.silan
{
    /// <summary>
    /// frmInfo.xaml 的交互逻辑
    /// </summary>
    public partial class frmInfo : UserControl
    {
        public frmInfo()
        {
            InitializeComponent();
         
            GlobalData.PortLeftDbClick += GlobalData_PortLeftDbClick;
           // this.DataContext = GlobalData.gbfrmInfoViewModel;
        }

        private async void GlobalData_PortLeftDbClick(string portLocation, ViewModel.PortViewModel portViewModel)
        {
            if (!string.IsNullOrEmpty(portLocation) && portViewModel != null)
            {
                if (!string.IsNullOrEmpty(portViewModel.PortLocation))
                {
                    GlobalData.gbfrmInfoViewModel.Location = portViewModel.PortLocation;
                    var tploc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(portViewModel.PortLocation);
                    if (tploc != null)
                    {
                        if (tploc.Is_Occupied == 1)
                        {
                            var tpCarriersources = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(tploc.Address);
                            if (tpCarriersources.Count==0)
                            {
                                GlobalData.gbfrmInfoViewModel.CassetteID = "";
                                GlobalData.gbfrmInfoViewModel.InstallTime = "";
                                GlobalData.gbfrmInfoViewModel.IDStatus = "";
                            }
                            else
                            {
                                TpCarrier Sourcecst = tpCarriersources[0];
                                GlobalData.gbfrmInfoViewModel.CassetteID = Sourcecst.Id;
                                GlobalData.gbfrmInfoViewModel.InstallTime = Sourcecst.Install_Time.ToString();
                                if (Sourcecst.Id.Contains("UNK"))
                                {
                                    GlobalData.gbfrmInfoViewModel.IDStatus = "UNK";
                                }
                                else
                                {
                                    GlobalData.gbfrmInfoViewModel.IDStatus = "Normal";
                                }
                            }
                          
                          
                            
                        }

                        else
                        {
                            GlobalData.gbfrmInfoViewModel.CassetteID = "";
                            GlobalData.gbfrmInfoViewModel.InstallTime = "";
                            GlobalData.gbfrmInfoViewModel.IDStatus = "";
                        }

                        GlobalData.gbfrmInfoViewModel.Prohibit = tploc.Is_Prohibited.ToString();

                        if (tploc.Is_Prohibited == 1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Prohibited";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnProhibited";
                        }
                        if (tploc.Is_Occupied == 1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Occupied";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnOccupied";
                        }
                        if (tploc.Is_Reserved == 1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Reserved";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnReserved";
                        }
                        GlobalData.gbfrmInfoViewModel.Zone = tploc.Zone_Name;
                        GlobalData.gbfrmInfoViewModel.LocationType = tploc.Type;
                    }

                }



            }
        }

        public async void GlobalData_ShelfLeftDbClick(string shelflocation, N2Purge.ViewModel.ShelfViewModel viewModel)
        {
            if (!string.IsNullOrEmpty(shelflocation)&& viewModel != null) {
                if (!string.IsNullOrEmpty(viewModel.ShelfLocation))
                {
                    GlobalData.gbfrmInfoViewModel.Location = viewModel.ShelfLocation;
                    var tploc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(viewModel.ShelfLocation);
                    if (tploc!=null)
                    {
                        if (tploc.Is_Occupied==1)
                        {
                            var tpCarriersources = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(tploc.Address);
                            if (tpCarriersources.Count == 0)
                            {
                                GlobalData.gbfrmInfoViewModel.CassetteID = "";
                                GlobalData.gbfrmInfoViewModel.InstallTime = "";
                                GlobalData.gbfrmInfoViewModel.IDStatus = "";
                            }
                            else
                            {
                                TpCarrier Sourcecst = tpCarriersources[0];
                                GlobalData.gbfrmInfoViewModel.CassetteID = Sourcecst.Id;
                                GlobalData.gbfrmInfoViewModel.InstallTime = Sourcecst.Install_Time.ToString();
                                if (Sourcecst.Id.Contains("UNK"))
                                {
                                    GlobalData.gbfrmInfoViewModel.IDStatus = "UNK";
                                }
                                else
                                {
                                    GlobalData.gbfrmInfoViewModel.IDStatus = "Normal";
                                }
                            }
                          
                        }
                       
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.CassetteID = "";
                            GlobalData.gbfrmInfoViewModel.InstallTime = "";
                            GlobalData.gbfrmInfoViewModel.IDStatus = "";
                        }
                      
                        GlobalData.gbfrmInfoViewModel.Prohibit = tploc.Is_Prohibited.ToString();
                      
                        if (tploc.Is_Prohibited==1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Prohibited";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnProhibited";
                        }
                        if (tploc.Is_Occupied == 1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Occupied";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnOccupied";
                        }
                        if (tploc.Is_Reserved == 1)
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "Reserved";
                        }
                        else
                        {
                            GlobalData.gbfrmInfoViewModel.Status = "UnReserved";
                        }
                        GlobalData.gbfrmInfoViewModel.Zone=tploc.Zone_Name;
                        GlobalData.gbfrmInfoViewModel.LocationType=tploc.Type;
                    }

                }
               


            }
           
        }
    }
}
