﻿using DBEntity;
using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Proj.WCF;
using Proj.Log;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmAlarmMonitor.xaml 的交互逻辑
    /// </summary>
    
    public partial class frmAlarmMonitor : UserControl
    {
        private ObservableCollection<TpalarmViewModel> tpAlarms = new ObservableCollection<TpalarmViewModel>();
        private DispatcherTimer timer;

        public frmAlarmMonitor()
        {
            InitializeComponent();
            InitializeTimer();
            RefreshAlarm();
            dgv.ItemsSource = tpAlarms;
        }

        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5); // 设置刷新间隔为5秒
            timer.Tick += new EventHandler(Timer_Tick!);
            timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            try
            {
                RefreshAlarm();
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog(ex.Message+Environment.NewLine+ex.StackTrace);
            }
        }

        public async void RefreshAlarm()
        {
            var alarmlist = await GlobalData.dbHelper.tpAlarmdb.GetAllTpAlarmAsync();
            if (alarmlist.Count == 0)
            {
                tpAlarms.Clear();
            }
            else
            {
                foreach (var alarm in alarmlist)
                {   
                    var alarmVm = new TpalarmViewModel
                    {
                        AlarmCode = alarm.Code,
                        AlarmUnit = alarm.Unit,
                        StartTime = (DateTime)alarm.Start_Time,
                        LastTime = (DateTime)alarm.Last_Time,
                        Comment = alarm.Comment
                    };

                    if (tpAlarms.Any(x => x.AlarmCode == alarm.Code && x.AlarmUnit == alarm.Unit))
                    {
                        continue;
                    }
                    tpAlarms.Add(alarmVm);
                    if (!cmb.Items.Contains(alarm.Unit))
                    {
                        cmb.Items.Add(alarm.Unit);
                    }
                }
                var craneList = await GlobalData.dbHelper.tpCranedb.GetAllTpCraneAsync();
                foreach (TpCrane crane in craneList)
                {
                    if (!cmb.Items.Contains(crane.Name))
                    {
                        cmb.Items.Add(crane.Name);
                    }
                }
            }
        }
           
        

        private void UserControl_Unloaded(object sender, RoutedEventArgs e)
        {
            if (timer != null)
            {
                timer.Stop();
                timer = null;
            }
        }

        private async void btnclear_Click(object sender, RoutedEventArgs e)
        {
            //if (!Permission.CheckPermission)
            //{
            //    return;
            //}
            if (cmb.Text.Length == 0)
            {
                MessageBox.Show("Please select the unit.");
                return;
            }
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("UNIT", cmb.Text);
            bool bResult = false;
            object objResult =await WCFClient.Instance.SendMessage("ClearUnitAlarm", dicParams);
            if (objResult != null)
            {
                bResult = Convert.ToBoolean(objResult);
            }
            if (bResult)
            {
                MessageBox.Show("Clear the alarm of '" + cmb.Text + "' successfully.");
            }
            else
            {
                MessageBox.Show("Clear the alarm of '" + cmb.Text + "' failed.");
            }
        }

        private async void btnsolution_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (dgv.SelectedItems.Count == 0)
                {
                    MessageBox.Show("Please select one row.");
                    return;
                }
                var dt = dgv.SelectedItem as TpalarmViewModel;
                var  configs =await GlobalData.dbHelper.tpAlarmConfig.GetAllTpAlarmConfigAsync();
                var config=configs.Where(t=>t.Code==dt.AlarmCode).ToList()[0];
                if (config != null)
                {
                    MessageBox.Show(config.Solution);
                }
                else
                {
                    MessageBox.Show("Can not find in AlarmConfig.");
                }
            }
            catch (Exception ex)
            {
                //Proj.Log.Logger.Instance.ExceptionLog("btnSolution_Click: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

       
    }
}
