using System;
using System.Windows.Media;
using N2Purge.ViewModel;

namespace N2Purge.ViewModel
{
    public class CmdStatusViewModel : VmPropertyChange
    {
        private int _status;
        private string _displayText;
        private SolidColorBrush _backgroundColor;

        public int Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    UpdateBackgroundColor();
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        public string DisplayText
        {
            get => _displayText;
            set
            {
                if (_displayText != value)
                {
                    _displayText = value;
                    OnPropertyChanged(nameof(DisplayText));
                }
            }
        }

        public SolidColorBrush BackgroundColor
        {
            get => _backgroundColor;
            private set
            {
                if (_backgroundColor != value)
                {
                    _backgroundColor = value;
                    OnPropertyChanged(nameof(BackgroundColor));
                }
            }
        }

        private void UpdateBackgroundColor()
        {
            BackgroundColor = Status switch
            {
                0 => new SolidColorBrush(Colors.Gray),      // 默认状态
                1 => new SolidColorBrush(Colors.Green),     // 成功状态
                2 => new SolidColorBrush(Colors.Red),       // 错误状态
                3 => new SolidColorBrush(Colors.Yellow),    // 警告状态
                _ => new SolidColorBrush(Colors.Gray)       // 未知状态
            };
        }

        public CmdStatusViewModel()
        {
            _status = 0;
            _displayText = string.Empty;
            _backgroundColor = new SolidColorBrush(Colors.Gray);
        }
    }
}