<UserControl x:Class="N2Purge.userControls.CmdStatus"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="100">
    <UserControl.Resources>
        <Style x:Key="StatusStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="Gray"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
    </UserControl.Resources>
    <Button Cursor="Hand">
        <Button.Template>
            <ControlTemplate TargetType="Button">
                <Border Style="{StaticResource StatusStyle}" 
                        CornerRadius="2"
                        Background="{Binding BackgroundColor}">
                    <TextBlock Text="{Binding DisplayText}" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             Foreground="White"
                             FontSize="12"
                             TextWrapping="Wrap"
                             Margin="5"/>
                </Border>
            </ControlTemplate>
        </Button.Template>
    </Button>
</UserControl>