﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.silan
{
    /// <summary>
    /// Shelf.xaml 的交互逻辑
    /// </summary>
    public partial class Shelf : UserControl
    {
        public Shelf()
        {
            InitializeComponent();
        }
        public event EventHandler<ShelfViewModel> ShelfLeftClick;//鼠标左键点击一下
        public  event EventHandler<ShelfViewModel> shelfDbClick;//鼠标左键点击2下
        private void UserControl_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
           var vm=this.DataContext as ShelfViewModel;
            if (vm != null) 
            {
                shelfDbClick?.Invoke(this,vm);
            }
        }

        private void UserControl_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                ShelfLeftClick?.Invoke(this, vm);
            }
        }
    }
}
