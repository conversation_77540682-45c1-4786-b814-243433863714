﻿<Window x:Class="N2Purge.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:N2Purge"
        xmlns:localControls="clr-namespace:N2Purge.userControls"
        xmlns:userctr="clr-namespace:N2Purge.userControls"
        xmlns:ctr="clr-namespace:N2Purge.frmUserControl"
    mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="800" Loaded="Window_Loaded" Closed="Window_Closed" KeyDown="MainWindow_KeyDown">
    <Window.Resources>
        <!-- 引用资源字典 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="StyleDictionary.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <Style x:Key="DefaultButton" TargetType="Button">
                <Setter Property="Background" Value="Snow"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="bd" CornerRadius="5" 
                                    Background="{TemplateBinding Background}" 
                                    TextBlock.TextAlignment="Center" 
                                    BorderThickness="1" 
                                    BorderBrush="Black">
                                <ContentControl Content="{TemplateBinding Content}" 
                                              HorizontalAlignment="Center"  
                                              VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Background" Value="LightGray" TargetName="bd"/>
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="Green" TargetName="bd"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>


            <!--</Style>-->
        </ResourceDictionary>
    </Window.Resources>
    <Grid>
        <!--<localControls:Shelf x:Name="shelf" Height="80" Width="120"/>-->
        <Grid.RowDefinitions>
            <RowDefinition Height="100"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="100"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>

            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="100"/>
        </Grid.ColumnDefinitions>
        <Border BorderBrush="Black" BorderThickness="1" Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="0" Margin="5">
            <localControls:TopScInfo Margin="0"/>
        </Border>
        <!--<TreeView x:Name="tv" Grid.Row="1" Grid.Column="0" SelectedItemChanged="tv1_SelectedItemChanged" Grid.RowSpan="2">
            <TreeViewItem Header="Control">
                <TreeViewItem Header="Main Control">
                    -->
        <!--<TreeViewItem Header="柱状图"/>-->
        <!--
                </TreeViewItem>

              
            </TreeViewItem>

            -->
        <!-- 列表控件 -->
        <!--
            <TreeViewItem Header="Monitor">
                <TreeViewItem Header="Alarm Monitor">
                    
                </TreeViewItem>
                <TreeViewItem Header="Report"/>
                <TreeViewItem Header="ReportQuery"/>
                <TreeViewItem Header="Servo"/>
            </TreeViewItem>
            <TreeViewItem Header="System Setting">
                <TreeViewItem Header="Department Setting"/>
                <TreeViewItem Header="User Setting"/>
                <TreeViewItem Header="Module Setting"/>
             
            </TreeViewItem>
            <TreeViewItem Header="Maintance">
                <TreeViewItem Header="Carrier"/>
            </TreeViewItem>
            <TreeViewItem Header="EQP Settings">
                <TreeViewItem Header="Alarm Settings"/>
            </TreeViewItem>
            <TreeViewItem Header="History">
                <TreeViewItem Header="EQP Event History"/>
                <TreeViewItem Header="Alarm History"/>
                <TreeViewItem Header="Transfer History"/>
                <TreeViewItem Header="PIO Chat"/>
                <TreeViewItem Header="PIO History"/>
            </TreeViewItem>
            <TreeViewItem Header="Test">
                <TreeViewItem Header="Cycle"/>
            </TreeViewItem>
        </TreeView>-->
        <Border BorderBrush="Black" BorderThickness="1" Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="2" Margin="5" >

            <!--<localControls:ButtomInfo/>-->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>

                </Grid.ColumnDefinitions>
                <Button Height="80" x:Name="btnmain" Width="120" VerticalContentAlignment="Center" TextBlock.FontSize="20" Grid.Column="0" Content="Main" Style="{StaticResource DefaultButton}" Tag="d:/N2Purge/N2 Purge/N2 Purge/Images/main.png" Click="Button_Click"/>
                <Button x:Name="btnMonitor" VerticalContentAlignment="Center" TextBlock.FontSize="20" Height="80" Width="120" Grid.Column="1" Content="Monitor" Style="{StaticResource DefaultButton}" Tag="Images/Carrier.png" Click="btnMonitor_Click"/>
                <Button x:Name="btnSetting" TextBlock.FontSize="20" Height="80" Width="120" Grid.Column="2" Content="Setting" Style="{StaticResource DefaultButton}" Tag="Images/Alarm.png" Click="btnSetting_Click"/>
                <Button x:Name="btnIO" TextBlock.FontSize="20" Height="80" Width="120" Grid.Column="3" Content="IO" Style="{StaticResource DefaultButton}" Tag="Images/Alarm.png" Click="btnIO_Click"/>
                <Button x:Name="btnHistory" TextBlock.FontSize="20" Height="80" Width="120" Grid.Column="4" Content="History" Style="{StaticResource DefaultButton}" Tag="Images/IO.png" Click="btnHistory_Click"/>
                <Button x:Name="btnUser" TextBlock.FontSize="20" Height="80" Width="120" Grid.Column="5" Content="User" Style="{StaticResource DefaultButton}" Tag="Images/IO.png" Click="btnUser_Click"/>
                <Button x:Name="btnAutoTask" TextBlock.FontSize="16" Height="80" Width="120" Grid.Column="6" Content="AutoTask" Style="{StaticResource DefaultButton}" Tag="Images/Alarm.png" Click="btnAutoTask_Click"/>
            </Grid>
        </Border>
        <ContentControl x:Name="bd" BorderThickness="1" Grid.Column="0" Grid.Row="1" Margin="10" Grid.ColumnSpan="2">
            <!--<ctr:frmMain/>-->
        </ContentControl>
        <!--<userctr:PurgeShelfInfo Grid.Column="2" Grid.Row="1"/>-->
        <!--<Border Grid.Column="1" Grid.Row="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Label Content="FoupID" Grid.Row="0" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                <TextBox x:Name="shelftxt" Text="11" Grid.Row="1" TextAlignment="Center" Height="50" VerticalContentAlignment="Center"/>
                <Button x:Name="btnsearch" Content="查询" Grid.Row="2" Margin="
                        2" Background="Snow" PreviewMouseDown="btnsearch_MouseDown" Click="btnsearch_Click" MouseLeave="btnsearch_MouseMove"/> 
                <Border x:Name="bdbtn" Grid.Row="3" BorderThickness="1" BorderBrush="Black">
                </Border>
            </Grid>
        </Border>-->
    </Grid>

</Window>
