﻿using N2Purge.ViewModel;
using N2Purge;
using N2Purge.userControls;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.userControls
{
    /// <summary>
    /// Shelf.xaml 的交互逻辑
    /// </summary>
    public partial class Shelf : UserControl
    {
        public Shelf()
        {
            InitializeComponent();
        }
        private Dictionary<string, WinShelfInfo>  dicwin=new Dictionary<string, WinShelfInfo>();
        private Dictionary<string, WinShelf> dicwinshelf = new Dictionary<string, WinShelf>();
        private void UserControl_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            var viewModel = this.DataContext as ShelfViewModel;
            if (viewModel != null)
            {
                if (viewModel.IsPurge)
                {
                    if (!dicwin.ContainsKey(viewModel.ShelfLocation))
                    {
                        WinShelfInfo cur = new WinShelfInfo();
                        cur.Width = 600;
                        cur.Height = 500;
                        cur.Closing += (s, args) =>
                        {
                            args.Cancel = true; // 取消关闭事件
                            cur.Visibility = Visibility.Hidden; // 隐藏窗口而不是关闭
                        };
                        dicwin.Add(viewModel.ShelfLocation, cur);
                    }

                    if (GlobalData.gbpurgevm[viewModel.ShelfLocation] != null)
                    {
                        var window = dicwin[viewModel.ShelfLocation];
                        window.DataContext = GlobalData.gbpurgevm[viewModel.ShelfLocation];
                        window.Visibility = Visibility.Visible; // 显示窗口
                    }
                }
            }
        }

        private void UserControl_MouseEnter(object sender, MouseEventArgs e)
        {
            var viewModel = this.DataContext as ShelfViewModel;
            if (viewModel != null)
            {
                if (!dicwinshelf.ContainsKey(viewModel.ShelfLocation))
                {
                    WinShelf cur = new WinShelf();
                    cur.Width = 300;
                    cur.Height = 200;
                    cur.Closing += (s, args) =>
                    {
                        args.Cancel = true; // 取消关闭事件
                        cur.Visibility = Visibility.Hidden; // 隐藏窗口而不是关闭
                    };
                    dicwinshelf.Add(viewModel.ShelfLocation, cur);
                }
              
                if (GlobalData.gbshelfinfovm[viewModel.ShelfLocation] != null)
                {
                    var window = dicwinshelf[viewModel.ShelfLocation];
                    window.DataContext = GlobalData.gbshelfinfovm[viewModel.ShelfLocation];
                    window.Visibility = Visibility.Visible; // 显示窗口
                }
            }
        }

        private void UserControl_MouseLeave(object sender, MouseEventArgs e)
        {
            var viewModel = this.DataContext as ShelfViewModel;
            if (viewModel != null)
            {

                if (GlobalData.gbshelfinfovm[viewModel.ShelfLocation] != null)
                {
                    var window = dicwinshelf[viewModel.ShelfLocation];
                    window.DataContext = GlobalData.gbshelfinfovm[viewModel.ShelfLocation];
                    window.Visibility = Visibility.Collapsed; // 显示窗口
                }
            }

        }
        public event EventHandler<ShelfEventArgs> ShelfleftClicked;
        private void UserControl_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var viewModel = this.DataContext as ShelfViewModel;
            ShelfleftClicked?.Invoke(this, new ShelfEventArgs(viewModel.ShelfLocation));
        }
    }
}
