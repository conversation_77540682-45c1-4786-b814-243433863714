<UserControl x:Class="N2Purge.frmUserControl.frmAutoTaskManager"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2E3440" Padding="15">
            <TextBlock Text="自动任务管理" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- 状态信息 -->
        <Border Grid.Row="1" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock x:Name="txtTaskStatus" Text="检查中..." FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="txtTaskDetails" Text="正在检查计划任务状态..." FontSize="12" Foreground="#6C757D"/>
                    <TextBlock x:Name="txtSingletonStatus" Text="单例状态: 检查中..." FontSize="12" Foreground="#6C757D" Margin="0,5,0,0"/>
                    <TextBlock x:Name="txtPermissionWarning" Text="" FontSize="12" Foreground="#DC3545" Margin="0,5,0,0"
                               TextWrapping="Wrap" Visibility="Collapsed"/>
                </StackPanel>
                
                <Button x:Name="btnRefresh" Grid.Column="1" Content="刷新状态" Width="100" Height="35" 
                        Background="#007BFF" Foreground="White" BorderThickness="0" 
                        Click="BtnRefresh_Click" Margin="10,0,0,0"/>
            </Grid>
        </Border>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="15">
            <StackPanel>
                <!-- 计划任务管理 -->
                <GroupBox Header="计划任务管理" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <TextBlock Text="自动监控任务设置" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock Text="程序会自动检查并创建每5分钟执行一次的计划任务，确保系统持续监控。" 
                                   TextWrapping="Wrap" Margin="0,0,0,15" Foreground="#6C757D"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock x:Name="txtTaskInfo" Grid.Column="0" Text="任务信息加载中..." 
                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <Button x:Name="btnCreateTask" Grid.Column="1" Content="创建任务" Width="80" Height="30" 
                                    Background="#28A745" Foreground="White" BorderThickness="0" 
                                    Click="BtnCreateTask_Click" Margin="5,0"/>
                            
                            <Button x:Name="btnDeleteTask" Grid.Column="2" Content="删除任务" Width="80" Height="30" 
                                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                                    Click="BtnDeleteTask_Click" Margin="5,0"/>
                            
                            <Button x:Name="btnOpenTaskScheduler" Grid.Column="3" Content="打开任务计划程序" Width="120" Height="30" 
                                    Background="#17A2B8" Foreground="White" BorderThickness="0" 
                                    Click="BtnOpenTaskScheduler_Click" Margin="5,0"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 单例管理 -->
                <GroupBox Header="单例管理" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <TextBlock Text="应用程序实例控制" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock Text="确保系统中只有一个N2Purge实例运行，避免资源冲突。" 
                                   TextWrapping="Wrap" Margin="0,0,0,15" Foreground="#6C757D"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock x:Name="txtInstanceInfo" Grid.Column="0" Text="实例信息加载中..." 
                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <Button x:Name="btnCheckInstances" Grid.Column="1" Content="检查实例" Width="80" Height="30" 
                                    Background="#FFC107" Foreground="Black" BorderThickness="0" 
                                    Click="BtnCheckInstances_Click" Margin="5,0"/>
                            
                            <Button x:Name="btnKillOtherInstances" Grid.Column="2" Content="终止其他实例" Width="100" Height="30" 
                                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                                    Click="BtnKillOtherInstances_Click" Margin="5,0"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 系统信息 -->
                <GroupBox Header="系统信息" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="应用程序路径:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="txtAppPath" Grid.Row="0" Grid.Column="1" Text="加载中..." Margin="10,5" TextWrapping="Wrap"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="当前用户:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="txtCurrentUser" Grid.Row="1" Grid.Column="1" Text="加载中..." Margin="10,5"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="管理员权限:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="txtAdminStatus" Grid.Row="2" Grid.Column="1" Text="检查中..." Margin="10,5"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="启动时间:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="txtStartTime" Grid.Row="3" Grid.Column="1" Text="加载中..." Margin="10,5"/>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="运行时长:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="txtRunTime" Grid.Row="4" Grid.Column="1" Text="计算中..." Margin="10,5"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 操作日志 -->
                <GroupBox Header="操作日志" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="200"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Text="最近的操作记录" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" 
                                      Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1">
                            <TextBlock x:Name="txtOperationLog" Text="暂无操作记录" Padding="10" FontFamily="Consolas" 
                                       FontSize="11" TextWrapping="Wrap"/>
                        </ScrollViewer>
                        
                        <Button x:Name="btnClearLog" Grid.Row="2" Content="清除日志" Width="80" Height="25" 
                                HorizontalAlignment="Right" Margin="0,10,0,0" 
                                Background="#6C757D" Foreground="White" BorderThickness="0" 
                                Click="BtnClearLog_Click"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部状态栏 -->
        <Border Grid.Row="3" Background="#E9ECEF" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="txtStatusBar" Grid.Column="0" Text="就绪" VerticalAlignment="Center" FontSize="12"/>
                <TextBlock x:Name="txtLastUpdate" Grid.Column="1" Text="最后更新: 从未" VerticalAlignment="Center" FontSize="12" Foreground="#6C757D"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
