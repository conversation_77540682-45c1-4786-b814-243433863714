﻿<UserControl x:Class="N2Purge.userControls.Shelf"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="80" 
             MouseDoubleClick="UserControl_MouseDoubleClick" 
             MouseEnter="UserControl_MouseEnter" 
             MouseLeave="UserControl_MouseLeave" 
             MouseLeftButtonDown="UserControl_MouseLeftButtonDown">
    <Border BorderBrush="Black" BorderThickness="2"> 
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 点位ID显示 -->
            <TextBlock Grid.Row="0" 
                       VerticalAlignment="Center" 
                       HorizontalAlignment="Center" 
                       Text="{Binding ShelfLocation}" 
                       FontSize="10" 
                       FontWeight="Bold"
                       Foreground="Black"
                       Background="White"
                       Margin="2"/>
            
            <!-- 状态显示区域 -->
            <Grid Grid.Row="1">
                <Rectangle Fill="{Binding ShelfColorBrush}"
                           Stroke="Black"
                           StrokeThickness="1"/>

                <!-- 货物ID显示 -->
                <TextBlock VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Text="{Binding FoupDisplayText}"
                           FontSize="9"
                           FontWeight="Bold"
                           Foreground="Red"
                           Background="Transparent"
                           Margin="2">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <!-- 只有在有货物ID时才显示 -->
                                <DataTrigger Binding="{Binding FoupDisplayText}" Value="">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </Grid>
            
            <!-- 状态文本显示 -->
            <TextBlock Grid.Row="2" 
                       VerticalAlignment="Center" 
                       HorizontalAlignment="Center" 
                       Text="{Binding StatusText}"
                       FontSize="8" 
                       FontWeight="Bold"
                       Foreground="Black"
                       Background="White"
                       Margin="2"/>
        </Grid>
    </Border>
</UserControl>
