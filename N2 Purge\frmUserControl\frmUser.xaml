﻿<UserControl x:Class="N2Purge.frmUserControl.frmUser"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="500">
    <Grid HorizontalAlignment="Left" VerticalAlignment="Center" Width="500" Height="500" Margin="0,0,0,0">
        <Border Background="White" CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect BlurRadius="20" Opacity="0.5"/>
            </Border.Effect>
        </Border>
       
        <Grid VerticalAlignment="Center" HorizontalAlignment="Center">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="110"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="0" Text="当前用户：" />
            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="LblCurUserName" />

            <TextBlock Grid.Row="1" Grid.Column="0" Text="当前等级：" />
            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="LblCurLevel" />

            <TextBlock Grid.Row="2" Grid.Column="0" Text="用户：" />
            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cboUser"
                      TextBlock.TextAlignment="Center"
                      FontSize="18"
                      Margin="5"
                      VerticalContentAlignment="Center"
                      IsEditable="False"
                      MaxDropDownHeight="200"/>

            <TextBlock Grid.Row="3" Grid.Column="0" Text="用户密码：" />
            <PasswordBox Grid.Row="3" Grid.Column="1" x:Name="pwdUserPassword"
                         Margin="5"
                         VerticalContentAlignment="Center"
                         FontSize="18"
                         PasswordChar="●"/>

            <TextBlock Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" x:Name="lblTip" Text="*" VerticalAlignment="Center" 
                         TextBlock.Foreground="Red" FontSize="18" Width="100" HorizontalAlignment="Center"/>

            <Button Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" x:Name="btnLoggin" Content="登录"  Height="40" Background="Gray"  
                      FontSize="18"  Foreground="White" Click="btnLoggin_Click"/>
          
        </Grid>

    </Grid>

</UserControl>
