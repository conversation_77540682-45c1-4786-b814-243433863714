using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace N2Purge.ViewModel
{
    public class StatisticsViewModel : INotifyPropertyChanged
    {
        private DateTime _startTime = DateTime.Now.AddDays(-1);
        private DateTime _endTime = DateTime.Now;
        private DateTime _selectedDate = DateTime.Now;
        private int _transportCount;
        private int _failureCount;
        private int _alarmCount;
        private string _queryTime;
        private string _alarmDuration;
        private int _mcbf;
        private int _mcba;
        private int _mtbf;
        private int _mttr;
        private string _upTime;
        private string _utilizationRate;

        public DateTime StartTime
        {
            get => _startTime;
            set { _startTime = value; OnPropertyChanged(); }
        }

        public DateTime EndTime
        {
            get => _endTime;
            set { _endTime = value; OnPropertyChanged(); }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set { _selectedDate = value; OnPropertyChanged(); }
        }

        public int TransportCount
        {
            get => _transportCount;
            set { _transportCount = value; OnPropertyChanged(); }
        }

        public int FailureCount
        {
            get => _failureCount;
            set { _failureCount = value; OnPropertyChanged(); }
        }

        public int AlarmCount
        {
            get => _alarmCount;
            set { _alarmCount = value; OnPropertyChanged(); }
        }

        public string QueryTime
        {
            get => _queryTime;
            set { _queryTime = value; OnPropertyChanged(); }
        }

        public string AlarmDuration
        {
            get => _alarmDuration;
            set { _alarmDuration = value; OnPropertyChanged(); }
        }

        public int MCBF
        {
            get => _mcbf;
            set { _mcbf = value; OnPropertyChanged(); }
        }

        public int MCBA
        {
            get => _mcba;
            set { _mcba = value; OnPropertyChanged(); }
        }

        public int MTBF
        {
            get => _mtbf;
            set { _mtbf = value; OnPropertyChanged(); }
        }

        public int MTTR
        {
            get => _mttr;
            set { _mttr = value; OnPropertyChanged(); }
        }

        public string UpTime
        {
            get => _upTime;
            set { _upTime = value; OnPropertyChanged(); }
        }

        public string UtilizationRate
        {
            get => _utilizationRate;
            set { _utilizationRate = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}