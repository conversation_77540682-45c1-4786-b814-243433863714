﻿using DBEntity;
using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.silan
{
    /// <summary>
    /// Port.xaml 的交互逻辑
    /// </summary>
    public partial class Port : UserControl
    {
        public object ViewModel
        {
            get { return DataContext; }
            set { DataContext = value; }
        }

        public Port()
        {
            InitializeComponent();
        }
        public event EventHandler<PortViewModel> PortLeftClick;
        public event EventHandler<PortViewModel> PortLeftDbClick;
        public event EventHandler<PortViewModel> PortRightClick;
        private void Border_MouseDown(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            if (border != null)
            {
                var data = bdlp.DataContext as PortViewModel;
                if (e.ClickCount == 1)
                {
                    PortLeftClick?.Invoke(this, data);
                }
            else if (e.ClickCount == 2) { 
                    PortLeftDbClick?.Invoke(this, data); };
               // MessageBox.Show(data?.ToString() ?? "No data");
            }
        }

        private void Border1_MouseDown(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            if (border != null)
            {
                var data = bdbd.DataContext as PortViewModel;
                if (e.ClickCount == 1)
                {
                    PortLeftClick?.Invoke(this, data);
                }
                else if (e.ClickCount == 2)
                {
                    PortLeftDbClick?.Invoke(this, data);
                };
                //  MessageBox.Show(data?.ToString() ?? "No data");
            }
        }

        private void Border2_MouseDown(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            if (border != null)
            {
                var data = bdop.DataContext as PortViewModel;
                if (e.ClickCount == 1)
                {
                    PortLeftClick?.Invoke(this, data);
                }
                else if (e.ClickCount == 2)
                {
                    PortLeftDbClick?.Invoke(this, data);
                };
                // MessageBox.Show(data?.ToString() ?? "No data");
            }
        }

      


        private void MenuItemLpIM_Click(object sender, RoutedEventArgs e)
        {
            var data = bdlp.DataContext;
        }

        private void MenuItemLpOM_Click(object sender, RoutedEventArgs e)
        {

        }

        private void LpPI_Click(object sender, RoutedEventArgs e)
        {

        }

        private void LpTc_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var contextMenu = menuItem.Parent as ContextMenu;
            var border = contextMenu.PlacementTarget as Border;
            var viewmodel = border.DataContext as PortViewModel;
            GlobalData.PortMouseRightClick("Transfer", viewmodel);
        }

        private void LpIc_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var contextMenu = menuItem.Parent as ContextMenu;
            var border = contextMenu.PlacementTarget as Border;
            var viewmodel = border.DataContext as PortViewModel;
            GlobalData.PortMouseRightClick("Install", viewmodel);
        }

        private void LpRc_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var contextMenu = menuItem.Parent as ContextMenu;
            var border = contextMenu.PlacementTarget as Border;
            var viewmodel = border.DataContext as PortViewModel;
            GlobalData.PortMouseRightClick("Remove", viewmodel);
        }

        private async void LpEnable_Click(object sender, RoutedEventArgs e)
        {
            //if (bdlp.DataContext is PortViewModel viewModel)
            //{
            //    viewModel.IsEnabled = true;
            //}
            try
            {
                var strLocation = (this.DataContext as gbPortViewModel).Lp.PortLocation.Substring(0, 5);
                if (string.IsNullOrEmpty(strLocation))
                {
                    return ;
                }
                TpLocation loc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                if (loc == null)
                {
                    MessageBox.Show("Can not find the location.");
                    return ;
                }
                string msgName = "SetLocationEnabled";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", loc.Address);
                dicParams.Add("ENABLED", 1);

                object obj =await Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }
                
            }
            catch (Exception ex)
            {
              
            }

        }

        private async void LpDisable_Click(object sender, RoutedEventArgs e)
        {
            //if (bdlp.DataContext is PortViewModel viewModel)
            //{
            //    viewModel.IsEnabled = false;
            //}
            try
            {
                var strLocation = (this.DataContext as gbPortViewModel).Lp.PortLocation.Substring(0, 5);
                if (string.IsNullOrEmpty(strLocation))
                {
                    return;
                }
                TpLocation loc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                if (loc == null)
                {
                    MessageBox.Show("Can not find the location.");
                    return ;
                }
                string msgName = "SetLocationEnabled";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", loc.Address);
                dicParams.Add("ENABLED", 0);

                object obj =await Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    //MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }
                
            }
            catch (Exception ex)
            {
             
            }
        }

        private void BdEnable_Click(object sender, RoutedEventArgs e)
        {
            if (bdbd.DataContext is PortViewModel viewModel)
            {
                viewModel.IsEnabled = true;
            }
        }

        private void BdDisable_Click(object sender, RoutedEventArgs e)
        {
            if (bdbd.DataContext is PortViewModel viewModel)
            {
                viewModel.IsEnabled = false;
            }
        }

        private void OpEnable_Click(object sender, RoutedEventArgs e)
        {
            if (bdop.DataContext is PortViewModel viewModel)
            {
                viewModel.IsEnabled = true;
            }
        }

        private void OpDisable_Click(object sender, RoutedEventArgs e)
        {
            if (bdop.DataContext is PortViewModel viewModel)
            {
                viewModel.IsEnabled = false;
            }
        }

        private void LpSps_Click(object sender, RoutedEventArgs e)
        {

        }

        private void LpIo_Click(object sender, RoutedEventArgs e)
        {

        }

      

        private async void LpCr_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var menuItem = sender as MenuItem;
                var contextMenu = menuItem.Parent as ContextMenu;
                var border = contextMenu.PlacementTarget as Border;
                var viewmodel = border.DataContext as PortViewModel;
                string strLocation = viewmodel.PortLocation;
                if (string.IsNullOrEmpty(strLocation))
                {
                    return ;
                }
                TpLocation loc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                if (loc == null)
                {
                    MessageBox.Show("Can not find the location.");
                    return ;
                }
                string msgName = "ClearReserved";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", loc.Address);

                object obj =await Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    //MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }
               
            }
            catch (Exception ex)
            {
               
            }
        }

        private void BdIm_Click(object sender, RoutedEventArgs e)
        {

        }

        private void MenuItem_Click(object sender, RoutedEventArgs e)
        {
            //切换模式
            var strPortID=(this.DataContext as gbPortViewModel).Lp.PortLocation.Substring(0,5);
            if (string.IsNullOrEmpty(strPortID))
            {
                MessageBox.Show("The port was not found!");
                return;
            }

            MessageBoxResult dr = MessageBox.Show("The port will be changed to input type !", "Tips", MessageBoxButton.OKCancel);
            if (dr != MessageBoxResult.OK)
            {
                return ;
            }
            string msgName = string.Empty;
            Dictionary<string, object> dicParams = new Dictionary<string, object>();

            //msgName = "PORTTYPECHG";//PORTTYPECHANGE
            msgName = "PORTTYPECHANGE";//PORTTYPECHANGE by dz
            dicParams.Add("PORTID", strPortID);
            dicParams.Add("PORTINOUTTYPE", 1);//PORTINOUTTYPE by dz

            object obj = Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
            bool res = true;
            if (obj == null || !bool.TryParse(obj.ToString(), out res))
            {
                res = false;
            }

            if (res)
            {
                MessageBox.Show("Submit successfully!");
            }
            else
            {
                MessageBox.Show("Submit failure!");
            }

        }

        private void BdPI_Click(object sender, RoutedEventArgs e)
        {

        }

        private void BdTc_Click(object sender, RoutedEventArgs e)
        {

        }

        private void BdIC_Click(object sender, RoutedEventArgs e)
        {

        }

        private void BdRc_Click(object sender, RoutedEventArgs e)
        {

        }

        //private void BdEnable_Click(object sender, RoutedEventArgs e)
        //{

        //}

        //private void BdDisable_Click(object sender, RoutedEventArgs e)
        //{

        //}

      
        private void bdlp_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            if (border != null)
            {
                var data = bdop.DataContext as PortViewModel;
                PortLeftDbClick?.Invoke(this, data);
                // MessageBox.Show(data?.ToString() ?? "No data");
            }
        }

        private void LpOm_Click(object sender, RoutedEventArgs e)
        {
            //Out Mode
            var strPortID = (this.DataContext as gbPortViewModel).Lp.PortLocation.Substring(0, 5);
            if (string.IsNullOrEmpty(strPortID))
            {
                MessageBox.Show("The port was not found!");
                return ;
            }

            MessageBoxResult dr = MessageBox.Show("The port will be changed to output type !", "Tips", MessageBoxButton.OKCancel);
            if (dr != MessageBoxResult.OK)
            {
                return ;
            }

            string msgName = string.Empty;
            Dictionary<string, object> dicParams = new Dictionary<string, object>();

            msgName = "PORTTYPECHANGE";
            dicParams.Add("PORTID", strPortID);
            //dicParams.Add("PORTTYPE", 2);//by dz
            dicParams.Add("PORTINOUTTYPE", 2);//by dz

            object obj = Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
            bool res = true;
            if (obj == null || !bool.TryParse(obj.ToString(), out res))
            {
                res = false;
            }

            if (res)
            {
                MessageBox.Show("Submit successfully!");
            }
            else
            {
                MessageBox.Show("Submit failure!");
            }

        }
    }
}
