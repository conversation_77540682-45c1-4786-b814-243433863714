using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Diagnostics;
using Proj.Log;
using Timer = System.Windows.Forms.Timer;

namespace N2Purge.Services
{
    /// <summary>
    /// 简化的录制回放服务
    /// </summary>
    public class SimpleRecordReplayService
    {
        #region Windows API 声明

        // 钩子类型
        private const int WH_MOUSE_LL = 14;
        private const int WH_KEYBOARD_LL = 13;

        // 鼠标消息
        private const int WM_LBUTTONDOWN = 0x0201;
        private const int WM_LBUTTONUP = 0x0202;
        private const int WM_RBUTTONDOWN = 0x0204;
        private const int WM_RBUTTONUP = 0x0205;
        private const int WM_MOUSEMOVE = 0x0200;
        private const int WM_MOUSEWHEEL = 0x020A;

        // 键盘消息
        private const int WM_KEYDOWN = 0x0100;
        private const int WM_KEYUP = 0x0101;
        private const int WM_SYSKEYDOWN = 0x0104;
        private const int WM_SYSKEYUP = 0x0105;

        // 钩子委托
        private delegate IntPtr LowLevelProc(int nCode, IntPtr wParam, IntPtr lParam);

        // Windows API 函数
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int x;
            public int y;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MSLLHOOKSTRUCT
        {
            public POINT pt;
            public uint mouseData;
            public uint flags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct KBDLLHOOKSTRUCT
        {
            public uint vkCode;
            public uint scanCode;
            public uint flags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        #endregion
        #region 字段和属性

        private bool _isRecording = false;
        private bool _isPlaying = false;
        private List<InputAction> _recordedActions = new List<InputAction>();
        private DateTime _recordingStartTime;
        private string _recordingDirectory;
        private System.Timers.Timer _playbackTimer;
        private int _playbackIndex = 0;
        private List<InputAction> _playbackActions = new List<InputAction>();

        // 钩子相关
        private IntPtr _mouseHook = IntPtr.Zero;
        private IntPtr _keyboardHook = IntPtr.Zero;
        private LowLevelProc _mouseProc;
        private LowLevelProc _keyboardProc;

        /// <summary>
        /// 是否正在录制
        /// </summary>
        public bool IsRecording => _isRecording;

        /// <summary>
        /// 是否正在回放
        /// </summary>
        public bool IsPlaying => _isPlaying;

        #endregion

        #region 事件

        /// <summary>
        /// 录制状态改变事件
        /// </summary>
        public event EventHandler<string> RecordingStateChanged;

        /// <summary>
        /// 回放状态改变事件
        /// </summary>
        public event EventHandler<string> PlaybackStateChanged;

        #endregion

        #region 构造函数

        public SimpleRecordReplayService()
        {
            _recordingDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "RecordReplay");
            if (!Directory.Exists(_recordingDirectory))
            {
                Directory.CreateDirectory(_recordingDirectory);
            }

            _playbackTimer = new System.Timers.Timer();
            _playbackTimer.Elapsed += PlaybackTimer_Elapsed;

            // 初始化钩子委托
            _mouseProc = MouseHookProc;
            _keyboardProc = KeyboardHookProc;
        }

        #endregion

        #region 录制功能

        /// <summary>
        /// 开始录制
        /// </summary>
        public async Task<bool> StartRecordingAsync(string description = "")
        {
            try
            {
                if (_isRecording || _isPlaying)
                {
                    return false;
                }

                _isRecording = true;
                _recordedActions.Clear();
                _recordingStartTime = DateTime.Now;

                // 安装全局钩子
                if (!InstallHooks())
                {
                    _isRecording = false;
                    Logger.Instance.ErrorLog("安装输入钩子失败");
                    return false;
                }

                RecordingStateChanged?.Invoke(this, "开始录制");
                Logger.Instance.InfoLog($"开始录制: {description}");

                return true;
            }
            catch (Exception ex)
            {
                _isRecording = false;
                Logger.Instance.ExceptionLog($"开始录制失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止录制
        /// </summary>
        public async Task<string> StopRecordingAsync(bool autoSave = true)
        {
            try
            {
                if (!_isRecording)
                {
                    return null;
                }

                _isRecording = false;

                // 卸载钩子
                UninstallHooks();

                string filePath = null;
                if (autoSave && _recordedActions.Count > 0)
                {
                    filePath = await SaveRecordingAsync();
                }

                RecordingStateChanged?.Invoke(this, "停止录制");
                Logger.Instance.InfoLog($"停止录制，共录制 {_recordedActions.Count} 个操作");

                return filePath;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止录制失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 保存录制
        /// </summary>
        private async Task<string> SaveRecordingAsync()
        {
            try
            {
                string fileName = $"Recording_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                string filePath = Path.Combine(_recordingDirectory, fileName);

                var recordingData = new RecordingData
                {
                    StartTime = _recordingStartTime,
                    EndTime = DateTime.Now,
                    Actions = _recordedActions
                };

                string json = JsonSerializer.Serialize(recordingData, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);

                Logger.Instance.InfoLog($"录制已保存到: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"保存录制失败: {ex.Message}", ex);
                return null;
            }
        }

        #endregion

        #region 回放功能

        /// <summary>
        /// 开始回放最新录制
        /// </summary>
        public async Task<bool> StartPlaybackLatestAsync()
        {
            try
            {
                var files = Directory.GetFiles(_recordingDirectory, "Recording_*.json");
                if (files.Length == 0)
                {
                    Logger.Instance.InfoLog("没有找到录制文件");
                    return false;
                }

                // 获取最新文件
                Array.Sort(files);
                string latestFile = files[files.Length - 1];

                return await StartPlaybackFromFileAsync(latestFile);
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始回放最新录制失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从文件开始回放
        /// </summary>
        public async Task<bool> StartPlaybackFromFileAsync(string filePath)
        {
            try
            {
                if (_isRecording || _isPlaying)
                {
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Logger.Instance.ErrorLog($"录制文件不存在: {filePath}");
                    return false;
                }

                string json = await File.ReadAllTextAsync(filePath);
                var recordingData = JsonSerializer.Deserialize<RecordingData>(json);

                if (recordingData?.Actions == null || recordingData.Actions.Count == 0)
                {
                    Logger.Instance.InfoLog("录制文件中没有操作数据");
                    return false;
                }

                _playbackActions = recordingData.Actions;
                _playbackIndex = 0;
                _isPlaying = true;

                PlaybackStateChanged?.Invoke(this, "开始回放");
                Logger.Instance.InfoLog($"开始回放: {filePath}, 共 {_playbackActions.Count} 个操作");

                // 开始回放定时器
                _playbackTimer.Interval = 50; // 50ms间隔
                _playbackTimer.Start();

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始回放失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止回放
        /// </summary>
        public void StopPlayback()
        {
            try
            {
                if (!_isPlaying)
                {
                    return;
                }

                _isPlaying = false;
                _playbackTimer.Stop();

                PlaybackStateChanged?.Invoke(this, "停止回放");
                Logger.Instance.InfoLog("回放已停止");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止回放失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 回放定时器

        private void PlaybackTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                if (!_isPlaying || _playbackIndex >= _playbackActions.Count)
                {
                    StopPlayback();
                    return;
                }

                var action = _playbackActions[_playbackIndex];

                // 简化的回放逻辑（实际应该使用SendInput等API）
                Logger.Instance.InfoLog($"回放操作: {action.Type} at ({action.X}, {action.Y}) - {action.Data}");

                _playbackIndex++;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"回放操作失败: {ex.Message}", ex);
                StopPlayback();
            }
        }

        #endregion

        #region 钩子管理

        /// <summary>
        /// 安装全局钩子
        /// </summary>
        private bool InstallHooks()
        {
            try
            {
                using (Process curProcess = Process.GetCurrentProcess())
                using (ProcessModule curModule = curProcess.MainModule)
                {
                    // 安装鼠标钩子
                    _mouseHook = SetWindowsHookEx(WH_MOUSE_LL, _mouseProc,
                        GetModuleHandle(curModule.ModuleName), 0);

                    // 安装键盘钩子
                    _keyboardHook = SetWindowsHookEx(WH_KEYBOARD_LL, _keyboardProc,
                        GetModuleHandle(curModule.ModuleName), 0);

                    bool success = _mouseHook != IntPtr.Zero && _keyboardHook != IntPtr.Zero;

                    if (success)
                    {
                        Logger.Instance.InfoLog("全局输入钩子安装成功");
                    }
                    else
                    {
                        Logger.Instance.ErrorLog($"钩子安装失败 - 鼠标钩子: {_mouseHook != IntPtr.Zero}, 键盘钩子: {_keyboardHook != IntPtr.Zero}");
                        UninstallHooks();
                    }

                    return success;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"安装钩子异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 卸载全局钩子
        /// </summary>
        private void UninstallHooks()
        {
            try
            {
                if (_mouseHook != IntPtr.Zero)
                {
                    UnhookWindowsHookEx(_mouseHook);
                    _mouseHook = IntPtr.Zero;
                }

                if (_keyboardHook != IntPtr.Zero)
                {
                    UnhookWindowsHookEx(_keyboardHook);
                    _keyboardHook = IntPtr.Zero;
                }

                Logger.Instance.InfoLog("全局输入钩子已卸载");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"卸载钩子异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 鼠标钩子处理程序
        /// </summary>
        private IntPtr MouseHookProc(int nCode, IntPtr wParam, IntPtr lParam)
        {
            try
            {
                if (nCode >= 0 && _isRecording)
                {
                    var hookStruct = Marshal.PtrToStructure<MSLLHOOKSTRUCT>(lParam);

                    var action = new InputAction
                    {
                        Type = GetMouseActionType((int)wParam),
                        X = hookStruct.pt.x,
                        Y = hookStruct.pt.y,
                        Timestamp = DateTime.Now,
                        Data = $"MouseData: {hookStruct.mouseData}"
                    };

                    _recordedActions.Add(action);

                    // 记录详细日志（可选，避免日志过多）
                    if (_recordedActions.Count % 10 == 0) // 每10个操作记录一次
                    {
                        Logger.Instance.InfoLog($"已录制 {_recordedActions.Count} 个操作");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"鼠标钩子处理异常: {ex.Message}", ex);
            }

            return CallNextHookEx(_mouseHook, nCode, wParam, lParam);
        }

        /// <summary>
        /// 键盘钩子处理程序
        /// </summary>
        private IntPtr KeyboardHookProc(int nCode, IntPtr wParam, IntPtr lParam)
        {
            try
            {
                if (nCode >= 0 && _isRecording)
                {
                    var hookStruct = Marshal.PtrToStructure<KBDLLHOOKSTRUCT>(lParam);

                    var action = new InputAction
                    {
                        Type = GetKeyboardActionType((int)wParam),
                        X = 0, // 键盘事件没有坐标
                        Y = 0,
                        Timestamp = DateTime.Now,
                        Data = $"VirtualKey: {hookStruct.vkCode}, ScanCode: {hookStruct.scanCode}"
                    };

                    _recordedActions.Add(action);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"键盘钩子处理异常: {ex.Message}", ex);
            }

            return CallNextHookEx(_keyboardHook, nCode, wParam, lParam);
        }

        /// <summary>
        /// 获取鼠标操作类型
        /// </summary>
        private string GetMouseActionType(int wParam)
        {
            return wParam switch
            {
                WM_LBUTTONDOWN => "LeftButtonDown",
                WM_LBUTTONUP => "LeftButtonUp",
                WM_RBUTTONDOWN => "RightButtonDown",
                WM_RBUTTONUP => "RightButtonUp",
                WM_MOUSEMOVE => "MouseMove",
                WM_MOUSEWHEEL => "MouseWheel",
                _ => $"MouseUnknown({wParam})"
            };
        }

        /// <summary>
        /// 获取键盘操作类型
        /// </summary>
        private string GetKeyboardActionType(int wParam)
        {
            return wParam switch
            {
                WM_KEYDOWN => "KeyDown",
                WM_KEYUP => "KeyUp",
                WM_SYSKEYDOWN => "SysKeyDown",
                WM_SYSKEYUP => "SysKeyUp",
                _ => $"KeyUnknown({wParam})"
            };
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 输入操作
        /// </summary>
        public class InputAction
        {
            public string Type { get; set; } = "";
            public int X { get; set; }
            public int Y { get; set; }
            public DateTime Timestamp { get; set; }
            public string Data { get; set; } = "";
        }

        /// <summary>
        /// 录制数据
        /// </summary>
        public class RecordingData
        {
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public List<InputAction> Actions { get; set; } = new List<InputAction>();
        }

        #endregion

        #region 释放资源

        public void Dispose()
        {
            try
            {
                // 停止录制和回放
                if (_isRecording)
                {
                    StopRecordingAsync(true).Wait(TimeSpan.FromSeconds(2));
                }

                if (_isPlaying)
                {
                    StopPlayback();
                }

                // 卸载钩子
                UninstallHooks();

                // 释放定时器
                _playbackTimer?.Dispose();

                Logger.Instance.InfoLog("录制回放服务已释放");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"释放录制回放服务异常: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
