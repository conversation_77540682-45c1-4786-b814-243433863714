@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    编译并测试单例功能
echo ========================================
echo.

set "SCRIPT_DIR=%~dp0"
set "CS_FILE=%SCRIPT_DIR%SingletonTest.cs"
set "EXE_FILE=%SCRIPT_DIR%SingletonTest.exe"

:: 检查C#文件是否存在
if not exist "%CS_FILE%" (
    echo [错误] 找不到测试文件: %CS_FILE%
    pause
    exit /b 1
)

:: 查找.NET编译器
set "CSC_PATH="
for %%i in (
    "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    "C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe"
) do (
    if exist "%%~i" (
        set "CSC_PATH=%%~i"
        goto :found_csc
    )
)

echo [错误] 找不到C#编译器 (csc.exe)
echo 请确保已安装Visual Studio或.NET Framework SDK
pause
exit /b 1

:found_csc
echo [信息] 找到C#编译器: %CSC_PATH%
echo.

:: 编译测试程序
echo [步骤 1] 编译单例测试程序...
"%CSC_PATH%" /out:"%EXE_FILE%" "%CS_FILE%" >nul 2>&1

if %errorLevel% neq 0 (
    echo [错误] 编译失败
    "%CSC_PATH%" /out:"%EXE_FILE%" "%CS_FILE%"
    pause
    exit /b 1
)

echo [成功] 编译完成: %EXE_FILE%
echo.

:: 测试单例功能
echo [步骤 2] 测试单例功能...
echo.
echo 测试说明：
echo 1. 将启动两个测试程序实例
echo 2. 第一个实例应该成为主实例
echo 3. 第二个实例应该检测到第一个实例并退出
echo.

set /p confirm=是否开始测试？(Y/N): 
if /i "%confirm%" neq "Y" (
    echo 测试已取消
    goto :cleanup
)

echo.
echo ========================================
echo 开始测试...
echo ========================================
echo.

:: 启动第一个实例
echo [测试] 启动第一个测试实例...
start "SingletonTest-1" cmd /k "echo 第一个实例 && "%EXE_FILE%" && pause"
echo [信息] 等待3秒让第一个实例完全启动...
timeout /t 3 /nobreak >nul

:: 启动第二个实例
echo [测试] 启动第二个测试实例...
start "SingletonTest-2" cmd /k "echo 第二个实例 && "%EXE_FILE%" && pause"
echo [信息] 两个实例都已启动，请观察控制台窗口的输出结果
echo.

echo ========================================
echo 测试结果观察指南
echo ========================================
echo.
echo 预期结果：
echo ✅ 第一个实例: 显示"当前为主实例，程序将保持运行"
echo ✅ 第二个实例: 显示"检测到其他实例正在运行，程序将退出"
echo.
echo 如果两个实例都显示为主实例，说明单例功能有问题
echo.
echo 请查看两个控制台窗口的输出，然后关闭它们继续...
pause

:cleanup
:: 清理编译生成的文件
if exist "%EXE_FILE%" (
    echo [清理] 删除测试程序: %EXE_FILE%
    del "%EXE_FILE%" >nul 2>&1
)

echo.
echo 测试完成！
pause
