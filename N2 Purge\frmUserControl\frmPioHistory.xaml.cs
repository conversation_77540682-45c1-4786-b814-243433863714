﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBEntity;
using Xceed.Wpf.Toolkit.PropertyGrid.Attributes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmPioHistory.xaml 的交互逻辑
    /// </summary>
    public partial class frmPioHistory : UserControl,INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private ObservableCollection<PioHistory> _pioHistories = new ObservableCollection<PioHistory>();
        public ObservableCollection<PioHistory> PioHistories
        {
            get { return _pioHistories; }
            set
            {
                _pioHistories = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PioHistories)));
            }
        }
    
      
        public frmPioHistory()
        {
            InitializeComponent();
            dtstart.Value = DateTime.Now.AddDays(-1);
            dtend.Value = DateTime.Now;
            this.DataContext = this;
        }
        public async void GetInfoFromDb()
        {
            PioHistories.Clear();  // 清空现有数据
            var query = await GlobalData.dbHelper.thpiodb.GetPioHistoryByTimeRangeAsync((DateTime)dtstart.Value, (DateTime)dtend.Value);
            if (query != null)
            {
                if (!string.IsNullOrEmpty(txtConnectModule.Text))
                {
                    query = query.Where(item => item.Connect_Module == txtConnectModule.Text).ToList();
                }
                if (!string.IsNullOrEmpty(txtPortName.Text))
                {
                    query = query.Where(item => item.Port_Name == txtPortName.Text).ToList();
                }
                if (!string.IsNullOrEmpty(txtIOName.Text))
                {
                    query = query.Where(item => item.IO_Name == txtIOName.Text).ToList();
                }
                if (cbxDirection.SelectedItem != null)
                {
                    query = query.Where(item => item.Direction == ((ComboBoxItem)cbxDirection.SelectedItem).Content.ToString()).ToList();
                }

                foreach (var item in query)
                {
                    PioHistories.Add(item);
                }
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            GetInfoFromDb();
        }
    }
}
