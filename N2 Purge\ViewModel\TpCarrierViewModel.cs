using System;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    public class TpCarrierViewModel : VmPropertyChange
    {
        private string _id;
        public string Id
        {
            get { return _id; }
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        private string _locationName;
        public string LocationName
        {
            get { return _locationName; }
            set
            {
                _locationName = value;
                OnPropertyChanged(nameof(LocationName));
            }
        }

        private string _stateName;
        public string StateName
        {
            get { return _stateName; }
            set
            {
                _stateName = value;
                OnPropertyChanged(nameof(StateName));
            }
        }

        private string _idReadStatusName;
        public string IDReadStatusName
        {
            get { return _idReadStatusName; }
            set
            {
                _idReadStatusName = value;
                OnPropertyChanged(nameof(IDReadStatusName));
            }
        }

        private DateTime _installTime;
        public DateTime InstallTime
        {
            get { return _installTime; }
            set
            {
                _installTime = value;
                OnPropertyChanged(nameof(InstallTime));
            }
        }

        private string _comment;
        public string Comment
        {
            get { return _comment; }
            set
            {
                _comment = value;
                OnPropertyChanged(nameof(Comment));
            }
        }
    }
}