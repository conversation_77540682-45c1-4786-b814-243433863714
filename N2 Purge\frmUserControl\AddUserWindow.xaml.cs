using System.Windows;
using System.Windows.Controls;

namespace N2Purge.frmUserControl
{
    public partial class AddUserWindow : Window
    {
        public User NewUser { get; private set; }

        public AddUserWindow()
        {
            InitializeComponent();
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名！");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Password))
            {
                MessageBox.Show("请输入密码！");
                return;
            }

            if (cmbUserLevel.SelectedItem == null)
            {
                MessageBox.Show("请选择用户等级！");
                return;
            }

            NewUser = new User
            {
                UserName = txtUsername.Text,
                Password = txtPassword.Password,
                UserLevel = int.Parse(((ComboBoxItem)cmbUserLevel.SelectedItem).Content.ToString())
            };

            DialogResult = true;
            Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}