﻿<UserControl x:Class="N2Purge.silan.frmMainsilan"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.silan"
             xmlns:ctr="clr-namespace:N2Purge.userControls"
             xmlns:ctrlocal="clr-namespace:N2Purge.Conveyor"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" Loaded="UserControl_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="2*"/>
            <RowDefinition Height="8*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="6*"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>
        <GroupBox Header="Transfer Order List">
            <DataGrid x:Name="transgerdgv" ContextMenuOpening="ContextMenu_ContextMenuOpening" CanUserAddRows="False" BorderThickness="1" BorderBrush="Black" AutoGenerateColumns="False">
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem x:Name="btncancel" Header="Cancel" Click="btncancel_Click" 
             Tag="{Binding RelativeSource={RelativeSource AncestorType=ContextMenu}, 
                   Path=PlacementTarget.(DataGrid.SelectedItem)}"/>
                        <MenuItem x:Name="btnAbort" Header="Abort" Click="btnAbort_Click"
             Tag="{Binding RelativeSource={RelativeSource AncestorType=ContextMenu}, 
                   Path=PlacementTarget.(DataGrid.SelectedItem)}"/>
                        <MenuItem x:Name="btnUpdate" Header="Update Priority" Click="btnUpdate_Click"
             Tag="{Binding RelativeSource={RelativeSource AncestorType=ContextMenu}, 
                   Path=PlacementTarget.(DataGrid.SelectedItem)}"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
                <DataGrid.Columns>
                    <DataGridTextColumn Width="*" Header="ID" Binding="{Binding Id}"/>
                    <DataGridTextColumn Header="CarrierID" Binding="{Binding CarrierId}" Width="*"/>
                    <DataGridTextColumn Header="SourceLocation" Binding="{Binding SourceLocation}" Width="*"/>
                    <DataGridTextColumn Header="DestLocation" Binding="{Binding DestLocation}" Width="*"/>
                    <DataGridTextColumn Header="Priority" Binding="{Binding Priority}" Width="*"/>
                    <DataGridTextColumn Header="CommandType" Binding="{Binding CommandType}" Width="*"/>
                    <DataGridTextColumn Header="CreateTime" Binding="{Binding CreateTime}" Width="*"/>
                    <DataGridTextColumn Header="State" Binding="{Binding State}" Width="*"/>
                    <DataGridTextColumn Header="CmdSource" Binding="{Binding CmdSource}" Width="*"/>
                    <DataGridTextColumn Header="DelayReason" Binding="{Binding DelayReason}" Width="*"/>
                    <DataGridTextColumn Header="CurrLocation" Binding="{Binding CurrLocation}" Width="*"/>
                    <DataGridTextColumn Header="WaitingTime" Binding="{Binding WaitingTime}" Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>
        <Border BorderThickness="1" BorderBrush="LightGray" Grid.Column="0" Grid.Row="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="8*"/>
                    <ColumnDefinition Width="2*"/>
                </Grid.ColumnDefinitions>
                <ctrlocal:frmConvryorMain Grid.Row="1"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center"  VerticalAlignment="Center" Grid.Row="1">
                    <!-- CPU使用率 -->
                    <ctrlocal:CircleUsageIndicator 
                x:Name="CpuIndicator" 
                UsagePercentage="10" 
                Caption="CV使用率" 
                Width="200" Height="200"/>

                </StackPanel>
                <!--<Menu Grid.ColumnSpan="2">
                    <MenuItem Header="System">
                        <MenuItem Header="Host Mode Change"/>
                        <MenuItem Header="System Status Change"/>
                        <MenuItem Header="Option"/>
                        <MenuItem Header="Shared Zone Set"/>
                        <MenuItem Header="Minimize"/>
                        <MenuItem Header="Exit"/>
                    </MenuItem>
                    <MenuItem Header="Report">
                        <MenuItem Header="Transfer History"/>
                        <MenuItem Header="Alarm History"/>
                        <MenuItem Header="Port ID Read Error History"/>
                        <MenuItem Header="History Chart"/>
                        <MenuItem Header="Terminal Message History"/>
                        <MenuItem Header="Failure Analysis"/>
                        <MenuItem Header="Servo Monitoring"/>
                    </MenuItem>
                    <MenuItem Header="User">
                        <MenuItem Header="Log In"/>
                        <MenuItem Header="Log Out"/>
                        <MenuItem Header="User Info Change"/>
                    </MenuItem>
                    <MenuItem Header="Operation">
                        <MenuItem Header="Normal Operation"/>
                        <MenuItem Header="Crane Stop"/>
                        <MenuItem Header="Manual Transfer"/>
                    </MenuItem>
                    <MenuItem Header="Data Management"/>
                    <MenuItem Header="Window"/>
                    <MenuItem Header="Help"/>
                </Menu>-->
                <!--<Grid x:Name="gd" Margin="2" Grid.Row="1">
                    <Grid Margin="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="50"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="40"/>
                        
                        </Grid.ColumnDefinitions>
                        <Border x:Name="craneContainer" Margin="0" Grid.Column="1" Grid.ColumnSpan="1" Grid.Row="2" BorderThickness="1" BorderBrush="Gray"  SizeChanged="CraneContainer_SizeChanged">
                            <ctr:Crane x:Name="crane"  HorizontalAlignment="Left" >
                                <ctr:Crane.RenderTransform>
                                    <TransformGroup>
                                        <TranslateTransform X="{Binding CranePosition}" />
                                        <RotateTransform Angle="0" CenterX="0" CenterY="0"/>
                                    </TransformGroup>
                                </ctr:Crane.RenderTransform>
                            </ctr:Crane>
                            --><!--<Grid>
                                
                                --><!--<Rectangle Stroke="Gray"  StrokeThickness="1"/>--><!--
                            </Grid>--><!--
                        </Border>
                        <Grid Grid.Column="1" x:Name="InnerGrid" Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                             
                            </Grid.RowDefinitions>
                        </Grid>
                        <Grid Grid.Column="1" x:Name="RightGrid" Grid.Row="4" Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                              
                            </Grid.RowDefinitions>
                        </Grid>
                        <Grid Grid.Row="0" Grid.Column="0" Width="50">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                           
                            </Grid.RowDefinitions>

                            <Label Content="10" Grid.Row="0" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="9" Grid.Row="1" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="8" Grid.Row="2" HorizontalContentAlignment="Left" VerticalContentAlignment="Center" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="7" Grid.Row="3" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="6" Grid.Row="4" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="5" Grid.Row="5" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="4" Grid.Row="6" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="3" Grid.Row="7" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="2" Grid.Row="8" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="1" Grid.Row="9" HorizontalContentAlignment="Left" VerticalContentAlignment="Bottom" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Grid>
                        <Grid Grid.Row="4" Grid.Column="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                             
                            </Grid.RowDefinitions>
                            <Label Content="1" Grid.Row="0" VerticalContentAlignment="Center" HorizontalContentAlignment="Left"  VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="2" Grid.Row="1" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="3" Grid.Row="2" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="4" Grid.Row="3" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="5" Grid.Row="4" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="6" Grid.Row="5" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="7" Grid.Row="6" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="8" Grid.Row="7" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="9" Grid.Row="8" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            <Label Content="10" Grid.Row="9" VerticalContentAlignment="Center" HorizontalContentAlignment="Left" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                         
                        </Grid>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Content="1" Grid.Column="0" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"  VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="2" Grid.Column="1" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="3" Grid.Column="2" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="4" Grid.Column="3" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="5" Grid.Column="4" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="6" Grid.Column="5" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="7" Grid.Column="6" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="8" Grid.Column="7" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="9" Grid.Column="8" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="10" Grid.Column="9" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="11" Grid.Column="10" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="12" Grid.Column="11" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="13" Grid.Column="12" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="14" Grid.Column="13" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                        </Grid>
                        <Grid Grid.Row="3" Grid.Column="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Content="1" Grid.Column="0" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"  VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="2" Grid.Column="1" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="3" Grid.Column="2" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="4" Grid.Column="3" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="5" Grid.Column="4" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="6" Grid.Column="5" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="7" Grid.Column="6" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="8" Grid.Column="7" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="9" Grid.Column="8" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="10" Grid.Column="9" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="11" Grid.Column="10" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="12" Grid.Column="11" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="13" Grid.Column="12" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            <Label Content="14" Grid.Column="13" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                        </Grid>
                    </Grid>
                </Grid>-->
            </Grid>
        </Border>
        <Border BorderThickness="1" BorderBrush="LightGray" Grid.Column="1" Grid.Row="0" Grid.RowSpan="2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="3*"/>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="6*"/>
                </Grid.RowDefinitions>
                <local:frmAction x:Name="frmat" Grid.Row="0"/>
                <Button x:Name="btnExcute" Grid.Row="1" Margin="5" Content="Excute" TextBlock.FontSize="18" Click="btnExcute_Click"/>
                <TextBlock x:Name="lblTransferResult" Grid.Row="2" Margin="2" Background="LightYellow"/>
                <!--<local:frmInfo x:Name="storageIno" Grid.Row="3"/>-->
                <ctrlocal:frmCVIO x:Name="storageIno" Grid.Row="3"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
