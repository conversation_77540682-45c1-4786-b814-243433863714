﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmUser.xaml 的交互逻辑
    /// </summary>
    public partial class frmUser : UserControl
    {
        public event EventHandler<string> UserLoginTabClicked;
        private bool isLoad=false;
        private bool islogin=false;
        public frmUser()
        {
            InitializeComponent();
            if (isLoad) return;
            _ = InitializeUserList(); // 异步初始化，不等待
            isLoad=true;
        }

        /// <summary>
        /// 刷新用户列表
        /// </summary>
        public async Task RefreshUserList()
        {
            await InitializeUserList();
        }

        private async Task InitializeUserList()
        {
            try
            {
                var userNames = await GlobalData.dbHelper._userManagement.GetAllUserNames();
                cboUser.ItemsSource = userNames;

                // 如果列表为空，显示提示
                if (userNames == null || userNames.Count == 0)
                {
                    lblTip.Text = "未找到用户数据";
                    lblTip.Foreground = Brushes.Orange;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取用户列表失败：{ex.Message}");
                lblTip.Text = "加载用户列表失败";
                lblTip.Foreground = Brushes.Red;
            }
        }

        private async void btnLoggin_Click(object sender, RoutedEventArgs e)
        {
            if (btnLoggin.Content.ToString()=="登录")
            {
                if (string.IsNullOrEmpty(cboUser.Text) || string.IsNullOrEmpty(pwdUserPassword.Password))
                {
                    lblTip.Text = "请输入用户名和密码";
                    return;
                }

                try
                {
                    bool loginSuccess = await GlobalData.dbHelper._userManagement.Login(cboUser.Text, pwdUserPassword.Password);
                    if (loginSuccess)
                    {
                        lblTip.Text = "登录成功";
                        lblTip.Foreground = Brushes.Green;
                        LblCurUserName.Text = cboUser.Text;
                        LblCurLevel.Text = GlobalData.dbHelper._userManagement._currentUserLevel.ToString();
                        UserLoginTabClicked?.Invoke(this, "UserLogin");
                        GlobalData.UserLogin(true);
                        pwdUserPassword.Password = ""; // 清空密码框
                        btnLoggin.Content = "退出";
                    }
                    else
                    {
                        lblTip.Text = "密码错误";
                        lblTip.Foreground = Brushes.Red;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"登录失败：{ex.Message}");
                }
            }
            else
            {
                GlobalData.dbHelper._userManagement.LogOut();
                lblTip.Text = "";
                lblTip.Foreground = Brushes.White;
                LblCurUserName.Text = "";
                LblCurLevel.Text = "";
                GlobalData.UserLogin(false);
                pwdUserPassword.Password = "";
                btnLoggin.Content = "登录";
                UserLoginTabClicked?.Invoke(this, "UserLogout");
            }
        }
    }
}
