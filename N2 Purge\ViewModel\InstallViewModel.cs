using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    /// <summary>
    /// Install Vm
    /// </summary>
    public class InstallViewModel : VmPropertyChange
    {
        private string cassetteId;
        private string sourceLocation;
        private string _InstallCstState;
        private string priority;

        public string CassetteId
        {
            get { return cassetteId; }
            set
            {
                if (cassetteId != value)
                {
                    cassetteId = value;
                    OnPropertyChanged(nameof(CassetteId));
                }
            }
        }

        public string SourceLocation
        {
            get { return sourceLocation; }
            set
            {
                if (sourceLocation != value)
                {
                    sourceLocation = value;
                    OnPropertyChanged(nameof(SourceLocation));
                }
            }
        }

        public string InstallCstState
        {
            get { return _InstallCstState; }
            set
            {
                if (_InstallCstState != value)
                {
                    _InstallCstState = value;
                    OnPropertyChanged(nameof(InstallCstState));
                }
            }
        }

        public string Priority
        {
            get { return priority; }
            set
            {
                if (priority != value)
                {
                    priority = value;
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

     
    }
}