﻿<UserControl x:Class="N2Purge.silan.frmAction"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.silan"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="200" Loaded="FrmAction_Loaded">
    <Grid Grid.Column="1" Grid.Row="0" Grid.RowSpan="2">
        <TabControl x:Name="tb">
            <TabItem Header="Transfer" TextBlock.FontSize="18" x:Name="transfer">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="0"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="0"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Label Content="Cassette ID" Margin="4" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Content="Source Location" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Content="Dest Location" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Content="Priority" Margin="4"  Background="LightBlue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <TextBox Margin="4" x:Name="CarrierId" Text="{Binding CarrierId}" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="black" VerticalContentAlignment="Center"   TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                    <!--<TextBox Margin="4" x:Name="SourceLocation" Text="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>-->
                    <ComboBox Margin="4" x:Name="SourceLocation" ItemsSource="{Binding SourceList}" SelectedValue="{Binding SourceLocation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" 
                                  DisplayMemberPath="Value" SelectedValuePath="Key"
                              Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                    
                    
                    <!--<TextBox Margin="4" x:Name="DestLocation" Text="{Binding DestLocation}" Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>-->
                    <ComboBox Margin="4" x:Name="DestLocation" ItemsSource="{Binding DestList}"  SelectedValue="{Binding DestLocation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" 
                              DisplayMemberPath="Value" SelectedValuePath="Key"
                              Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="Black" VerticalContentAlignment="Center"  TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                    
                    <TextBox Margin="4" x:Name="Priority" Text="{Binding Priority}" Background="Transparent" Grid.Column="3" Grid.Row="3" TextBlock.Foreground="Black" VerticalContentAlignment="Center" TextBlock.TextAlignment="Center" TextBlock.FontSize="16"/>
                </Grid>
            </TabItem>
            <TabItem Header="Install" TextBlock.FontSize="18" x:Name="install">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>
                    <Label Margin="4"  Content="Cassette ID" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Margin="4"  Content="Location" Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Margin="4"  Content="CST State" Background="LightBlue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Margin="4"  Content="IDR Result" Background="LightBlue" Grid.Column="0" Grid.Row="3" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <TextBox Margin="4" x:Name="CassetteId"  Text="{Binding CassetteId}" Background="Transparent" Grid.Column="1" Grid.Row="0" VerticalContentAlignment="Center" TextBlock.Foreground="black" TextBlock.FontSize="16"/>
                    <TextBox Margin="4" x:Name="InstallSourceLocation" Text="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" VerticalContentAlignment="Center" Grid.Row="1" TextBlock.Foreground="black" TextBlock.FontSize="16"/>
                    <TextBox Margin="4" x:Name="InstallCstState" Text="{Binding InstallCstState}" Background="Transparent" Grid.Column="1" Grid.Row="2" VerticalContentAlignment="Center" TextBlock.Foreground="black" TextBlock.FontSize="16"/>
                    <ComboBox Margin="4" x:Name="InstallIdResult"   Background="Transparent" Grid.Column="1" Grid.Row="3" TextBlock.Foreground="black" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                </Grid>
            </TabItem>
            <TabItem Header="Remove" TextBlock.FontSize="18" x:Name="remove">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>
                    <Label Margin="4"  Content="Cassette ID" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Margin="4"  Content="Location" Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>


                    <TextBox Margin="4" x:Name="RemoveCstID"  Text="{Binding CstID}" Background="Transparent" Grid.Column="1" Grid.Row="0"  TextBlock.Foreground="black" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <TextBox Margin="4" x:Name="RemoveLocation" Text="{Binding Location}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="black" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>

                </Grid>
            </TabItem>
            <!--不显示扫描项目-->
            <TabItem Visibility="Collapsed" Header="Scan" TextBlock.FontSize="18" x:Name="scan">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="97*"/>
                        <ColumnDefinition Width="56*"/>
                        <ColumnDefinition Width="18*"/>
                        <ColumnDefinition Width="23*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>
                    <Label Margin="4,4,4,4"  Content="Cassette ID" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <Label Margin="4,4,4,4"  Content="Source Location" Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>

                    <Label Margin="4,4,4,4" Content="Priority" Background="LightBlue" Grid.Column="0" Grid.Row="2" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <TextBox Margin="4,4,4,4" x:Name="ScanCassetteId" Text="{Binding CassetteId}" Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="black" Grid.ColumnSpan="3" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>
                    <TextBox Margin="4,4,4,4" x:Name="ScanSourceLocation" Text="{Binding SourceLocation}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="black" VerticalContentAlignment="Center" Grid.ColumnSpan="3" TextBlock.FontSize="16"/>

                    <TextBox Margin="4,4,4,4" x:Name="ScanPriority"  Text="{Binding Priority}" Background="Transparent" Grid.Column="1" Grid.Row="2" TextBlock.Foreground="black" Grid.ColumnSpan="3" TextBlock.FontSize="16" VerticalContentAlignment="Center"/>
                </Grid>
            </TabItem>
            <!--不显示移动项目-->
            <TabItem Visibility="Collapsed" Header="Move" TextBlock.FontSize="18" x:Name="move">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>

                    <Label Margin="4"  Content="Dest Location" Background="LightBlue" Grid.Column="0" Grid.Row="0" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>

                    <Label Margin="4"  Content="Priority" Background="LightBlue" Grid.Column="0" Grid.Row="1" TextBlock.Foreground="White" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>

                    <TextBox Margin="4" x:Name="MoveSourceLocation" Text="{Binding SourceLocation}"  Background="Transparent" Grid.Column="1" Grid.Row="0" TextBlock.Foreground="black" VerticalContentAlignment="Center" TextBlock.FontSize="16"/>

                    <TextBox Margin="4" x:Name="MovePriority"  Text="{Binding Priority}" Background="Transparent" Grid.Column="1" Grid.Row="1" TextBlock.Foreground="black" TextBlock.FontSize="16" VerticalContentAlignment="Center"/>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
