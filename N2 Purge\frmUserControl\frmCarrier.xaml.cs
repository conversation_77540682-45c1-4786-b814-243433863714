﻿using DBEntity;
using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmCarrier.xaml 的交互逻辑
    /// </summary>
    public partial class frmCarrier : UserControl
    {
        public frmCarrier()
        {
            InitializeComponent();
            dgv.ItemsSource = tpCarriers;
        }
        private ObservableCollection<TpCarrierViewModel> tpCarriers = new ObservableCollection<TpCarrierViewModel>(); 
        public async void GetCarrierFromDb()
        {
            var carrierlst = await GlobalData.dbHelper.tpCarrierdb.GetAllTpCarrierAsync();
            if (carrierlst != null)
            {
                foreach (var carrier in carrierlst)
                {
                    TpCarrierViewModel current=new TpCarrierViewModel();
                    current.Id = carrier.Id;
                    current.LocationName = carrier.Location;current.IDReadStatusName = carrier.Id_Read_Status;
                    current.InstallTime = (DateTime)carrier.Install_Time;
                    current.StateName = carrier.State;
                    var existingCarrier = tpCarriers.FirstOrDefault(c => c.Id == carrier.Id);
                    if (existingCarrier != null)
                    {
                        // 检查是否有变化
                        if (!string.Equals(existingCarrier.LocationName, carrier.Location) ||
                            !string.Equals(existingCarrier.StateName, carrier.State) ||
                            !string.Equals(existingCarrier.IDReadStatusName, carrier.Id_Read_Status) ||
                            !string.Equals(existingCarrier.Comment, carrier.Comment))
                        {
                            var index = tpCarriers.IndexOf(existingCarrier);
                            tpCarriers[index] = current;
                        }
                    }
                    else
                    {
                        // 新增载具
                        tpCarriers.Add(current);
                    }
                }

                // 移除已不存在的载具
                var removedCarriers = tpCarriers.Where(c => !carrierlst.Any(n => n.Id == c.Id)).ToList();
                foreach (var removedCarrier in removedCarriers)
                {
                    tpCarriers.Remove(removedCarrier);
                }
            }
        }

        private void btnsearch_Click(object sender, RoutedEventArgs e)
        {
            GetCarrierFromDb();
        }

        private async void btnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (dgv.SelectedItem==null)
            {
                return;
            }
            var sel = dgv.SelectedItem as TpCarrierViewModel;
            if (sel==null)
            {
                return;
            }
            MessageBoxResult mr = MessageBox.Show("Are you sure to delete the selected data?",
                "Info",
                MessageBoxButton.OKCancel,
                MessageBoxImage.Information);
            if (mr != MessageBoxResult.OK) { return; }
            string strAddress = "";
            strAddress = sel.LocationName;
            if (await GlobalData.dbHelper.tpCarrierdb.DeleteTpCarrierAsync(sel.Id)==1)
            {
                if (!string.IsNullOrEmpty(strAddress))
                {
                    var tploc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strAddress);
                    if (tploc!=null)
                    {
                        tploc.Is_Occupied = 0;
                        await GlobalData.dbHelper.tpLocationdb.UpdateTpLocationAsync(tploc,strAddress);
                        GetCarrierFromDb();
                    }
                }
            }
        }
    }
}
