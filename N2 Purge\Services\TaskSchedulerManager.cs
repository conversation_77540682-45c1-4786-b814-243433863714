using System;
using System.Diagnostics;
using System.IO;
using System.Security.Principal;
using System.Text;
using Proj.Log;

namespace N2Purge.Services
{
    /// <summary>
    /// 计划任务管理器
    /// </summary>
    public class TaskSchedulerManager
    {
        private const string TASK_NAME = "N2PurgeAutoStart";
        private const string TASK_DESCRIPTION = "N2 Purge 智能传送带控制系统自动启动任务";
        
        /// <summary>
        /// 创建计划任务
        /// </summary>
        /// <param name="executablePath">可执行文件路径</param>
        /// <param name="startupDelay">启动延迟(秒)</param>
        /// <returns>是否创建成功</returns>
        public static bool CreateStartupTask(string executablePath, int startupDelay = 30)
        {
            try
            {
                if (!IsAdministrator())
                {
                    Logger.Instance?.ExceptionLog("创建计划任务需要管理员权限");
                    return false;
                }

                if (!File.Exists(executablePath))
                {
                    Logger.Instance?.ExceptionLog($"可执行文件不存在: {executablePath}");
                    return false;
                }

                // 删除已存在的任务
                DeleteStartupTask();

                // 创建XML配置
                string taskXml = GenerateTaskXml(executablePath, startupDelay);
                string tempXmlPath = Path.Combine(Path.GetTempPath(), $"{TASK_NAME}.xml");
                
                File.WriteAllText(tempXmlPath, taskXml, Encoding.UTF8);

                // 使用schtasks命令创建任务
                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/create /tn \"{TASK_NAME}\" /xml \"{tempXmlPath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        string output = process.StandardOutput.ReadToEnd();
                        string error = process.StandardError.ReadToEnd();

                        if (process.ExitCode == 0)
                        {
                            Logger.Instance?.OperationLog($"计划任务创建成功: {TASK_NAME}");
                            return true;
                        }
                        else
                        {
                            Logger.Instance?.ExceptionLog($"创建计划任务失败: {error}");
                            return false;
                        }
                    }
                }

                // 清理临时文件
                if (File.Exists(tempXmlPath))
                {
                    File.Delete(tempXmlPath);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"创建计划任务异常: {ex.Message}");
                return false;
            }

            return false;
        }

        /// <summary>
        /// 删除计划任务
        /// </summary>
        /// <returns>是否删除成功</returns>
        public static bool DeleteStartupTask()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/delete /tn \"{TASK_NAME}\" /f",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        if (process.ExitCode == 0)
                        {
                            Logger.Instance?.OperationLog($"计划任务删除成功: {TASK_NAME}");
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"删除计划任务异常: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 检查计划任务是否存在
        /// </summary>
        /// <returns>任务是否存在</returns>
        public static bool TaskExists()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/query /tn \"{TASK_NAME}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        return process.ExitCode == 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance?.ExceptionLog($"检查计划任务异常: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 检查是否具有管理员权限
        /// </summary>
        /// <returns>是否为管理员</returns>
        public static bool IsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 生成任务XML配置
        /// </summary>
        /// <param name="executablePath">可执行文件路径</param>
        /// <param name="startupDelay">启动延迟</param>
        /// <returns>XML配置字符串</returns>
        private static string GenerateTaskXml(string executablePath, int startupDelay)
        {
            string workingDirectory = Path.GetDirectoryName(executablePath) ?? "";
            string currentUser = Environment.UserName;
            
            return $@"<?xml version=""1.0"" encoding=""UTF-16""?>
<Task version=""1.4"" xmlns=""http://schemas.microsoft.com/windows/2004/02/mit/task"">
  <RegistrationInfo>
    <Date>{DateTime.Now:yyyy-MM-ddTHH:mm:ss.fffffffK}</Date>
    <Author>{currentUser}</Author>
    <Description>{TASK_DESCRIPTION}</Description>
  </RegistrationInfo>
  <Triggers>
    <BootTrigger>
      <Enabled>true</Enabled>
      <Delay>PT{startupDelay}S</Delay>
    </BootTrigger>
    <LogonTrigger>
      <Enabled>true</Enabled>
      <Delay>PT{startupDelay}S</Delay>
    </LogonTrigger>
  </Triggers>
  <Principals>
    <Principal id=""Author"">
      <UserId>{Environment.UserDomainName}\\{currentUser}</UserId>
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <DisallowStartOnRemoteAppSession>false</DisallowStartOnRemoteAppSession>
    <UseUnifiedSchedulingEngine>true</UseUnifiedSchedulingEngine>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
    <RestartOnFailure>
      <Interval>PT1M</Interval>
      <Count>3</Count>
    </RestartOnFailure>
  </Settings>
  <Actions Context=""Author"">
    <Exec>
      <Command>{executablePath}</Command>
      <WorkingDirectory>{workingDirectory}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>";
        }
    }
}
