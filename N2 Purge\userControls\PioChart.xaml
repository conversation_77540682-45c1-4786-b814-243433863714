<UserControl x:Class="N2Purge.userControls.PioChart"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:oxy="http://oxyplot.org/wpf"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="100"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 左侧信号名称列表 -->
        <StackPanel Grid.Column="0">
            <Border Height="30" Margin="2,40,2,2" Background="LightGray">
                <TextBlock Text="A_GO" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_CONT" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_COMPT" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_Busy" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_TR_REQ" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_CS_0" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="A_VALID" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="P_ES" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="P_NO_AVEL" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="P_Ready" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="P_U_REQ" VerticalAlignment="Center" Margin="5"/>
            </Border>
            <Border Height="30" Margin="2" Background="LightGray">
                <TextBlock Text="P_L_REQ" VerticalAlignment="Center" Margin="5"/>
            </Border>
        </StackPanel>
        
        <!-- 右侧 OxyPlot 图表 -->
        <oxy:PlotView x:Name="plotView" Grid.Column="1" Background="Black"/>
    </Grid>
</UserControl>