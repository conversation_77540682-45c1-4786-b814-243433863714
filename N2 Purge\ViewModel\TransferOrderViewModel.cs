using DBEntity;
using Microsoft.Extensions.Logging;
using N2Purge.frmUserControl;
using N2Purge.ViewModel;
using Proj.WCF;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace N2Purge.ViewModel
{
    public class TransferOrderViewModel : VmPropertyChange
    {
        private bool _canCancel;
        public bool CanCancel
        {
            get => _canCancel;
            set
            {
                _canCancel = value;
                OnPropertyChanged(nameof(CanCancel));
            }
        }

        private ICommand _cancelCommand;
        public ICommand CancelCommand
        {
            get
            {
                return _cancelCommand ?? (_cancelCommand = new RelayCommand(ExecuteCancel));
            }
        }

        private ICommand _abortCommand;
        //public ICommand AbortCommand
        //{
        //    get
        //    {
        //        return _abortCommand ?? (_abortCommand = new RelayCommand( ExecuteAbort));
        //    }
        //}

        private ICommand _updatePriorityCommand;
        public ICommand UpdatePriorityCommand
        {
            get
            {
                return _updatePriorityCommand ?? (_updatePriorityCommand = new RelayCommand(ExecuteUpdatePriority));
            }
        }

        private void ExecuteCancel(object parameter)
        {
            //检查权限
            var selectedItem = parameter as TransferViewModel;
            if (selectedItem != null)
            {
                // Logger.Instance.OperationLog("Click button: cancel.");

                Dictionary<string, object> dicParams = new Dictionary<string, object>();

                dicParams.Add("COMMANDID", selectedItem.Id);
                string strLog = "Click button: cancel, CommondID=";
                strLog += selectedItem.Id.ToString();

                //Logger.Instance.OperationLog(strLog);
                object objResult = WCFClient.Instance.SendMessage("Cancel", dicParams);
                bool bResult = true;
                if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                {
                    bResult = false;
                }
                if (bResult)
                {
                    MessageBox.Show("Submit cancel successfully");
                }
                else
                {
                    MessageBox.Show("Submit cancel failure");
                }

            }
            else
            {
                MessageBox.Show("Please select a transfer task.");
                return;
            }
        }

        private async Task ExecuteAbort(object parameter)
        {
            //检查权限
            if (GlobalData.dbHelper._userManagement.CheckOperationPermission(2))
            {
                return;
            }
            var selectedItem = parameter as TransferViewModel;
            if (selectedItem != null)
            {
                Dictionary<string, object> dicCheck = new Dictionary<string, object>();
                object objCheckAbort =await WCFClient.Instance.SendMessage("CheckAbort", dicCheck);
                bool bCheckResult = true;
                if (objCheckAbort == null || !bool.TryParse(objCheckAbort.ToString(), out bCheckResult))
                {
                    bCheckResult = false;
                }
                if (!bCheckResult)
                {
                    string strMsg = "PLC任务无法停止，请用示教盒操作，将Crane的Y轴缩回到原位。";
                    MessageBox.Show(strMsg);
                    // Logger.Instance.OperationLog(strMsg);
                    return;
                }

                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("COMMANDID", selectedItem.Id);
                object objResult = WCFClient.Instance.SendMessage("Abort", dicParams);
                bool bResult = true;
                if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                {
                    bResult = false;
                }
                if (bResult)
                {
                    MessageBox.Show("Submit abort successfully");
                }
                else
                {
                    MessageBox.Show("Submit abort failure");
                }
            }
            else
            {
                MessageBox.Show("Please select a transfer task.");
                return;
            }
        }

        private void ExecuteUpdatePriority(object parameter)
        {
            var selectedItem = parameter as TransferViewModel;
            if (selectedItem != null)
            {

                string strCmdID = selectedItem.Id.ToString();
                FrmUpdatePriority frmPriority = new FrmUpdatePriority(strCmdID);
                frmPriority.CommandId = strCmdID;
                if (frmPriority.ShowDialog() == true)
                {
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("COMMANDID", strCmdID);
                    dicParams.Add("PRIORITY", frmPriority.Priority);
                    object objResult = WCFClient.Instance.SendMessage("UpdatePriority", dicParams);
                    bool bResult = true;
                    if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                    {
                        bResult = false;
                    }
                    if (bResult)
                    {
                        MessageBox.Show("Submit update priority successfully");
                    }
                    else
                    {
                        MessageBox.Show("Submit update priority failure");
                    }
                }
            }
        }

        public class RelayCommand : ICommand
        {
            private readonly Action<object> _execute;
            private readonly Predicate<object> _canExecute;

            public RelayCommand(Action<object> execute, Predicate<object> canExecute = null)
            {
                _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                _canExecute = canExecute;
            }

            public event EventHandler CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }

            public bool CanExecute(object parameter)
            {
                return _canExecute == null || _canExecute(parameter);
            }

            public void Execute(object parameter)
            {
                _execute(parameter);
            }
        }
    }
}