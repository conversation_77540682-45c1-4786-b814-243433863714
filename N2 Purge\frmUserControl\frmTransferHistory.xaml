﻿<UserControl x:Class="N2Purge.frmUserControl.frmTransferHistory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.frmUserControl"
             xmlns:xctr="http://schemas.xceed.com/wpf/xaml/toolkit"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>

        </Grid.RowDefinitions>
        <Border BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Label  Content="开始时间" Grid.Column="0" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <xctr:DateTimePicker Grid.Column="1" x:Name="dtstart" Background="White" Width="200" Margin="0"  HorizontalAlignment="Center" VerticalAlignment="Center" Format="Custom" TextBlock.TextAlignment="Center" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="结束时间" Grid.Column="2" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <xctr:DateTimePicker Grid.Column="3" x:Name="dtend" Background="White"  Width="200" Margin="0"  HorizontalAlignment="Center" VerticalAlignment="Center" Format="Custom" TextBlock.TextAlignment="Center" FormatString="yyyy-MM-dd HH:mm:ss"/>
                <Label  Content="命令ID" Grid.Column="4" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBox  x:Name="txtCommandID" Width="100" Background="White" Grid.Column="5" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Label  Content="CarrierID" Grid.Column="6" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBox x:Name="txtCarrierID" Width="100" Background="White" Grid.Column="7" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
            </Grid>
        </Border>
        <Border Grid.Row="1" BorderThickness="1" BorderBrush="Black" Background="AliceBlue">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="80"/>

                    <ColumnDefinition Width="286*"/>
                    <ColumnDefinition Width="9*"/>
                    <ColumnDefinition Width="223*"/>
                </Grid.ColumnDefinitions>

                <Label  Content="命令源" Grid.Column="0" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <TextBox x:Name="txtCommandSrc" Width="100" Background="White" Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Label  Content="命令状态" Grid.Column="2" HorizontalContentAlignment="Center" VerticalAlignment="Center"/>
                <ComboBox x:Name="cbxCommandState" Width="100" Background="White" Grid.Column="3" HorizontalAlignment="Center" VerticalAlignment="Center" TextBlock.TextAlignment="Center"/>
                <Button x:Name="btnSearch" Content="查询" Grid.Column="4" Margin="2,2,2,2" Background="Snow" Click="btnSearch_Click"/>

            </Grid>
        </Border>
        <DataGrid Grid.Row="2" x:Name="dgv" AutoGenerateColumns="False" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="CmdTime" Binding="{Binding Cmd_Time}" Width="1*"/>
                <DataGridTextColumn Header="CmdID" Binding="{Binding Cmd_ID}" Width="1*"/>
                <DataGridTextColumn Header="CarrierID" Binding="{Binding Carrier_ID}" Width="1*"/>
                <DataGridTextColumn Header="CmdSource" Binding="{Binding Cmd_Source}" Width="1*"/>
                <DataGridTextColumn Header="Priority" Binding="{Binding Priority}" Width="1*"/>
                <DataGridTextColumn Header="CmdType" Binding="{Binding Cmd_Type}" Width="1*"/>
                <DataGridTextColumn Header="StrLocation" Binding="{Binding Source_Location}" Width="1*"/>
                <DataGridTextColumn Header="DestLocation" Binding="{Binding Dest_Location}" Width="1*"/>
                <DataGridTextColumn Header="CalcPriority" Binding="{Binding Calc_Priority}" Width="1*"/>
                <DataGridTextColumn Header="TransferType" Binding="{Binding Transfer_Type}" Width="1*"/>
                <DataGridTextColumn Header="CraneName" Binding="{Binding Crane_Name}" Width="1*"/>
                <DataGridTextColumn Header="CraneStartTime" Binding="{Binding Crane_Start_Time}" Width="1*"/>
                <DataGridTextColumn Header="CraneEndTime" Binding="{Binding Crane_End_Time}" Width="1*"/>
                <DataGridTextColumn Header="CmdState" Binding="{Binding Cmd_State}" Width="1*"/>
                <DataGridTextColumn Header="DelayReason" Binding="{Binding Delay_Reason}" Width="1*"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="1*"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
