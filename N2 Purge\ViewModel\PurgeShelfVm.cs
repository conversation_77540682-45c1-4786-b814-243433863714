﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace N2Purge.ViewModel
{ 
    /// <summary>
    /// N2 Shelf VM
    /// </summary>
   public class PurgeShelfVm:VmPropertyChange
    {
        private string _shelfId;
        public string ShelfId
        {
            get { return _shelfId; }
            set
            {
                if (_shelfId != value)
                {
                    _shelfId = value;
                    OnPropertyChanged(nameof(ShelfId));
                }
            }
        }

        private string _Status;
        public string Status
        {
            get { return _Status; }
            set
            {
                if (_Status != value)
                {
                    _Status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        private bool _FoupType;

        public bool FoupType
        {
            get { return _FoupType; }
            set
            {
                if (_FoupType != value)
                {
                    _FoupType = value;
                    OnPropertyChanged(nameof(FoupType));
                }
            }
        }
        private string _FoupID;
        public string FoupID
        {
            get { return _FoupID; }
            set
            {
                if (_FoupID != value)
                {
                    _FoupID = value;
                    OnPropertyChanged(nameof(_FoupID));
                }
            }
        }
      
        private string _InletValue;

        public string InletValue
        {
            get { return _InletValue; }
            set
            {
                if (_InletValue != value)
                {
                    _InletValue = value;
                    OnPropertyChanged(nameof(_InletValue));
                }
            }
        }
        private string _MFC;
        public string MFC
        {
            get { return _MFC; }
            set
            {
                if (_MFC != value)
                {
                    _MFC = value;
                    OnPropertyChanged(nameof(MFC));
                }
            }
        }
       
       
       
        private int _OutletValue;

        public int OutletValue
        {
            get { return _OutletValue; }
            set
            {
                if (_OutletValue != value)
                {
                    _OutletValue = value;
                    OnPropertyChanged(nameof(OutletValue));
                }
            }
        }

        private int _RH;
        public int RH
        {
            get { return _RH; }
            set
            {
                if (_RH != value)
                {
                    _RH = value;
                    OnPropertyChanged(nameof(RH));
                }
            }
        }


        private bool _Temp;
        public bool Temp
        {
            get { return _Temp; }
            set
            {
                if (_Temp != value)
                {
                    _Temp = value;
                    OnPropertyChanged(nameof(Temp));
                }
            }
        }


        private string _Version;
      
        public string Version
        {
            get { return _Version; }
            set
            {
                if (_Version != value)
                {
                    _Version = value;
                    OnPropertyChanged(nameof(Version));
                }
            }
        }
        private Brush _isInlet;
        private Brush _isOutLet;
        private Brush _isVac;
        public Brush IsInlet
        {
            get { return _isInlet; }
            set
            {
                if (_isInlet != value)
                {
                    _isInlet = value;
                    OnPropertyChanged(nameof(IsInlet));
                }
            }
        }

        public Brush IsOutLet
        {
            get { return _isOutLet; }
            set
            {
                if (_isOutLet != value)
                {
                    _isOutLet = value;
                    OnPropertyChanged(nameof(IsOutLet));
                }
            }
        }
        public Brush IsVac
        {
            get { return _isVac; }
            set
            {
                if (_isVac != value)
                {
                    _isVac = value;
                    OnPropertyChanged(nameof(IsVac));
                }
            }
        }
    }
}
