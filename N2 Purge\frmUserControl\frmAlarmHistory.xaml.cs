﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Controls;

using DBEntity.History;
using LiveCharts;
using LiveCharts.Wpf;
using LiveCharts.Wpf.Charts.Base;

namespace N2Purge.frmUserControl
{
    public partial class frmAlarmHistory : UserControl
    {   
        public SeriesCollection SeriesCollection { get; set; }
        public string[] Labels { get; set; }

        public frmAlarmHistory()
        {
            InitializeComponent();
            InitializeChart();
        }

        private void InitializeChart()
        {
            SeriesCollection = new SeriesCollection();
            Labels = new string[24];  // 默认24小时
            for (int i = 0; i < 24; i++)
            {
                Labels[i] = $"{i:D2}:00";
            }
            chartAlarms.AxisX[0].Labels = Labels;
        }

        private async void btnSearch_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (tmstart.Value != null && tmend.Value != null)
            {
                var startTime = (DateTime)tmstart.Value;
                var endTime = (DateTime)tmend.Value;
                var timeSpan = endTime - startTime;

                var dt = await GlobalData.dbHelper.thAlarmdb.GetAlarmHistoryByTimeRangeAsync(startTime, endTime);
                dgalarm.ItemsSource = dt;

                SeriesCollection.Clear();
                if (timeSpan.TotalDays > 30)
                {
                    // 按月统计
                    var monthlyStats = dt.GroupBy(alarm => new { alarm.Start_Time.Year, alarm.Start_Time.Month })
                                       .OrderBy(g => g.Key.Year).ThenBy(g => g.Key.Month)
                                       .Select(g => new { Date = new DateTime(g.Key.Year, g.Key.Month, 1), Count = g.Count() })
                                       .ToList();

                    var values = new ChartValues<int>();
                    Labels = new string[monthlyStats.Count];

                    for (int i = 0; i < monthlyStats.Count; i++)
                    {
                        values.Add(monthlyStats[i].Count);
                        Labels[i] = monthlyStats[i].Date.ToString("yyyy-MM");
                    }

                    SeriesCollection.Add(new ColumnSeries
                    {
                        Title = "每月报警数量",
                        Values = values
                    });
                }
                else if (timeSpan.TotalDays > 1)
                {
                    // 按天统计
                    var dailyStats = dt.GroupBy(alarm => alarm.Start_Time.Date)
                                     .OrderBy(g => g.Key)
                                     .Select(g => new { Date = g.Key, Count = g.Count() })
                                     .ToList();

                    var values = new ChartValues<int>();
                    Labels = new string[dailyStats.Count];

                    for (int i = 0; i < dailyStats.Count; i++)
                    {
                        values.Add(dailyStats[i].Count);
                        Labels[i] = dailyStats[i].Date.ToString("MM-dd");
                    }

                    SeriesCollection.Add(new ColumnSeries
                    {
                        Title = "每日报警数量",
                        Values = values
                    });
                }
                else
                {
                    // 按小时统计
                    var hourlyStats = dt.GroupBy(alarm => alarm.Start_Time.Hour)
                                      .OrderBy(g => g.Key)
                                      .Select(g => new { Hour = g.Key, Count = g.Count() })
                                      .ToList();

                    var values = new ChartValues<int>();
                    Labels = new string[24];

                    for (int hour = 0; hour < 24; hour++)
                    {
                        var count = hourlyStats.FirstOrDefault(x => x.Hour == hour)?.Count ?? 0;
                        values.Add(count);
                        Labels[hour] = $"{hour:D2}:00";
                    }

                    SeriesCollection.Add(new ColumnSeries
                    {
                        Title = "每小时报警数量",
                        Values = values
                    });
                }

                chartAlarms.AxisX[0].Labels = Labels;
                chartAlarms.Series = SeriesCollection;
            }
        }

        private async void btnExport_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = $"AlarmHistory{DateTime.Now:yyyy_MM_dd_HH-mm-ss}",
                    DefaultExt = ".csv",
                    Filter = "CSV文件 (*.csv)|*.csv"
                };

                if (dialog.ShowDialog() == true)
                {
                    string directory = Path.GetDirectoryName(dialog.FileName);
                    string fileName = Path.GetFileName(dialog.FileName);
                    
                    var dt = await GlobalData.dbHelper.thAlarmdb.GetAlarmHistoryByTimeRangeAsync((DateTime)tmstart.Value, (DateTime)tmend.Value);
                    GlobalData.csvHelper.WriteToCsv(directory, fileName, dt);
                    
                  //  MessageBox.Show($"导出成功：{dialog.FileName}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
               // MessageBox.Show($"导出CSV文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
