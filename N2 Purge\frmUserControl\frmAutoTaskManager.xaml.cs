using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using N2Purge.Services;
using Proj.Log;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmAutoTaskManager.xaml 的交互逻辑
    /// </summary>
    public partial class frmAutoTaskManager : UserControl
    {
        private DispatcherTimer _refreshTimer;
        private DateTime _startTime;
        private StringBuilder _logBuffer;

        public frmAutoTaskManager()
        {
            InitializeComponent();
            _startTime = DateTime.Now;
            _logBuffer = new StringBuilder();
            
            InitializeUI();
            StartRefreshTimer();
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeUI()
        {
            try
            {
                // 设置系统信息
                txtAppPath.Text = Assembly.GetExecutingAssembly().Location;
                txtCurrentUser.Text = $"{Environment.UserDomainName}\\{Environment.UserName}";
                txtStartTime.Text = _startTime.ToString("yyyy-MM-dd HH:mm:ss");
                
                // 检查管理员权限
                bool isAdmin = IsAdministrator();
                txtAdminStatus.Text = isAdmin ? "是" : "否";
                txtAdminStatus.Foreground = isAdmin ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red;

                // 初始状态检查
                RefreshStatus();
                
                AddLog("界面初始化完成");
            }
            catch (Exception ex)
            {
                AddLog($"初始化界面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动刷新定时器
        /// </summary>
        private void StartRefreshTimer()
        {
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(30) // 每30秒刷新一次
            };
            _refreshTimer.Tick += (s, e) => RefreshStatus();
            _refreshTimer.Start();
        }

        /// <summary>
        /// 刷新状态
        /// </summary>
        private async void RefreshStatus()
        {
            try
            {
                txtStatusBar.Text = "正在刷新状态...";
                
                // 异步检查任务状态
                await Task.Run(() =>
                {
                    // 检查计划任务状态
                    bool taskExists = AutoTaskManager.IsTaskExists();
                    
                    // 检查单例状态
                    bool isMainInstance = SimpleSingletonManager.IsMainInstance;
                    var otherInstanceIds = SimpleSingletonManager.GetOtherInstanceIds();
                    
                    // 更新UI
                    Dispatcher.Invoke(() =>
                    {
                        // 更新任务状态
                        if (taskExists)
                        {
                            txtTaskStatus.Text = "✅ 自动任务已启用";
                            txtTaskStatus.Foreground = System.Windows.Media.Brushes.Green;
                            txtTaskDetails.Text = "计划任务 'N2PurgeAutoMonitor' 正在运行，每5分钟执行一次";
                            txtTaskInfo.Text = "任务状态: 已创建并启用";
                            btnCreateTask.IsEnabled = false;
                            btnDeleteTask.IsEnabled = true;
                        }
                        else
                        {
                            txtTaskStatus.Text = "❌ 自动任务未启用";
                            txtTaskStatus.Foreground = System.Windows.Media.Brushes.Red;
                            txtTaskDetails.Text = "未检测到自动监控计划任务，建议创建";
                            txtTaskInfo.Text = "任务状态: 未创建";
                            btnCreateTask.IsEnabled = true;
                            btnDeleteTask.IsEnabled = false;
                        }
                        
                        // 更新单例状态
                        if (isMainInstance)
                        {
                            txtSingletonStatus.Text = $"✅ 单例状态: 主实例 (发现 {otherInstanceIds.Length} 个其他实例)";
                            txtSingletonStatus.Foreground = System.Windows.Media.Brushes.Green;
                            txtInstanceInfo.Text = $"当前为主实例，其他实例数量: {otherInstanceIds.Length}";
                            btnKillOtherInstances.IsEnabled = otherInstanceIds.Length > 0;
                        }
                        else
                        {
                            txtSingletonStatus.Text = "⚠️ 单例状态: 非主实例";
                            txtSingletonStatus.Foreground = System.Windows.Media.Brushes.Orange;
                            txtInstanceInfo.Text = "当前不是主实例";
                            btnKillOtherInstances.IsEnabled = false;
                        }
                        
                        // 更新运行时长
                        TimeSpan runTime = DateTime.Now - _startTime;
                        txtRunTime.Text = $"{runTime.Days}天 {runTime.Hours:D2}:{runTime.Minutes:D2}:{runTime.Seconds:D2}";
                        
                        // 更新最后更新时间
                        txtLastUpdate.Text = $"最后更新: {DateTime.Now:HH:mm:ss}";
                        txtStatusBar.Text = "状态刷新完成";
                    });
                });
            }
            catch (Exception ex)
            {
                txtStatusBar.Text = $"刷新状态失败: {ex.Message}";
                AddLog($"刷新状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void AddLog(string message)
        {
            try
            {
                string logEntry = $"[{DateTime.Now:HH:mm:ss}] {message}";
                _logBuffer.AppendLine(logEntry);
                
                // 保持日志缓冲区大小
                if (_logBuffer.Length > 10000)
                {
                    string content = _logBuffer.ToString();
                    string[] lines = content.Split('\n');
                    _logBuffer.Clear();
                    
                    // 保留最后50行
                    for (int i = Math.Max(0, lines.Length - 50); i < lines.Length; i++)
                    {
                        _logBuffer.AppendLine(lines[i]);
                    }
                }
                
                txtOperationLog.Text = _logBuffer.ToString();
                
                // 滚动到底部
                if (txtOperationLog.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            }
            catch
            {
                // 忽略日志添加失败
            }
        }

        /// <summary>
        /// 检查管理员权限
        /// </summary>
        /// <returns>是否为管理员</returns>
        private bool IsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        #region 事件处理

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            AddLog("手动刷新状态");
            RefreshStatus();
        }

        private async void BtnCreateTask_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddLog("开始创建自动任务");
                btnCreateTask.IsEnabled = false;
                txtStatusBar.Text = "正在创建计划任务...";
                
                bool result = await Task.Run(() => AutoTaskManager.CheckAndSetupAutoTask());
                
                if (result)
                {
                    AddLog("自动任务创建成功");
                    MessageBox.Show("自动任务创建成功！\n\n任务将每5分钟执行一次，确保系统持续监控。", 
                                  "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    AddLog("自动任务创建失败");
                    MessageBox.Show("自动任务创建失败！\n\n请检查是否具有管理员权限，或查看日志了解详细错误信息。", 
                                  "操作失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                
                RefreshStatus();
            }
            catch (Exception ex)
            {
                AddLog($"创建任务异常: {ex.Message}");
                MessageBox.Show($"创建任务时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnCreateTask.IsEnabled = true;
                txtStatusBar.Text = "就绪";
            }
        }

        private async void BtnDeleteTask_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要删除自动监控任务吗？\n\n删除后系统将不会自动启动监控。", 
                                           "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    AddLog("开始删除自动任务");
                    btnDeleteTask.IsEnabled = false;
                    txtStatusBar.Text = "正在删除计划任务...";
                    
                    bool success = await Task.Run(() => AutoTaskManager.DeleteTask());
                    
                    if (success)
                    {
                        AddLog("自动任务删除成功");
                        MessageBox.Show("自动任务删除成功！", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        AddLog("自动任务删除失败");
                        MessageBox.Show("自动任务删除失败！\n\n请检查是否具有管理员权限。", 
                                      "操作失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    
                    RefreshStatus();
                }
            }
            catch (Exception ex)
            {
                AddLog($"删除任务异常: {ex.Message}");
                MessageBox.Show($"删除任务时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnDeleteTask.IsEnabled = true;
                txtStatusBar.Text = "就绪";
            }
        }

        private void BtnOpenTaskScheduler_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddLog("打开任务计划程序");
                Process.Start("taskschd.msc");
            }
            catch (Exception ex)
            {
                AddLog($"打开任务计划程序失败: {ex.Message}");
                MessageBox.Show($"打开任务计划程序失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCheckInstances_Click(object sender, RoutedEventArgs e)
        {
            AddLog("检查应用程序实例");
            RefreshStatus();
        }

        private async void BtnKillOtherInstances_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要终止其他N2Purge实例吗？\n\n这可能会导致数据丢失，请谨慎操作。", 
                                           "确认终止", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.Yes)
                {
                    AddLog("开始终止其他实例");
                    btnKillOtherInstances.IsEnabled = false;
                    txtStatusBar.Text = "正在终止其他实例...";

                    int killedCount = await Task.Run(() => SimpleSingletonManager.KillOtherInstances());
                    bool success = killedCount > 0;
                    
                    if (success)
                    {
                        AddLog($"成功终止 {killedCount} 个其他实例");
                        MessageBox.Show($"成功终止 {killedCount} 个其他实例！", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        AddLog("未发现其他实例或终止失败");
                        MessageBox.Show("未发现其他实例或终止失败！", "操作完成", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    
                    RefreshStatus();
                }
            }
            catch (Exception ex)
            {
                AddLog($"终止其他实例异常: {ex.Message}");
                MessageBox.Show($"终止其他实例时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnKillOtherInstances.IsEnabled = true;
                txtStatusBar.Text = "就绪";
            }
        }

        private void BtnClearLog_Click(object sender, RoutedEventArgs e)
        {
            _logBuffer.Clear();
            txtOperationLog.Text = "";
            AddLog("日志已清除");
        }

        #endregion

        /// <summary>
        /// 控件卸载时清理资源
        /// </summary>
        private void UserControl_Unloaded(object sender, RoutedEventArgs e)
        {
            _refreshTimer?.Stop();
            _refreshTimer = null;
        }
    }
}
