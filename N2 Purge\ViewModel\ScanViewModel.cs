using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace N2Purge.ViewModel
{
    public class ScanViewModel : VmPropertyChange
    {
        private string cassetteId;
        private string sourceLocation;
        private int priority;

        public string CassetteId
        {
            get { return cassetteId; }
            set
            {
                if (cassetteId != value)
                {
                    cassetteId = value;
                    OnPropertyChanged(nameof(CassetteId));
                }
            }
        }

        public string SourceLocation
        {
            get { return sourceLocation; }
            set
            {
                if (sourceLocation != value)
                {
                    sourceLocation = value;
                    OnPropertyChanged(nameof(SourceLocation));
                }
            }
        }

        public int Priority
        {
            get { return priority; }
            set
            {
                if (priority != value)
                {
                    priority = value;
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

        
    }
}