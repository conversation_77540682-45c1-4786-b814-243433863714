﻿using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.ViewModel
{
    public class SingleShelfInfoViewModel:VmPropertyChange
    {
        private string _address;
        private string _location;
        private string _cstState;
        private string _zone;
        private string _comment;

        public string Address
        {
            get => _address;
            set
            {
                _address = value;
                OnPropertyChanged(nameof(Address));
            }
        }

        public string Location
        {
            get => _location;
            set
            {
                _location = value;
                OnPropertyChanged(nameof(Location));
            }
        }

        public string CSTState
        {
            get => _cstState;
            set
            {
                _cstState = value;
                OnPropertyChanged(nameof(CSTState));
            }
        }

        public string Zone
        {
            get => _zone;
            set
            {
                _zone = value;
                OnPropertyChanged(nameof(Zone));
            }
        }

        public string Comment
        {
            get => _comment;
            set
            {
                _comment = value;
                OnPropertyChanged(nameof(Comment));
            }
        }

    }
}
