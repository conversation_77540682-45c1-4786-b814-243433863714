﻿using DBEntity;
using DBEntity.History;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmStatistics.xaml 的交互逻辑
    /// </summary>
    public partial class frmStatistics : UserControl
    {
        public frmStatistics()
        {
            InitializeComponent();
            this.DataContext = GlobalData.gbstatisticsViewModel;
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {

        }
        public async void Search()
        {
            DateTime startTime = (DateTime)dtstart.Value;
            DateTime endTime = (DateTime)dtend.Value;

            var  cranes = await GlobalData.dbHelper.tpCranedb.GetAllTpCraneAsync();
            if (cranes.Count==0)
            {
                return;
            }

            string strCraneName = cranes[0].Name;

            var trlst= await GlobalData.dbHelper.thtransferdb.GetTransferHistoryByTimeRangeAsync(startTime, endTime);
            int iTransferCount =trlst.Count;
            var alarmList =await GlobalData.dbHelper.thAlarmdb.GetAlarmHistoryByTimeRangeAsync(startTime, endTime);
            alarmList.Where(x => x.Unit == strCraneName);
            double dAlarmTotalTime = 0;  //报警总时间(小时)
            int iAlarmCount = 0;
            int iFailureCount = 0;
            if (alarmList.Count > 0)
            {
                DateTime lastAlarmClearTime = (DateTime)alarmList[0].Clear_Time;
                iAlarmCount = 1;

                foreach (AlarmHistory alarm in alarmList)
                {
                    DateTime alarmStartTime = (DateTime)alarm.Start_Time;
                    DateTime alarmClearTime = alarmStartTime;
                    if (alarm.Clear_Time != null)
                    {
                        alarmClearTime = (DateTime)alarm.Clear_Time;
                    }
                    double alarmTime = (alarmClearTime - alarmStartTime).TotalHours;
                    if (alarmTime > 0.1)  //大于6分钟
                    {
                        if (alarmStartTime > lastAlarmClearTime)  //去除时间重叠的Alarm
                        {
                            dAlarmTotalTime += alarmTime;
                            iAlarmCount++;
                            lastAlarmClearTime = alarmClearTime;

                            if (alarmTime > 0.5)  //大于30分钟
                            {
                                iFailureCount++;
                            }
                        }
                    }
                }
            }
            double dTotalTime = (endTime - startTime).TotalHours;

            GlobalData.gbstatisticsViewModel.TransportCount = iTransferCount;
            GlobalData.gbstatisticsViewModel.FailureCount = iFailureCount;
            GlobalData.gbstatisticsViewModel.AlarmCount = iAlarmCount;
            GlobalData.gbstatisticsViewModel.QueryTime= dTotalTime.ToString("0");
            GlobalData.gbstatisticsViewModel.AlarmDuration= dAlarmTotalTime.ToString("0");

            int iMCBF = 0;
            int iMCBA = 0;
            int iMTBF = 0;
            int iMTTR = 0;

            if (iFailureCount > 0)
            {
                iMCBF = iTransferCount / iFailureCount;
                iMTBF = Convert.ToInt32(dTotalTime / iFailureCount);
            }
            else
            {
                iMCBF = iTransferCount;
                iMTBF = Convert.ToInt32(dTotalTime);
            }

            if (iAlarmCount > 0)
            {
                iMCBA = iTransferCount / iAlarmCount;
                iMTTR = Convert.ToInt32(dAlarmTotalTime / iAlarmCount);
            }
            else
            {
                iMCBA = iTransferCount;
                iMTTR = 0;
            }

            GlobalData.gbstatisticsViewModel.MCBF = iMCBF;
            GlobalData.gbstatisticsViewModel.MCBA  = iMCBA;
            GlobalData.gbstatisticsViewModel.MTBF  = iMTBF;
            GlobalData.gbstatisticsViewModel.MTTR  = iMTTR;
            GlobalData.gbstatisticsViewModel.UpTime  = ((dTotalTime - dAlarmTotalTime) / dTotalTime * 100).ToString("0.00") + " %";
        }
        public async void QueryRunRate()
        {
            DateTime startTime = (DateTime)dateRunRate.Value;
            DateTime endTime = startTime.AddDays(1);
            var trlst = await GlobalData.dbHelper.thtransferdb.GetTransferHistoryByTimeRangeAsync(startTime, endTime);
            double craneTotalActiveTime = 0;
            foreach (TransferHistory transfer in trlst)
            {
                if (transfer.Crane_Start_Time != null && transfer.Crane_End_Time != null)
                {
                    double activeTime = ((DateTime)transfer.Crane_End_Time - (DateTime)transfer.Crane_Start_Time).TotalMinutes;
                    craneTotalActiveTime += activeTime;
                }
            }
            double totalTime = (endTime - startTime).TotalMinutes;
            double runRate = craneTotalActiveTime / totalTime;
            GlobalData.gbstatisticsViewModel.UtilizationRate = (runRate * 100).ToString("0.00") + " %";
        }
    }
}
