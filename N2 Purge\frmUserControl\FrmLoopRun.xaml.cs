﻿using DBEntity;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Xceed.Wpf.Toolkit;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// FrmLoopRun.xaml 的交互逻辑
    /// </summary>
    public partial class FrmLoopRun : UserControl
    {
        public FrmLoopRun()
        {
            InitializeComponent();
        }
        private async void InitData()
        {
            try
            {
                var carrierlst = await GlobalData.dbHelper.tpCarrierdb.GetAllTpCarrierAsync();
                if (carrierlst != null)
                {
                    foreach (TpCarrier tpCarrier in carrierlst)
                    {
                        cmbCarrierID.Items.Add(tpCarrier.Id);
                        cmbCarrierToUse.Items.Add(tpCarrier.Id);
                    }

                }
                cmbCycleMode.Items.Add("Normal");
                cmbCycleMode.SelectedIndex = 0;
                cmbCycleMode.Items.Add("EmptyCheck");
                cmbCycleMode.SelectedIndex = 1;
                cmbCycleMode.Items.Add("PickLocationCheck");
                cmbCycleMode.SelectedIndex = 2;
                cmbCycleMode.Items.Add("PlaceLocationCheck");
                cmbCycleMode.SelectedIndex = 3;
                cmbCycleMode.Items.Add("DoubleCheck");
                cmbCycleMode.SelectedIndex = 4;

                cmbCycleMode.SelectedIndex = 0;
                List<TpCyclecarrier> cycCarrierlst = await GlobalData.dbHelper.tpcyclecarrierdb.GetAllTpCyclecarrierAsync();
                if (cycCarrierlst.Count != 0)
                {
                    TpCyclecarrier cycCarrier = cycCarrierlst[0];
                    cmbCarrierID.SelectedIndex = cmbCarrierID.Items.IndexOf(cycCarrier.Carrier_ID);
                    numericCycleTimes.Text = ((decimal)cycCarrier.Cycle_Times).ToString();
                    if (cycCarrier.Use_Port == 1)
                    {
                        chkIsUsePort.IsChecked = true;
                        rbnPortB.IsChecked = rbnPortB.Content == cycCarrier.Port_Name;
                        rbnPortA.IsEnabled = true;
                        rbnPortB.IsEnabled = true;
                    }
                }
                var selectedlocList=await GlobalData.dbHelper.tpcycleLocdb.GetAllTpCyclelocationAsync();
                foreach (var loc in selectedlocList)
                {
                    lstSelectedLocation.Items.Add(loc.Location);
                }
                //获取可用位置
                var tplocs = await GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync();
                if (tplocs.Count != 0)
                {
                    var tploclst = tplocs.Where(x => x.Is_Occupied != 1).Where(t => t.Is_Reserved != 1).Where(z => z.Is_Reserved != 1).ToList();
                    foreach (TpLocation location in tploclst)
                    {
                        if (!lstSelectedLocation.Items.Contains(location.Address) && 
                            !lstAllLocation.Items.Contains(location.Address))
                        {
                            lstAllLocation.Items.Add(location.Address);
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                throw;
            }
         
        }
        private void MoveRight()
        {
            var items = this.lstAllLocation.SelectedItems;
            List<object> itemList = new List<object>();
            if (items != null)
            {
                foreach (var item in items)
                {
                    itemList.Add(item);
                }
            }
            foreach (var item in itemList)
            {
                this.lstSelectedLocation.Items.Add(item);
                this.lstAllLocation.Items.Remove(item);
            }
        }

        private void MoveLeft()
        {
            var items = this.lstSelectedLocation.SelectedItems;
            List<object> itemList = new List<object>();
            if (items != null)
            {
                foreach (var item in items)
                {
                    itemList.Add(item);
                }
            }
            foreach (var item in itemList)
            {
                this.lstAllLocation.Items.Add(item);
                this.lstSelectedLocation.Items.Remove(item);
            }
        }

        private void btnMoveRight_Click(object sender, RoutedEventArgs e)
        {
            MoveRight();
        }

        private void btnMoveLeft_Click(object sender, RoutedEventArgs e)
        {
            MoveLeft();
        }

        private  async void btnSelectAll_Click(object sender, RoutedEventArgs e)
        {
            var tplocs = await GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync();
            if (tplocs.Count != 0)
            {
                var tploclst = tplocs.Where(x => x.Is_Occupied != 1).Where(t => t.Is_Reserved != 1).Where(z => z.Is_Reserved != 1).Where(a=>a.Type=="2").ToList();
                foreach (TpLocation location in tploclst)
                {
                    if (!lstSelectedLocation.Items.Contains(location.Address) &&
                          !lstAllLocation.Items.Contains(location.Address))
                    {
                        lstAllLocation.Items.Add(location.Address);
                    }
                }
            }
        }

        private async void btnClear_Click(object sender, RoutedEventArgs e)
        {
            lstSelectedLocation.Items.Clear(); var tplocs = await GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync();
            if (tplocs.Count == 0)
            {
                return;
            }
            var locList = tplocs.Where(x => x.Is_Occupied != 1).Where(t => t.Is_Reserved != 1).Where(z => z.Is_Reserved != 1).ToList();
            foreach (TpLocation location in locList)
            {
                if (lstSelectedLocation.Items.Contains(location.Address) == false)
                {
                    lstAllLocation.Items.Add(location.Address);
                }
            }
        }

        private async void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (await SaveCycleTestInfo())
            {
                System.Windows.MessageBox.Show("Save sucessed.");
            }
            else
            {
                System.Windows.MessageBox.Show("Save failed.");
            }
        }
        public async Task<bool> SaveCycleTestInfo()
        {
            int count = 0;
            int.TryParse(numericCycleTimes.Text, out count);
            if (count < 1)
            {
                System.Windows.MessageBox.Show("Please set the Cycle Times!");
                return false;
            }
            if (lstSelectedLocation.Items.Count < 1)
            {
                System.Windows.MessageBox.Show("Please set the LoopList!");
                return false;
            }
            if (cmbCarrierID.Text == "")
            {
                System.Windows.MessageBox.Show("Please input the Carrier ID.");
                return false;
            }

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("CARRIERID", cmbCarrierID.Text);
            dicParams.Add("USEPORT", chkIsUsePort.IsChecked);
            dicParams.Add("PORTNAME", ((bool)rbnPortA.IsChecked ? rbnPortA.Content.ToString() : rbnPortB.Content.ToString()));
            dicParams.Add("CYCLETIMES", int.Parse(this.numericCycleTimes.Text.ToString()));
            List<string> locationList = getListSource(this.lstSelectedLocation);
            string strLocations = "";
            for (int i = 0; i < locationList.Count; i++)
            {
                if (i == locationList.Count - 1)
                {
                    strLocations = strLocations + locationList[i];
                }
                else
                {
                    strLocations = strLocations + locationList[i] + ";";
                }
            }
            dicParams.Add("LOCATIONS", strLocations);
            object obj =await WCFClient.Instance.SendMessage("SetLoopTest", dicParams);
            bool res = true;
            if (obj == null || !bool.TryParse(obj.ToString(), out res))
            {
                res = false;
            }
            if (res)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        private List<string> getListSource(ListBox listBox)
        {
            List<string> strList = new List<string>();
            foreach (var item in listBox.Items)
            {
                strList.Add(item.ToString());
            }
            return strList;
        }
        private async void btnStart_Click(object sender, RoutedEventArgs e)
        {
            //判断系统状态是否满足
            Dictionary<string, object> stateParams = new Dictionary<string, object>();
            stateParams.Add("NAME", "SC Mode");
            var strSCMode =await WCFClient.Instance.SendMessage("GetState", stateParams);
            //stateParams["NAME"] = "Control State";
            //string strControlMode = WCFClient.Instance.SendMessage("GetState", stateParams).ToString();
            if (strSCMode.ToString() != "Test")
            {
                System.Windows.MessageBox.Show("Please switch SC Mode to 'Test'.");
                return;
            }

            //提示是否要开始循环，防止误操作
            MessageBoxResult ret = System.Windows.MessageBox.Show("Are you sure to start loop test?", "Prompt", MessageBoxButton.YesNo, MessageBoxImage.Information);
            if (ret == MessageBoxResult.No)
            {
                return;
            }

            //保存循环配置
            if (!await SaveCycleTestInfo())
            {
                System.Windows.MessageBox.Show("Save failed.");
            }

            //判断各位置是否可用，如果有不可用位置则弹出提示
            List<string> availableLocations = new List<string>(); //全部可用位置列表
            var tplocs = await GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync();
            if (tplocs.Count == 0)
            {
                return;
            }
            var locList = tplocs.Where(x => x.Is_Occupied != 1).Where(t => t.Is_Reserved != 1).Where(z => z.Is_Reserved != 1).ToList();
            foreach (TpLocation location in locList)
            {
                availableLocations.Add(location.Address);
            }

            List<string> invalidLocations = new List<string>();  //被选中的位置中，无效位置列表
            foreach (string selectedLoc in lstSelectedLocation.Items)
            {
                if (availableLocations.Contains(selectedLoc) == false)
                {
                    invalidLocations.Add(selectedLoc);
                }
            }
            if (invalidLocations.Count > 0)
            {
                string strMessage = "There are invalid locations: ";
                foreach (string loc in invalidLocations)
                {
                    strMessage = strMessage + loc + ", ";
                }
                strMessage = strMessage + "please change the settings.";
                System.Windows.MessageBox.Show(strMessage);
                return;
            }

            string strCarrierToUse = cmbCarrierToUse.Text;
            int nCycleMode = cmbCycleMode.SelectedIndex;
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("CarrierToUse", strCarrierToUse);
            dicParams.Add("CycleMode", nCycleMode);
           await WCFClient.Instance.SendMessage("StartLoopTest", dicParams);
        }

        private async void btnStop_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult ret = System.Windows.MessageBox.Show("Are you sure to stop loop test?", "Prompt", MessageBoxButton.YesNo, MessageBoxImage.Information);
            if (ret == MessageBoxResult.No)
            {
                return;
            }

            //TpCyclecarrierList.DeleteByCriteria(x => x.CarrierId != "");

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
           await WCFClient.Instance.SendMessage("StopLoopTest", dicParams);
        }
        private bool isload;
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (isload) { return; }
            InitData();
            isload = true;
        }

       
    }
}
