﻿<UserControl x:Class="N2Purge.userControls.PurgeShelfInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="300">
    <Grid Background="Transparent">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="2*"/>
        </Grid.RowDefinitions>
        <Label Content="ShelfID"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue" Margin="0,0,0,0" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="0" Grid.Column="0"/>
        <Label Content="Status"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"   TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="1" Grid.Column="0"/>
        <Label Content="FoupType"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="2" Grid.Column="0"/>
        <Label Content="FoupID"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="3" Grid.Column="0"/>
        <Label Content="Inlet"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="4" Grid.Column="0"/>
        <Label Content="MFC"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="5" Grid.Column="0"/>
        <Label Content="Outlet"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="6" Grid.Column="0"/>
        <Label Content="RH(%)"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="7" Grid.Column="0"/>
        <Label Content="Temp"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="8" Grid.Column="0"/>
        <Label Content="Version"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Background="AliceBlue"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="9" Grid.Column="0"/>
        <Label Content="{Binding ShelfId }" Background="AliceBlue"  HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Margin="0,0,0,0" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="0" Grid.Column="1"/>
        <Label Content="{Binding Status }" Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"   TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1" Grid.Column="1"/>
        <Label Content="{Binding FoupType}" Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="2" Grid.Column="1"/>
        <Label Content="{Binding FoupID}" Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="3" Grid.Column="1"/>
        <Label Content="{Binding InletValue}"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="4" Grid.Column="1"/>
        <Label Content="{Binding MFC}" Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="5" Grid.Column="1"/>
        <Label Content="{Binding OutletValue}" Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="6" Grid.Column="1"/>
        <Label Content="{Binding RH}"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="7" Grid.Column="1"/>
        <Label Content="{Binding Temp}"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="8" Grid.Column="1"/>
        <Label Content="{Binding Version}"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="9" Grid.Column="1"/>
        <Label Content="0" TextBlock.FontSize="20" 
       HorizontalAlignment="Stretch" VerticalAlignment="Stretch" 
       Background="Green" Foreground="Yellow" 
       HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
       Grid.Row="10" Grid.Column="0"/>
        <Button Content="Purge On" Background="AliceBlue" TextBlock.FontSize="20" Grid.Column="1" Grid.Row="10"/>
        <Border x:Name="bd" Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="11"  BorderThickness="1" BorderBrush="Black">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>
                <Label Content="Inlet"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="0" Grid.Column="0"/>
                <Label Content="Outlet"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="0" Grid.Column="1"/>
                <Label Content="Vac"  Background="AliceBlue" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Center" Grid.Row="0" Grid.Column="2"/>
                <Label  Background="{Binding IsInlet}"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1" Grid.Column="0"/>
                <Label  Background="{Binding IsOutLet}"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1" Grid.Column="1"/>
                <Label  Background="{Binding IsVac}"  TextBlock.FontSize="20" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1" Grid.Column="2"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
