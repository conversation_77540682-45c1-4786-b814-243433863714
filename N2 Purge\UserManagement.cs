using DBEntity;
using N2Purge.frmUserControl;
using System;
using System.Threading.Tasks;

namespace N2Purge
{
    public class UserManagement
    {
        private readonly DatabaseHelper<UserHistory> _dbHelper;
        public int _currentUserLevel = 0;

        public UserManagement(string connectionString)
        {
            _dbHelper = new DatabaseHelper<UserHistory>(connectionString);
        }
        public void LogOut()
        {
            _currentUserLevel = 0;
        }
        public async Task<bool> Login(string userId, string password)
        {
            var user = await _dbHelper.QueryFirstAsync($"UserName = '{userId}' AND Password = '{password}'");
            if (user != null)
            {
                user.LastLoginTime = DateTime.Now;
                await _dbHelper.UpdateAsync(user, $"Id = {user.Id}");
                _currentUserLevel = user.UserLevel;
                return true;
            }
            return false;
        }

        public async Task<bool> AddUser(UserHistory newUser, int operatorLevel)
        {
            if (operatorLevel < 2) return false; // 只有管理员和超级管理员可以添加用户

            if (newUser != null)
            {
                var existingUser = await _dbHelper.QueryFirstAsync($"UserId = '{newUser.UserId}'");
                if (existingUser == null)
                {
                    newUser.CreateTime = DateTime.Now;
                    newUser.UpdateTime = DateTime.Now;
                    await _dbHelper.AddAsync(newUser);
                    return true;
                }
            }
            return false;
        }

        public async Task<bool> UpdateUser(UserHistory updatedUser, int operatorLevel)
        {
            if (operatorLevel < 2) return false; // 只有管理员和超级管理员可以更新用户

            var user = await _dbHelper.QueryFirstAsync($"Id = {updatedUser.Id}");
            if (user != null)
            {
                // 普通管理员不能修改超级管理员的信息
                if (operatorLevel == 2 && user.UserLevel == 3) return false;

                user.UserName = updatedUser.UserName;
                user.Password = updatedUser.Password;
                user.UpdateTime = DateTime.Now;
                await _dbHelper.UpdateAsync(user, $"Id = {user.Id}");
                return true;
            }
            return false;
        }

        public async Task<bool> DeleteUser(string UserName, int operatorLevel)
        {
            if (operatorLevel < 3) return false; // 只有超级管理员可以删除用户

            var user = await _dbHelper.QueryFirstAsync($"UserName = '{UserName}'");
            if (user != null)
            {
                await _dbHelper.DeleteAsync($"UserName = '{UserName}'");
                return true;
            }
            return false;
        }

        public bool HasPermissionToDelete(int userLevel)
        {
            return userLevel == 3; // 只有超级管理员有删除权限
        }

        public bool CheckOperationPermission(int requiredLevel)
        {
#if DEBUG
            return true;
#endif
            if (_currentUserLevel < requiredLevel)
            {
                System.Windows.MessageBox.Show("您的权限不足，无法执行此操作。\r\nInsufficient permissions to perform this operation.");
                return false;
            }
            return true;
        }

        public async Task<List<UserHistory>> GetUsersByUserNameAsync(string userName)
        {
            return await _dbHelper.QueryAsync($"UserName = '{userName}'");
        }

        public async Task<List<UserHistory>> GetAllUsersAsync()
        {
            return await _dbHelper.QueryAsync();
        }

        public async Task<List<string>> GetAllUserNames()
        {
            var users = await GetAllUsersAsync();
            return users.Select(u => u.UserName)
                       .Where(name => !string.IsNullOrWhiteSpace(name))
                       .Distinct()
                       .OrderBy(name => name)
                       .ToList();
        }
    }
}