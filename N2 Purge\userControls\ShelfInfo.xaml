﻿<UserControl x:Class="N2Purge.userControls.ShelfInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:N2Purge.userControls"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="800">
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>
        <Label Content="Address" Grid.Row="0" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="Location State" Grid.Row="1" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="CST State" Grid.Row="2" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="Install Time" Grid.Row="3" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="Comment" Grid.Row="4" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding Address}" Grid.Column="1" Grid.Row="0" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding LocationState}" Grid.Column="1" Grid.Row="1" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding CSTState}" Grid.Column="1" Grid.Row="2" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding Zone}" Grid.Column="1" Grid.Row="3" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
        <Label Content="{Binding Comment}" Grid.Column="1" Grid.Row="4" TextBlock.FontSize="15" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
    </Grid>
</UserControl>
