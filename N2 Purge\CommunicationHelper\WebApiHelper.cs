﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;

namespace N2Purge.CommunicationHelper
{
    public class WebApiHelper
    {
        private readonly HttpClient _httpClient;

        public WebApiHelper()
        {
            _httpClient = new HttpClient();
        }

        public async Task<T> GetAsync<T>(string url)
        {
            HttpResponseMessage response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            string jsonResponse = await response.Content.ReadAsStringAsync();
            // 反序列化可能返回null，需要进行空值检查
            var result = JsonConvert.DeserializeObject<T>(jsonResponse);
            if (result == null)
            {
                throw new InvalidOperationException("反序列化结果为空");
            }
            return result;
        }

        public async Task<string> PostAsync(string url, object data)
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            HttpResponseMessage response = await _httpClient.PostAsync(url, content);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
    }
}
