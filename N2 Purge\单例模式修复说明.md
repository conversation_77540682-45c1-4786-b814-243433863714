# N2 Purge 单例模式修复说明

## 🔍 问题分析

之前的单例模式没有生效，主要原因包括：

1. **Debug模式覆盖问题**: 在Debug模式下，代码强制设置`_isMainInstance = true`，覆盖了单例检查结果
2. **逻辑混乱**: 单例检查和网络检测的逻辑混在一起，相互干扰
3. **Mutex名称问题**: 使用基于路径哈希的Mutex名称可能导致不一致
4. **检查时机问题**: 在WPF应用程序构造函数中进行检查可能太晚

## 🔧 修复方案

### 1. 创建简单单例管理器

**文件**: `Services\SimpleSingletonManager.cs`

- 使用固定的Mutex名称: `"Global\\N2Purge_SingleInstance_2024"`
- 简化逻辑，专注于单例检查
- 添加详细的调试输出和日志记录
- 支持异常处理和资源清理

### 2. 修改应用程序启动逻辑

**文件**: `App.xaml.cs`

- 将单例检查移到`OnStartup`方法中，确保在UI创建之前执行
- 移除Debug模式的强制覆盖逻辑
- 使用`this.Shutdown(0)`而不是`Environment.Exit(0)`来优雅退出
- 简化构造函数，只做基本初始化

### 3. 关键代码修改

#### 单例检查逻辑
```csharp
public static bool CheckSingleInstance()
{
    try
    {
        // 尝试创建命名Mutex
        bool createdNew;
        _mutex = new Mutex(true, _mutexName, out createdNew);
        
        if (createdNew)
        {
            // 成功创建新的Mutex，说明是第一个实例
            _isMainInstance = true;
            return true;
        }
        else
        {
            // Mutex已存在，尝试获取所有权
            bool acquired = _mutex.WaitOne(3000, false);
            if (acquired)
            {
                _isMainInstance = true;
                return true;
            }
            else
            {
                _isMainInstance = false;
                return false;
            }
        }
    }
    catch (AbandonedMutexException)
    {
        // 前一个实例异常退出，当前实例成为主实例
        _isMainInstance = true;
        return true;
    }
    catch (Exception)
    {
        // 异常情况下使用进程列表检查
        return CheckByProcessList();
    }
}
```

#### 应用程序启动检查
```csharp
protected override void OnStartup(StartupEventArgs e)
{
    // 在任何UI创建之前进行单例检查
    PerformSingletonCheck();
    
    // 如果通过单例检查，继续正常启动
    base.OnStartup(e);
}

private void PerformSingletonCheck()
{
    // 使用简单单例管理器进行检查
    bool isMainInstance = SimpleSingletonManager.CheckSingleInstance();
    
    if (!isMainInstance)
    {
        // 显示提示信息并退出
        MessageBox.Show("检测到N2Purge已在运行中！\n\n当前实例将自动退出。", 
                      "程序已运行", 
                      MessageBoxButton.OK, 
                      MessageBoxImage.Information);
        this.Shutdown(0);
        return;
    }
    
    // 继续其他初始化...
}
```

## 🧪 测试工具

### 1. 批处理测试脚本
**文件**: `Scripts\TestSingleton.bat`
- 自动启动两个实例进行测试
- 检查运行中的实例数量
- 提供详细的测试结果分析

### 2. 控制台测试程序
**文件**: `Scripts\SingletonTest.cs`
- 独立的C#控制台程序
- 使用相同的Mutex逻辑进行测试
- 详细的调试输出

### 3. 编译测试脚本
**文件**: `Scripts\CompileAndTestSingleton.bat`
- 自动编译控制台测试程序
- 启动多个实例进行验证
- 提供测试结果观察指南

## 🔍 调试功能

### 1. 详细日志记录
```csharp
Logger.Instance?.OperationLog($"[简单单例] 开始检查单例，Mutex名称: {_mutexName}");
Logger.Instance?.OperationLog("[简单单例] ✅ 成功创建Mutex，当前为主实例");
Logger.Instance?.OperationLog("[简单单例] ❌ 无法获取Mutex所有权，其他实例正在运行");
```

### 2. 控制台调试输出
```csharp
Console.WriteLine($"[DEBUG] 开始单例检查，Mutex: {_mutexName}");
Console.WriteLine($"[DEBUG] Mutex创建结果: createdNew={createdNew}");
Console.WriteLine("[DEBUG] ✅ 当前为主实例");
```

### 3. 进程列表备用检查
当Mutex检查失败时，自动使用进程列表进行二次验证：
```csharp
private static bool CheckByProcessList()
{
    // 获取相同名称和路径的进程
    // 统计除当前进程外的实例数量
    // 返回是否为唯一实例
}
```

## 📋 测试步骤

### 1. 快速测试
```batch
# 运行批处理测试脚本
Scripts\TestSingleton.bat
```

### 2. 详细测试
```batch
# 编译并运行控制台测试
Scripts\CompileAndTestSingleton.bat
```

### 3. 手动测试
1. 启动第一个N2Purge实例
2. 等待完全加载（约5秒）
3. 启动第二个N2Purge实例
4. 观察第二个实例是否自动退出

## ✅ 预期结果

### 正常情况
- **第一个实例**: 正常启动并显示主界面
- **第二个实例**: 显示"程序已运行"提示框后自动退出
- **进程数量**: 只有1个N2Purge.exe进程在运行

### 异常情况处理
- **Mutex创建失败**: 自动降级到进程列表检查
- **前实例异常退出**: 检测到遗弃Mutex，当前实例成为主实例
- **权限问题**: 记录详细错误信息，默认允许运行

## 🔧 故障排除

### 1. 单例仍然不生效
- 检查日志文件中的详细错误信息
- 运行控制台测试程序查看Mutex创建过程
- 确认防病毒软件没有阻止Mutex创建

### 2. 程序无法启动
- 检查是否有权限问题
- 查看Windows事件查看器中的应用程序错误
- 尝试以管理员身份运行

### 3. 调试信息查看
- 日志文件位置: `应用程序目录\Logs\`
- 控制台输出: 在Debug模式下可见
- 进程监控: 使用任务管理器查看进程数量

## 📈 性能影响

- **启动延迟**: 增加约1-3秒的单例检查时间
- **内存占用**: 增加约1-2MB的Mutex和检查逻辑
- **CPU使用**: 检查过程中短暂的CPU使用，之后无影响

## 🔄 后续维护

1. **定期测试**: 在每次重大更新后测试单例功能
2. **日志监控**: 定期检查单例相关的日志记录
3. **用户反馈**: 收集用户关于多实例问题的反馈

修复后的单例模式应该能够可靠地防止多个N2Purge实例同时运行，确保系统的稳定性和数据一致性。
