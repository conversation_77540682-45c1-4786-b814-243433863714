﻿using Microsoft.VisualBasic;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.frmUserControl
{
    /// <summary>
    /// frmSetting.xaml 的交互逻辑
    /// </summary>
    public partial class frmSetting : UserControl
    {
        public frmSetting()
        {
            InitializeComponent();
            this.DataContext = GlobalData.gbsettingvm;
        }

        private void RadioButton_Checked(object sender, RoutedEventArgs e)
        {

        }
        string m_strValue = "";
        private async void btnconfirm_Click(object sender, RoutedEventArgs e)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("NAME", "Control State");
            var strStatet =await WCFClient.Instance.SendMessage("GetState", dicParams);
            string strState=strStatet.ToString();
            MessageBoxResult dr = MessageBox.Show("Change to " + strState + " ?", "Tips", MessageBoxButton.OKCancel);//Online/Offline 二次确认
            if (dr != MessageBoxResult.OK)
            {
                return;
            }

            if (rbon.IsChecked==true)
            {
                dicParams.Add("VALUE", "Online");
            }
            if (rboff.IsChecked==true)
            {
                dicParams.Add("VALUE", "Offline");
            }
           await  WCFClient.Instance.SendMessage("ChangeState", dicParams);
           
        }

        private async void btnconfirm1_Click(object sender, RoutedEventArgs e)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("NAME", "Control State");
            var strStatet = await WCFClient.Instance.SendMessage("GetState", dicParams);
            string strState = strStatet.ToString();
            if (strState == "Offline")
            {
                MessageBox.Show("Please switch to online first.");
                return;
            }
            MessageBoxResult dr = MessageBox.Show("Change to " + strState + " ?", "Tips", MessageBoxButton.OKCancel);//Online/Offline 二次确认
            if (dr != MessageBoxResult.OK)
            {
                return;
            }
            if (rbloc.IsChecked==true)
            {
                dicParams.Add("VALUE", "OnlineLocal");
            }
            if (rbremote.IsChecked==true)
            {
                dicParams.Add("VALUE", "OnlineRemote");
            }
            await WCFClient.Instance.SendMessage("ChangeState", dicParams);
        }

        private async void btnconfirm2_Click(object sender, RoutedEventArgs e)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("NAME", "SC State");
            var strStatet = await WCFClient.Instance.SendMessage("GetState", dicParams);
            string strState = strStatet.ToString();
            MessageBoxResult dr = MessageBox.Show("Change to " + strState + " ?", "Tips", MessageBoxButton.OKCancel);//SC State 二次确认
            if (dr != MessageBoxResult.OK)
            {
                return;
            }
            if (IsAuto.IsChecked==true)
            {
                dicParams.Add("VALUE", "Auto");
            }
            if (IsPause.IsChecked==true)
            {
                dicParams.Add("VALUE", "Pause");
            }
            bool bResult = Convert.ToBoolean(WCFClient.Instance.SendMessage("ChangeState", dicParams));
            if (bResult)
            {
               // ucTopStateMonitor.SetValue("SC State", frmState.m_strValue);
            }
        }

        private async void btnconfirm3_Click(object sender, RoutedEventArgs e)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("NAME", "SC Mode");
            object strStatet =await WCFClient.Instance.SendMessage("GetState", dicParams);
            string strState=string.Empty;
            if (IsNormal.IsChecked==true)
            {
                strState = IsNormal.Content.ToString();
                dicParams.Add("VALUE", strState);
            }
            if (IsMaint.IsChecked==true)
            {
                strState = IsMaint.Content.ToString();
                dicParams.Add("VALUE", strState);
            }
            if (IsTest.IsChecked==true)
            {
                strState = IsTest.Content.ToString();
                dicParams.Add("VALUE", strState);
            }
            bool bResult = Convert.ToBoolean(await WCFClient.Instance.SendMessage("ChangeState", dicParams));
        }
    }
}
