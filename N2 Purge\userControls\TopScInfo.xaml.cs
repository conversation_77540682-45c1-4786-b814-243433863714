﻿ using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using DBEntity;
using N2Purge.frmUserControl;
using Proj.WCF;

namespace N2Purge.userControls
{
    /// <summary>
    /// TopScInfo.xaml 的交互逻辑
    /// </summary>
    public partial class TopScInfo : UserControl
    {
        private DispatcherTimer timer;
        private DispatcherTimer m_timerTopState;
        public TopScInfo()
        {
            InitializeComponent();
        }

        private void btnRun_Click(object sender, RoutedEventArgs e)
        {
            if (!GlobalData.dbHelper._userManagement.CheckOperationPermission(2))
            {
                return;
            }
            Proj.Log.Logger.Instance.OperationLog("Click top button: Auto(Resume)");

            MessageBoxResult dr = MessageBox.Show("The system will run ?", "Tips", MessageBoxButton.OKCancel);//Run 二次确认
            if (dr != MessageBoxResult.OK)
            {
                return;
            }

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            object objResult = WCFClient.Instance.SendMessage("Resume", dicParams);
            bool bResult = true;
            if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
            {
                bResult = false;
            }
            if (bResult)
            {
                MessageBox.Show("Submit resume successfully");
            }
            else
            {
                MessageBox.Show("Submit resume failure");
            }
            //GlobalData.gbPortViewModels["30101"].Lp.PortColor = 1;
        }

        private void btnPause_Click(object sender, RoutedEventArgs e)
        {
            if (!GlobalData.dbHelper._userManagement.CheckOperationPermission(2))
            {
                return;
            }
            Proj.Log.Logger.Instance.OperationLog("Click top button: Pause");

            MessageBoxResult dr = MessageBox.Show("The system will pause ?", "Tips", MessageBoxButton.OKCancel);//Pause 二次确认
            if (dr != MessageBoxResult.OK)
            {
                return;
            }

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            object objResult = WCFClient.Instance.SendMessage("Pause", dicParams);
            bool bResult = true;
            if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
            {
                bResult = false;
            }
            if (bResult)
            {
                MessageBox.Show("Submit pause successfully");
            }
            else
            {
                MessageBox.Show("Submit pause failure");
            }
            //GlobalData.gbPortViewModels["30101"].IsEnable = true;
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            GlobalData.gbtopScInfovm.Alarm = "Normal";
            GlobalData.gbtopScInfovm.Server =1;
            GlobalData.gbtopScInfovm.HSMSState = "NotConnected";
            GlobalData.gbtopScInfovm.MCS = "Disabled";
            GlobalData.gbtopScInfovm.ControlState = "EqOffline";
            GlobalData.gbtopScInfovm.ScState = "None";
            GlobalData.gbtopScInfovm.ScMode = "Normal";
            GlobalData.gbtopScInfovm.MachineState = "None";
            this.DataContext = GlobalData.gbtopScInfovm;
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1); // 每秒更新一次
            timer.Tick += Timer_Tick;
            timer.Start();
            m_timerTopState = new DispatcherTimer();
            m_timerTopState.Interval = TimeSpan.FromSeconds(1); // 每秒更新一次
            m_timerTopState.Tick += M_timerTopState_Tick; ;
            m_timerTopState.Start();
            //string imagePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Image", "OIP.jpg");
            //if (System.IO.File.Exists(imagePath))
            //{
            //    myImage.Source = new BitmapImage(new Uri(imagePath));
            //}
            //else
            //{
            //    MessageBox.Show("Image not found!");
            //}
        }
        private async Task GetAllTopState()
        {
            if (WCFClient.Instance.IsConnected())
            {
                GlobalData.gbtopScInfovm.Server = 1;
            }
            else
            {
                GlobalData.gbtopScInfovm.Server = 0;
                return;
            }

            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            string strStates =(await WCFClient.Instance.SendMessage("GetAllState", dicParams)).ToString();
            string[] statesArry = strStates.Split(';');
            if (statesArry.Length < 6)
            {
                return;
            }
            GlobalData.gbtopScInfovm.HSMSState = statesArry[0].Split(',')[1];
            GlobalData.gbtopScInfovm.MCS = statesArry[1].Split(',')[1];
            GlobalData.gbtopScInfovm.ControlState = statesArry[2].Split(',')[1];
            GlobalData.gbtopScInfovm.ScState = statesArry[3].Split(',')[1];
            GlobalData.gbtopScInfovm.ScMode = statesArry[4].Split(',')[1];
            GlobalData.gbtopScInfovm.MachineState = statesArry[5].Split(',')[1];
            //foreach (string strOneState in statesArry)
            //{
            //    string[] strStateValueArray = strOneState.Split(',');
            //   // ucTopStateMonitor.SetValue(strStateValueArray[0], strStateValueArray[1]);
            //}
        }

        private async void M_timerTopState_Tick(object? sender, EventArgs e)
        {
           await GetAllTopState();
            GetNewestAlarm();

            GetCountInfo();

        }

    private void Timer_Tick(object sender, EventArgs e)
        {
            // 更新TextBlock的内容为当前时间
            TimeTextBlock.Text = DateTime.Now.ToString("yyyy-MM-dd\nHH:mm:ss");
        }
        private async void GetNewestAlarm()
        {
            try
            {
                int nNewestAlarmCode = 0;
                var alarmList =await GlobalData.dbHelper.tpAlarmdb.GetAllTpAlarmAsync();
                if (alarmList.Count == 0)
                {
                    GlobalData.gbtopScInfovm.Alarm = "Normal";
                    //m_bAlarmDialogClose = true;
                    //m_bIsClickClose = false;
                }
                else
                {
                    GlobalData.gbtopScInfovm.Alarm = "Error";
                    TpAlarm newestAlarm = alarmList.OrderByDescending(x => x.Last_Time).First();
                    string strAlarmText = newestAlarm.Unit + ": " + newestAlarm.Code.ToString();
                    GlobalData.gbtopScInfovm.Alarm = strAlarmText;
                     nNewestAlarmCode = newestAlarm.Code;


                }
            }
            catch (Exception ex)
            {
                Proj.Log.Logger.Instance.ExceptionLog("GetNewestAlarm: " + ex.Message + ", Stack: " + ex.StackTrace);
               
            }
        }
        public async void GetCountInfo()
        {
            try
            {
               
               
                var cartierlst = await GlobalData.dbHelper.tpCarrierdb.GetAllTpCarrierAsync();
                GlobalData.gbtopScInfovm.Cassette=cartierlst.Count;
            }
            catch (Exception ex) { }
        }
        private void Label_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            FrmTpAlarm frmTpAlarm = new FrmTpAlarm();
            frmTpAlarm.ShowDialog();
        }
    }
}
